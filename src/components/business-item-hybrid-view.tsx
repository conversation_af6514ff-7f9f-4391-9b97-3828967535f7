"use client";

import { Collapsible, CollapsibleContent } from "@/components/ui/collapsible";
import { TooltipProvider } from "@/components/ui/tooltip";
import { businessItemsData } from "@/data/businessItemsData";
import { checkItemDependencies } from "@/lib/dependencyManager";
import {
  BusinessItem,
  BusinessItemDetail,
} from "@/types/BusinessSection.types";
import { AlertCircle, CheckCircle, Clock } from "lucide-react";
import { useState } from "react";
import { BusinessItemTable } from "./business-item-table";

interface BusinessItemHybridViewProps {
  selectedItem: BusinessItem;
  itemDetails: BusinessItemDetail[];
  onBackToItems: () => void;
}

export function BusinessItemHybridView({
  selectedItem,
  itemDetails,
  onBackToItems,
}: BusinessItemHybridViewProps) {
  const [isInfoExpanded, setIsInfoExpanded] = useState(false);

  // Get comprehensive data for the selected item
  const itemData = businessItemsData.find(
    (item) => item.id === selectedItem.id
  );
  const dependencyCheck = selectedItem.dependencies
    ? checkItemDependencies(selectedItem.id)
    : null;

  // Get dependent items (items that depend on this one)

  return (
    <TooltipProvider>
      <div className="space-y-6">
        {/* Header with Show Details button */}

        {/* Collapsible Information Section */}
        <Collapsible open={isInfoExpanded} onOpenChange={setIsInfoExpanded}>
          <CollapsibleContent>
            <div className="mb-6 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg border">
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                {/* Left Column - Main Details */}
                <div className="lg:col-span-2 space-y-4">
                  {/* Description */}
                  {(itemData?.description || selectedItem.description) && (
                    <div>
                      <h4 className="font-medium text-sm text-muted-foreground mb-2">
                        Description
                      </h4>
                      <p className="text-sm leading-relaxed">
                        {itemData?.description || selectedItem.description}
                      </p>
                    </div>
                  )}

                  {/* Question */}
                  {itemData?.question && (
                    <div>
                      <h4 className="font-medium text-sm text-muted-foreground mb-2">
                        Key Question
                      </h4>
                      <p className="text-sm bg-blue-50 dark:bg-blue-900/20 p-3 rounded-lg border-l-4 border-blue-500">
                        {itemData.question.replace(
                          "{PROJECT NAME}",
                          "your project"
                        )}
                      </p>
                    </div>
                  )}

                  {/* Guidance */}
                  {itemData?.guidance && (
                    <div>
                      <h4 className="font-medium text-sm text-muted-foreground mb-2">
                        Guidance
                      </h4>
                      <p className="text-sm text-muted-foreground leading-relaxed">
                        {itemData.guidance}
                      </p>
                    </div>
                  )}

                  {/* Note about sample data */}
                  <div>
                    <h4 className="font-medium text-sm text-muted-foreground mb-2">
                      Sample Data
                    </h4>
                    <p className="text-xs text-muted-foreground">
                      Sample responses have been loaded into the table below.
                      You can edit them or add your own entries.
                    </p>
                  </div>
                </div>

                {/* Right Column - Metadata */}
                <div className="space-y-4">
                  {/* Status Metrics */}
                  <div>
                    <h4 className="font-medium text-sm text-muted-foreground mb-2">
                      Progress Metrics
                    </h4>
                    <div className="grid grid-cols-3 gap-2 text-center">
                      <div className="p-2 bg-yellow-50 dark:bg-yellow-900/20 rounded">
                        <div className="text-sm font-bold text-yellow-600">
                          {selectedItem.ideas}
                        </div>
                        <div className="text-xs text-muted-foreground">
                          Ideas
                        </div>
                      </div>
                      <div className="p-2 bg-blue-50 dark:bg-blue-900/20 rounded">
                        <div className="text-sm font-bold text-blue-600">
                          {selectedItem.actions}
                        </div>
                        <div className="text-xs text-muted-foreground">
                          Actions
                        </div>
                      </div>
                      <div className="p-2 bg-green-50 dark:bg-green-900/20 rounded">
                        <div className="text-sm font-bold text-green-600">
                          {selectedItem.results}
                        </div>
                        <div className="text-xs text-muted-foreground">
                          Results
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Dependencies */}
                  {selectedItem.dependencies &&
                    selectedItem.dependencies.length > 0 && (
                      <div>
                        <h4 className="font-medium text-sm text-muted-foreground mb-2">
                          Dependencies
                        </h4>
                        {dependencyCheck && !dependencyCheck.isValid && (
                          <div className="flex items-center gap-2 p-2 bg-red-50 dark:bg-red-900/20 rounded mb-2">
                            <AlertCircle className="h-3 w-3 text-red-600" />
                            <span className="text-xs text-red-600">
                              Not satisfied
                            </span>
                          </div>
                        )}
                        <div className="space-y-1">
                          {selectedItem.dependencies
                            .slice(0, 3)
                            .map((depId, index) => {
                              const depItem = businessItemsData.find(
                                (item) => item.id === depId
                              );
                              const isCompleted =
                                depItem?.status === "confirmed";

                              return (
                                <div
                                  key={index}
                                  className="flex items-center gap-2 text-xs"
                                >
                                  {isCompleted ? (
                                    <CheckCircle className="h-3 w-3 text-green-600" />
                                  ) : (
                                    <Clock className="h-3 w-3 text-gray-400" />
                                  )}
                                  <span
                                    className={
                                      isCompleted
                                        ? "text-green-600"
                                        : "text-gray-600"
                                    }
                                  >
                                    {depItem?.title || depId}
                                  </span>
                                </div>
                              );
                            })}
                          {selectedItem.dependencies.length > 3 && (
                            <p className="text-xs text-muted-foreground">
                              +{selectedItem.dependencies.length - 3} more
                            </p>
                          )}
                        </div>
                      </div>
                    )}

                  {/* Metadata */}
                  <div>
                    <h4 className="font-medium text-sm text-muted-foreground mb-2">
                      Metadata
                    </h4>
                    <div className="space-y-1 text-xs">
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Category:</span>
                        <span>{selectedItem.category}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">
                          Input Type:
                        </span>
                        <span>{selectedItem.inputType}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Order:</span>
                        <span>{selectedItem.order}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </CollapsibleContent>
        </Collapsible>

        {/* Business Item Table */}
        <div>
          <h3 className="text-lg font-semibold mb-4">Action Items & Results</h3>
          <BusinessItemTable
            itemDetails={itemDetails}
            selectedBusinessItem={selectedItem}
            onBackToItems={onBackToItems}
          />
        </div>
      </div>
    </TooltipProvider>
  );
}
