"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/projects/[id]/page",{

/***/ "(app-pages-browser)/./src/components/business-item-hybrid-view.tsx":
/*!******************************************************!*\
  !*** ./src/components/business-item-hybrid-view.tsx ***!
  \******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BusinessItemHybridView: () => (/* binding */ BusinessItemHybridView)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_collapsible__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/collapsible */ \"(app-pages-browser)/./src/components/ui/collapsible.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./src/components/ui/separator.tsx\");\n/* harmony import */ var _components_ui_tooltip__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/tooltip */ \"(app-pages-browser)/./src/components/ui/tooltip.tsx\");\n/* harmony import */ var _data_businessItemsData__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/data/businessItemsData */ \"(app-pages-browser)/./src/data/businessItemsData.ts\");\n/* harmony import */ var _lib_dependencyManager__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/dependencyManager */ \"(app-pages-browser)/./src/lib/dependencyManager.ts\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_ChevronDown_ChevronUp_Clock_HelpCircle_Lightbulb_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,ChevronDown,ChevronUp,Clock,HelpCircle,Lightbulb,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_ChevronDown_ChevronUp_Clock_HelpCircle_Lightbulb_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,ChevronDown,ChevronUp,Clock,HelpCircle,Lightbulb,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_ChevronDown_ChevronUp_Clock_HelpCircle_Lightbulb_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,ChevronDown,ChevronUp,Clock,HelpCircle,Lightbulb,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/lightbulb.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_ChevronDown_ChevronUp_Clock_HelpCircle_Lightbulb_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,ChevronDown,ChevronUp,Clock,HelpCircle,Lightbulb,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_ChevronDown_ChevronUp_Clock_HelpCircle_Lightbulb_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,ChevronDown,ChevronUp,Clock,HelpCircle,Lightbulb,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-question-mark.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_ChevronDown_ChevronUp_Clock_HelpCircle_Lightbulb_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,ChevronDown,ChevronUp,Clock,HelpCircle,Lightbulb,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-up.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_ChevronDown_ChevronUp_Clock_HelpCircle_Lightbulb_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,ChevronDown,ChevronUp,Clock,HelpCircle,Lightbulb,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_ChevronDown_ChevronUp_Clock_HelpCircle_Lightbulb_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,ChevronDown,ChevronUp,Clock,HelpCircle,Lightbulb,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var _business_item_table__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./business-item-table */ \"(app-pages-browser)/./src/components/business-item-table.tsx\");\n/* __next_internal_client_entry_do_not_use__ BusinessItemHybridView auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction BusinessItemHybridView(param) {\n    let { selectedItem, itemDetails, onBackToItems, projectName = \"your project\" } = param;\n    _s();\n    const [isInfoExpanded, setIsInfoExpanded] = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(false);\n    // Get comprehensive data for the selected item\n    const itemData = _data_businessItemsData__WEBPACK_IMPORTED_MODULE_5__.businessItemsData.find((item)=>item.id === selectedItem.id);\n    const dependencyCheck = selectedItem.dependencies ? (0,_lib_dependencyManager__WEBPACK_IMPORTED_MODULE_6__.checkItemDependencies)(selectedItem.id) : null;\n    // Get dependent items (items that depend on this one)\n    const getStatusColor = (status)=>{\n        switch(status){\n            case \"confirmed\":\n                return \"text-green-600 dark:text-green-400 bg-green-50 dark:bg-green-900/20\";\n            case \"action\":\n                return \"text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20\";\n            case \"idea\":\n                return \"text-yellow-600 dark:text-yellow-400 bg-yellow-50 dark:bg-yellow-900/20\";\n            case \"unproven\":\n                return \"text-gray-600 dark:text-gray-400 bg-gray-50 dark:bg-gray-800\";\n            default:\n                return \"text-gray-600 dark:text-gray-400 bg-gray-50 dark:bg-gray-800\";\n        }\n    };\n    const getStatusIcon = (status)=>{\n        switch(status){\n            case \"confirmed\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_ChevronDown_ChevronUp_Clock_HelpCircle_Lightbulb_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                    lineNumber: 78,\n                    columnNumber: 16\n                }, this);\n            case \"action\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_ChevronDown_ChevronUp_Clock_HelpCircle_Lightbulb_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                    lineNumber: 80,\n                    columnNumber: 16\n                }, this);\n            case \"idea\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_ChevronDown_ChevronUp_Clock_HelpCircle_Lightbulb_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                    lineNumber: 82,\n                    columnNumber: 16\n                }, this);\n            case \"unproven\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_ChevronDown_ChevronUp_Clock_HelpCircle_Lightbulb_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                    lineNumber: 84,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_ChevronDown_ChevronUp_Clock_HelpCircle_Lightbulb_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                    lineNumber: 86,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_4__.TooltipProvider, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1\",\n                                children: [\n                                    (itemData === null || itemData === void 0 ? void 0 : itemData.question) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-2xl font-bold text-blue-600 dark:text-blue-400\",\n                                        children: itemData.question.replace(\"{PROJECT NAME}\", projectName)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                        lineNumber: 99,\n                                        columnNumber: 17\n                                    }, this),\n                                    ((itemData === null || itemData === void 0 ? void 0 : itemData.description) || selectedItem.description) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-muted-foreground leading-relaxed mt-2\",\n                                        children: (itemData === null || itemData === void 0 ? void 0 : itemData.description) || selectedItem.description\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                        lineNumber: 106,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                lineNumber: 96,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_4__.Tooltip, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_4__.TooltipTrigger, {\n                                                asChild: true,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                                    variant: \"ghost\",\n                                                    size: \"sm\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_ChevronDown_ChevronUp_Clock_HelpCircle_Lightbulb_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                        lineNumber: 116,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                    lineNumber: 115,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                lineNumber: 114,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_4__.TooltipContent, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: \"Click the expand button to see detailed guidance and examples\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                    lineNumber: 120,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                lineNumber: 119,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                        lineNumber: 113,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_2__.Collapsible, {\n                                        open: isInfoExpanded,\n                                        onOpenChange: setIsInfoExpanded,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_2__.CollapsibleTrigger, {\n                                            asChild: true,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                                variant: \"outline\",\n                                                size: \"sm\",\n                                                className: \"flex items-center gap-2\",\n                                                children: isInfoExpanded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_ChevronDown_ChevronUp_Clock_HelpCircle_Lightbulb_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                            lineNumber: 139,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        \"Hide Details\"\n                                                    ]\n                                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_ChevronDown_ChevronUp_Clock_HelpCircle_Lightbulb_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                            lineNumber: 144,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        \"Show Details\"\n                                                    ]\n                                                }, void 0, true)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                lineNumber: 132,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                            lineNumber: 131,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                        lineNumber: 127,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                lineNumber: 112,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                        lineNumber: 95,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                    lineNumber: 94,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_2__.Collapsible, {\n                    open: isInfoExpanded,\n                    onOpenChange: setIsInfoExpanded,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_2__.CollapsibleContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-6 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg border\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"lg:col-span-2 space-y-4\",\n                                        children: [\n                                            ((itemData === null || itemData === void 0 ? void 0 : itemData.description) || selectedItem.description) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium text-sm text-muted-foreground mb-2\",\n                                                        children: \"Description\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                        lineNumber: 165,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm leading-relaxed\",\n                                                        children: (itemData === null || itemData === void 0 ? void 0 : itemData.description) || selectedItem.description\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                        lineNumber: 168,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                lineNumber: 164,\n                                                columnNumber: 21\n                                            }, this),\n                                            (itemData === null || itemData === void 0 ? void 0 : itemData.question) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium text-sm text-muted-foreground mb-2\",\n                                                        children: \"Key Question\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                        lineNumber: 177,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm bg-blue-50 dark:bg-blue-900/20 p-3 rounded-lg border-l-4 border-blue-500\",\n                                                        children: itemData.question.replace(\"{PROJECT NAME}\", \"your project\")\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                        lineNumber: 180,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                lineNumber: 176,\n                                                columnNumber: 21\n                                            }, this),\n                                            (itemData === null || itemData === void 0 ? void 0 : itemData.guidance) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium text-sm text-muted-foreground mb-2\",\n                                                        children: \"Guidance\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                        lineNumber: 192,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-muted-foreground leading-relaxed\",\n                                                        children: itemData.guidance\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                        lineNumber: 195,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                lineNumber: 191,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium text-sm text-muted-foreground mb-2\",\n                                                        children: \"Sample Data\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                        lineNumber: 203,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-muted-foreground\",\n                                                        children: \"Sample responses have been loaded into the table below. You can edit them or add your own entries.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                        lineNumber: 206,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                lineNumber: 202,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                        lineNumber: 161,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium text-sm text-muted-foreground mb-2\",\n                                                        children: \"Progress Metrics\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                        lineNumber: 217,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-3 gap-2 text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-2 bg-yellow-50 dark:bg-yellow-900/20 rounded\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm font-bold text-yellow-600\",\n                                                                        children: selectedItem.ideas\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                                        lineNumber: 222,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-xs text-muted-foreground\",\n                                                                        children: \"Ideas\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                                        lineNumber: 225,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                                lineNumber: 221,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-2 bg-blue-50 dark:bg-blue-900/20 rounded\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm font-bold text-blue-600\",\n                                                                        children: selectedItem.actions\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                                        lineNumber: 230,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-xs text-muted-foreground\",\n                                                                        children: \"Actions\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                                        lineNumber: 233,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                                lineNumber: 229,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-2 bg-green-50 dark:bg-green-900/20 rounded\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm font-bold text-green-600\",\n                                                                        children: selectedItem.results\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                                        lineNumber: 238,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-xs text-muted-foreground\",\n                                                                        children: \"Results\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                                        lineNumber: 241,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                                lineNumber: 237,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                        lineNumber: 220,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                lineNumber: 216,\n                                                columnNumber: 19\n                                            }, this),\n                                            selectedItem.dependencies && selectedItem.dependencies.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium text-sm text-muted-foreground mb-2\",\n                                                        children: \"Dependencies\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                        lineNumber: 252,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    dependencyCheck && !dependencyCheck.isValid && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2 p-2 bg-red-50 dark:bg-red-900/20 rounded mb-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_ChevronDown_ChevronUp_Clock_HelpCircle_Lightbulb_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                className: \"h-3 w-3 text-red-600\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                                lineNumber: 257,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs text-red-600\",\n                                                                children: \"Not satisfied\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                                lineNumber: 258,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                        lineNumber: 256,\n                                                        columnNumber: 27\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-1\",\n                                                        children: [\n                                                            selectedItem.dependencies.slice(0, 3).map((depId, index)=>{\n                                                                const depItem = _data_businessItemsData__WEBPACK_IMPORTED_MODULE_5__.businessItemsData.find((item)=>item.id === depId);\n                                                                const isCompleted = (depItem === null || depItem === void 0 ? void 0 : depItem.status) === \"confirmed\";\n                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2 text-xs\",\n                                                                    children: [\n                                                                        isCompleted ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_ChevronDown_ChevronUp_Clock_HelpCircle_Lightbulb_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                            className: \"h-3 w-3 text-green-600\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                                            lineNumber: 279,\n                                                                            columnNumber: 37\n                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_ChevronDown_ChevronUp_Clock_HelpCircle_Lightbulb_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                            className: \"h-3 w-3 text-gray-400\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                                            lineNumber: 281,\n                                                                            columnNumber: 37\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: isCompleted ? \"text-green-600\" : \"text-gray-600\",\n                                                                            children: (depItem === null || depItem === void 0 ? void 0 : depItem.title) || depId\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                                            lineNumber: 283,\n                                                                            columnNumber: 35\n                                                                        }, this)\n                                                                    ]\n                                                                }, index, true, {\n                                                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                                    lineNumber: 274,\n                                                                    columnNumber: 33\n                                                                }, this);\n                                                            }),\n                                                            selectedItem.dependencies.length > 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-muted-foreground\",\n                                                                children: [\n                                                                    \"+\",\n                                                                    selectedItem.dependencies.length - 3,\n                                                                    \" more\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                                lineNumber: 296,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                        lineNumber: 263,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                lineNumber: 251,\n                                                columnNumber: 23\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium text-sm text-muted-foreground mb-2\",\n                                                        children: \"Metadata\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                        lineNumber: 306,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-1 text-xs\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-muted-foreground\",\n                                                                        children: \"Category:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                                        lineNumber: 311,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: selectedItem.category\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                                        lineNumber: 312,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                                lineNumber: 310,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-muted-foreground\",\n                                                                        children: \"Input Type:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                                        lineNumber: 315,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: selectedItem.inputType\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                                        lineNumber: 318,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                                lineNumber: 314,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-muted-foreground\",\n                                                                        children: \"Order:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                                        lineNumber: 321,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: selectedItem.order\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                                        lineNumber: 322,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                                lineNumber: 320,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                        lineNumber: 309,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                lineNumber: 305,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                        lineNumber: 214,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                lineNumber: 159,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                            lineNumber: 158,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                        lineNumber: 157,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                    lineNumber: 156,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_3__.Separator, {}, void 0, false, {\n                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                    lineNumber: 333,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold mb-4\",\n                            children: \"Action Items & Results\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                            lineNumber: 337,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_business_item_table__WEBPACK_IMPORTED_MODULE_8__.BusinessItemTable, {\n                            itemDetails: itemDetails,\n                            selectedBusinessItem: selectedItem,\n                            onBackToItems: onBackToItems\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                            lineNumber: 338,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                    lineNumber: 336,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n            lineNumber: 92,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n        lineNumber: 91,\n        columnNumber: 5\n    }, this);\n}\n_s(BusinessItemHybridView, \"xhHbMehHQ3yyT7gl75aBEcicsE0=\");\n_c = BusinessItemHybridView;\nvar _c;\n$RefreshReg$(_c, \"BusinessItemHybridView\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/business-item-hybrid-view.tsx\n"));

/***/ })

});