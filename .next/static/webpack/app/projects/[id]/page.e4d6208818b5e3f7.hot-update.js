"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/projects/[id]/page",{

/***/ "(app-pages-browser)/./src/components/project/ProjectMainContent.tsx":
/*!*******************************************************!*\
  !*** ./src/components/project/ProjectMainContent.tsx ***!
  \*******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ProjectMainContent: () => (/* binding */ ProjectMainContent)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _lib_constants__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/constants */ \"(app-pages-browser)/./src/lib/constants.ts\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _business_item_hybrid_view__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../business-item-hybrid-view */ \"(app-pages-browser)/./src/components/business-item-hybrid-view.tsx\");\n/* harmony import */ var _business_sections_BusinessSectionsGridEnhanced__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../business-sections/BusinessSectionsGridEnhanced */ \"(app-pages-browser)/./src/components/business-sections/BusinessSectionsGridEnhanced.tsx\");\n/* harmony import */ var _ContentSections__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./ContentSections */ \"(app-pages-browser)/./src/components/project/ContentSections.tsx\");\n/* __next_internal_client_entry_do_not_use__ ProjectMainContent auto */ \n\n\n\n\n\n\nfunction ProjectMainContent(param) {\n    let { activeContent, setActiveContent, mockDraftItems, mockFileItems, selectedItem, itemDetails, sections, isLoading, error, onBusinessItemClick, onBackToItems } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex-1 min-w-0 min-h-0 w-full max-w-full p-4 pb-20 overflow-y-auto bg-gray-50 dark:bg-gray-900 relative transition-all duration-300 ease-in-out main-content-bottom-fade\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 project-main-content pointer-events-none\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectMainContent.tsx\",\n                lineNumber: 40,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-30\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ContentSections__WEBPACK_IMPORTED_MODULE_5__.ContentSections, {\n                        activeContent: activeContent,\n                        setActiveContent: setActiveContent,\n                        mockDraftItems: mockDraftItems,\n                        mockFileItems: mockFileItems\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectMainContent.tsx\",\n                        lineNumber: 45,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.AnimatePresence, {\n                        mode: \"wait\",\n                        children: [\n                            !activeContent && !selectedItem && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                exit: {\n                                    opacity: 0,\n                                    y: -20\n                                },\n                                transition: {\n                                    duration: 0.3,\n                                    ease: \"easeInOut\"\n                                },\n                                children: [\n                                    isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center h-64\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"animate-spin rounded-full \".concat(_lib_constants__WEBPACK_IMPORTED_MODULE_2__.ICON_SIZES.lg, \" border-b-2 border-gray-900 mx-auto mb-4\")\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectMainContent.tsx\",\n                                                    lineNumber: 66,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600\",\n                                                    children: \"Loading business sections...\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectMainContent.tsx\",\n                                                    lineNumber: 69,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectMainContent.tsx\",\n                                            lineNumber: 65,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectMainContent.tsx\",\n                                        lineNumber: 64,\n                                        columnNumber: 17\n                                    }, this),\n                                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center h-64\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-red-600 mb-4\",\n                                                    children: error\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectMainContent.tsx\",\n                                                    lineNumber: 80,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                                    onClick: ()=>window.location.reload(),\n                                                    children: \"Try Again\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectMainContent.tsx\",\n                                                    lineNumber: 81,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectMainContent.tsx\",\n                                            lineNumber: 79,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectMainContent.tsx\",\n                                        lineNumber: 78,\n                                        columnNumber: 17\n                                    }, this),\n                                    !isLoading && !error && sections.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_business_sections_BusinessSectionsGridEnhanced__WEBPACK_IMPORTED_MODULE_4__.BusinessSectionsGridEnhanced, {\n                                        sections: sections,\n                                        onItemClick: onBusinessItemClick\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectMainContent.tsx\",\n                                        lineNumber: 90,\n                                        columnNumber: 17\n                                    }, this),\n                                    !isLoading && !error && sections.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center h-64\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600 mb-4\",\n                                                    children: \"No business sections found\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectMainContent.tsx\",\n                                                    lineNumber: 100,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                                    onClick: ()=>window.location.reload(),\n                                                    children: \"Reload\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectMainContent.tsx\",\n                                                    lineNumber: 103,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectMainContent.tsx\",\n                                            lineNumber: 99,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectMainContent.tsx\",\n                                        lineNumber: 98,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, \"business-sections\", true, {\n                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectMainContent.tsx\",\n                                lineNumber: 55,\n                                columnNumber: 13\n                            }, this),\n                            !activeContent && selectedItem && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    x: 30\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    x: 0\n                                },\n                                exit: {\n                                    opacity: 0,\n                                    x: -30\n                                },\n                                transition: {\n                                    duration: 0.3,\n                                    ease: \"easeInOut\"\n                                },\n                                className: \"-m-4 -p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_business_item_hybrid_view__WEBPACK_IMPORTED_MODULE_3__.BusinessItemHybridView, {\n                                    selectedItem: selectedItem,\n                                    itemDetails: itemDetails,\n                                    onBackToItems: onBackToItems\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectMainContent.tsx\",\n                                    lineNumber: 122,\n                                    columnNumber: 15\n                                }, this)\n                            }, \"business-detail\", false, {\n                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectMainContent.tsx\",\n                                lineNumber: 114,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectMainContent.tsx\",\n                        lineNumber: 52,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectMainContent.tsx\",\n                lineNumber: 43,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectMainContent.tsx\",\n        lineNumber: 38,\n        columnNumber: 5\n    }, this);\n}\n_c = ProjectMainContent;\nvar _c;\n$RefreshReg$(_c, \"ProjectMainContent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/project/ProjectMainContent.tsx\n"));

/***/ })

});