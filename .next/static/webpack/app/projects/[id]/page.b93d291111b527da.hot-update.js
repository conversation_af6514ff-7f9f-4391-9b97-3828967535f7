"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/projects/[id]/page",{

/***/ "(app-pages-browser)/./src/app/projects/[id]/page.tsx":
/*!****************************************!*\
  !*** ./src/app/projects/[id]/page.tsx ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProjectDetailPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_project__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/project */ \"(app-pages-browser)/./src/components/project/index.ts\");\n/* harmony import */ var _components_project_sidebar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/project-sidebar */ \"(app-pages-browser)/./src/components/project-sidebar.tsx\");\n/* harmony import */ var _components_ui_sidebar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/sidebar */ \"(app-pages-browser)/./src/components/ui/sidebar.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _hooks_useResizable__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../../hooks/useResizable */ \"(app-pages-browser)/./src/hooks/useResizable.ts\");\n/* harmony import */ var _stores_businessItemStore__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../../stores/businessItemStore */ \"(app-pages-browser)/./src/stores/businessItemStore.ts\");\n/* harmony import */ var _stores_businessSectionStore__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../../../stores/businessSectionStore */ \"(app-pages-browser)/./src/stores/businessSectionStore.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n// Mock data\nconst mockDraftItems = [\n    {\n        id: 1,\n        title: \"Project proposal\",\n        status: \"draft\",\n        lastModified: \"2 hours ago\"\n    },\n    {\n        id: 2,\n        title: \"Design brief\",\n        status: \"draft\",\n        lastModified: \"1 day ago\"\n    },\n    {\n        id: 3,\n        title: \"Technical specs\",\n        status: \"draft\",\n        lastModified: \"3 days ago\"\n    }\n];\n// Mock file items data\nconst mockFileItems = [\n    {\n        id: 1,\n        title: \"logo.svg\",\n        type: \"image\",\n        size: \"24KB\"\n    },\n    {\n        id: 2,\n        title: \"wireframes.fig\",\n        type: \"design\",\n        size: \"1.2MB\"\n    },\n    {\n        id: 3,\n        title: \"requirements.pdf\",\n        type: \"document\",\n        size: \"156KB\"\n    },\n    {\n        id: 4,\n        title: \"styleguide.pdf\",\n        type: \"document\",\n        size: \"2.1MB\"\n    }\n];\nfunction ProjectDetailPage() {\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.useParams)();\n    const projectId = params.id;\n    const [activeContent, setActiveContent] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(null);\n    const [chatWidth, setChatWidth] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(\"45%\");\n    const [isChatCollapsed, setIsChatCollapsed] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    const [sidebarWidth, setSidebarWidth] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(\"45vw\");\n    // Sync sidebar width with chat width\n    const handleChatWidthChange = (width)=>{\n        setChatWidth(width);\n        setSidebarWidth(width === \"45%\" ? \"45vw\" : \"45vw\");\n    };\n    const { sections, isLoading, error, setSections, setLoading, setError } = (0,_stores_businessSectionStore__WEBPACK_IMPORTED_MODULE_8__.useBusinessSectionStore)();\n    const { selectedItem, itemDetails, setSelectedItem, setItemDetails } = (0,_stores_businessItemStore__WEBPACK_IMPORTED_MODULE_7__.useBusinessItemStore)();\n    // Dynamic sidebar width based on selected item\n    const currentSidebarWidth = selectedItem ? \"30vw\" : sidebarWidth;\n    // Reset to default view on page load/refresh\n    (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)({\n        \"ProjectDetailPage.useEffect\": ()=>{\n            setSelectedItem(null);\n            setItemDetails([]);\n        }\n    }[\"ProjectDetailPage.useEffect\"], [\n        setSelectedItem,\n        setItemDetails\n    ]);\n    // Load business sections on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)({\n        \"ProjectDetailPage.useEffect\": ()=>{\n            const loadBusinessSections = {\n                \"ProjectDetailPage.useEffect.loadBusinessSections\": async ()=>{\n                    try {\n                        setLoading(true);\n                        setError(null);\n                        const data = await fetchBusinessSections(projectId);\n                        setSections(data);\n                    } catch (err) {\n                        setError(\"Failed to load business sections\");\n                        console.error(\"Error loading business sections:\", err);\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"ProjectDetailPage.useEffect.loadBusinessSections\"];\n            loadBusinessSections();\n        }\n    }[\"ProjectDetailPage.useEffect\"], [\n        setSections,\n        setLoading,\n        setError\n    ]);\n    // Handle business item selection - show detail view\n    const handleBusinessItemClick = (item)=>{\n        setSelectedItem(item);\n        // Load mock item details for the selected business item\n        const loadItemDetails = async ()=>{\n            try {\n                const { fetchItemDetails } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @/stores/businessItemStore */ \"(app-pages-browser)/./src/stores/businessItemStore.ts\"));\n                const details = await fetchItemDetails(item.id);\n                setItemDetails(details);\n            } catch (err) {\n                console.error(\"Error loading item details:\", err);\n                setItemDetails([]);\n            }\n        };\n        loadItemDetails();\n    };\n    // Handle back to items\n    const handleBackToItems = ()=>{\n        setSelectedItem(null);\n    };\n    // Resize functionality\n    const resizable = (0,_hooks_useResizable__WEBPACK_IMPORTED_MODULE_6__.useResizable)({\n        initialWidth: sidebarWidth,\n        minWidthPercent: 10,\n        maxWidthPercent: 70,\n        onWidthChange: {\n            \"ProjectDetailPage.useResizable[resizable]\": (width)=>{\n                // Convert percentage to viewport width\n                const widthPercent = parseFloat(width.replace(\"%\", \"\"));\n                const vwWidth = \"\".concat(widthPercent, \"vw\");\n                setSidebarWidth(vwWidth);\n                // Update chatWidth to match sidebarWidth for consistency\n                if (widthPercent <= 70) {\n                    setChatWidth(\"45%\");\n                } else {\n                    setChatWidth(\"45%\");\n                }\n            }\n        }[\"ProjectDetailPage.useResizable[resizable]\"],\n        onCollapse: {\n            \"ProjectDetailPage.useResizable[resizable]\": ()=>{\n                setIsChatCollapsed(true);\n            }\n        }[\"ProjectDetailPage.useResizable[resizable]\"]\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-screen overflow-hidden\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_3__.SidebarProvider, {\n            defaultOpen: true,\n            style: {\n                \"--sidebar-width\": currentSidebarWidth,\n                \"--sidebar-width-mobile\": \"18rem\",\n                \"--sidebar-width-icon\": \"5rem\",\n                transition: \"all 0.3s ease-in-out\"\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_project_sidebar__WEBPACK_IMPORTED_MODULE_2__.ProjectSidebar, {\n                    projectId: projectId,\n                    chatWidth: chatWidth,\n                    setChatWidth: handleChatWidthChange,\n                    isChatCollapsed: isChatCollapsed,\n                    setIsChatCollapsed: setIsChatCollapsed,\n                    selectedBusinessItem: selectedItem,\n                    showDescription: !!selectedItem,\n                    onBackToProject: handleBackToItems,\n                    resizeHandle: {\n                        onMouseDown: resizable.handleMouseDown,\n                        isDragging: resizable.isDragging\n                    }\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/app/projects/[id]/page.tsx\",\n                    lineNumber: 146,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_3__.SidebarInset, {\n                    className: \"flex-1 flex flex-col h-screen overflow-hidden\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_project__WEBPACK_IMPORTED_MODULE_1__.ProjectHeader, {\n                            activeContent: activeContent,\n                            setActiveContent: setActiveContent,\n                            selectedBusinessItem: selectedItem,\n                            onBackToItems: handleBackToItems\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/app/projects/[id]/page.tsx\",\n                            lineNumber: 161,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_project__WEBPACK_IMPORTED_MODULE_1__.ProjectMainContent, {\n                            activeContent: activeContent,\n                            setActiveContent: setActiveContent,\n                            mockDraftItems: mockDraftItems,\n                            mockFileItems: mockFileItems,\n                            selectedItem: selectedItem,\n                            itemDetails: itemDetails,\n                            sections: sections,\n                            isLoading: isLoading,\n                            error: error,\n                            onBusinessItemClick: handleBusinessItemClick,\n                            onBackToItems: handleBackToItems\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/app/projects/[id]/page.tsx\",\n                            lineNumber: 168,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"fixed bottom-0 h-16 bg-gradient-to-t from-gray-50 via-gray-50/80 to-transparent dark:from-gray-900 dark:via-gray-900/80 dark:to-transparent pointer-events-none z-10\",\n                            style: {\n                                left: \"var(--sidebar-width, 45vw)\",\n                                right: \"0\"\n                            }\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/app/projects/[id]/page.tsx\",\n                            lineNumber: 183,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"fixed bottom-0 h-8 backdrop-blur-sm pointer-events-none z-10\",\n                            style: {\n                                left: \"var(--sidebar-width, 45vw)\",\n                                right: \"0\"\n                            }\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/app/projects/[id]/page.tsx\",\n                            lineNumber: 190,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/app/projects/[id]/page.tsx\",\n                    lineNumber: 160,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Data/new era/siift-next/src/app/projects/[id]/page.tsx\",\n            lineNumber: 135,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Data/new era/siift-next/src/app/projects/[id]/page.tsx\",\n        lineNumber: 134,\n        columnNumber: 5\n    }, this);\n}\n_s(ProjectDetailPage, \"ZRFTZ7FQRtw8sqsm2d34g9VD/0U=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_4__.useParams,\n        _stores_businessSectionStore__WEBPACK_IMPORTED_MODULE_8__.useBusinessSectionStore,\n        _stores_businessItemStore__WEBPACK_IMPORTED_MODULE_7__.useBusinessItemStore,\n        _hooks_useResizable__WEBPACK_IMPORTED_MODULE_6__.useResizable\n    ];\n});\n_c = ProjectDetailPage;\nvar _c;\n$RefreshReg$(_c, \"ProjectDetailPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/projects/[id]/page.tsx\n"));

/***/ })

});