// Dependency management system for business items
import { businessItemsData } from "@/data/businessItemsData";
import type { BusinessItemData } from "@/types/BusinessItemData.types";

export interface DependencyStatus {
  itemId: string;
  canProgress: boolean;
  blockedBy: string[];
  dependents: string[];
  completionPercentage: number;
}

export interface DependencyValidationResult {
  isValid: boolean;
  missingDependencies: string[];
  readyToProgress: boolean;
  message: string;
}

export class DependencyManager {
  private items: BusinessItemData[];
  
  constructor(items: BusinessItemData[] = businessItemsData) {
    this.items = items;
  }

  // Check if an item's dependencies are satisfied
  checkDependencies(itemId: string): DependencyValidationResult {
    const item = this.getItem(itemId);
    
    if (!item) {
      return {
        isValid: false,
        missingDependencies: [],
        readyToProgress: false,
        message: "Item not found"
      };
    }

    if (!item.dependencies || item.dependencies.length === 0) {
      return {
        isValid: true,
        missingDependencies: [],
        readyToProgress: true,
        message: "No dependencies required"
      };
    }

    const missingDependencies = item.dependencies.filter(depId => {
      const depItem = this.getItem(depId);
      return !depItem || depItem.status !== "confirmed";
    });

    const isValid = missingDependencies.length === 0;
    
    return {
      isValid,
      missingDependencies,
      readyToProgress: isValid && item.status !== "confirmed",
      message: isValid 
        ? "All dependencies satisfied" 
        : `Missing: ${missingDependencies.map(id => this.getItem(id)?.title || id).join(", ")}`
    };
  }

  // Get all items that depend on a specific item
  getDependents(itemId: string): string[] {
    return this.items
      .filter(item => item.dependencies?.includes(itemId))
      .map(item => item.id);
  }

  // Get dependency status for an item
  getDependencyStatus(itemId: string): DependencyStatus {
    const item = this.getItem(itemId);
    const validation = this.checkDependencies(itemId);
    const dependents = this.getDependents(itemId);
    
    if (!item) {
      return {
        itemId,
        canProgress: false,
        blockedBy: [],
        dependents: [],
        completionPercentage: 0
      };
    }

    const completionPercentage = item.dependencies?.length 
      ? ((item.dependencies.length - validation.missingDependencies.length) / item.dependencies.length) * 100
      : 100;

    return {
      itemId,
      canProgress: validation.readyToProgress,
      blockedBy: validation.missingDependencies,
      dependents,
      completionPercentage
    };
  }

  // Get all items ready to work on (dependencies satisfied, not completed)
  getReadyItems(): string[] {
    return this.items
      .filter(item => {
        const validation = this.checkDependencies(item.id);
        return validation.readyToProgress;
      })
      .map(item => item.id);
  }

  // Get items blocked by dependencies
  getBlockedItems(): string[] {
    return this.items
      .filter(item => {
        const validation = this.checkDependencies(item.id);
        return !validation.isValid && item.status !== "confirmed";
      })
      .map(item => item.id);
  }

  // Get dependency chain for an item (all items it depends on, recursively)
  getDependencyChain(itemId: string, visited: Set<string> = new Set()): string[] {
    if (visited.has(itemId)) return []; // Prevent circular dependencies
    
    const item = this.getItem(itemId);
    if (!item || !item.dependencies) return [];

    visited.add(itemId);
    const chain: string[] = [];

    for (const depId of item.dependencies) {
      chain.push(depId);
      const subChain = this.getDependencyChain(depId, visited);
      chain.push(...subChain);
    }

    return [...new Set(chain)]; // Remove duplicates
  }

  // Get impact chain for an item (all items that depend on it, recursively)
  getImpactChain(itemId: string, visited: Set<string> = new Set()): string[] {
    if (visited.has(itemId)) return []; // Prevent circular dependencies
    
    visited.add(itemId);
    const dependents = this.getDependents(itemId);
    const chain: string[] = [];

    for (const depId of dependents) {
      chain.push(depId);
      const subChain = this.getImpactChain(depId, visited);
      chain.push(...subChain);
    }

    return [...new Set(chain)]; // Remove duplicates
  }

  // Validate if changing an item's status would break dependencies
  validateStatusChange(itemId: string, newStatus: "idea" | "action" | "confirmed" | "unproven"): DependencyValidationResult {
    const item = this.getItem(itemId);
    
    if (!item) {
      return {
        isValid: false,
        missingDependencies: [],
        readyToProgress: false,
        message: "Item not found"
      };
    }

    // If changing from confirmed to something else, check impact
    if (item.status === "confirmed" && newStatus !== "confirmed") {
      const impactChain = this.getImpactChain(itemId);
      const affectedConfirmed = impactChain.filter(id => {
        const affectedItem = this.getItem(id);
        return affectedItem?.status === "confirmed";
      });

      if (affectedConfirmed.length > 0) {
        return {
          isValid: false,
          missingDependencies: [],
          readyToProgress: false,
          message: `Cannot change status: ${affectedConfirmed.length} confirmed items depend on this`
        };
      }
    }

    // If changing to confirmed, check dependencies
    if (newStatus === "confirmed") {
      return this.checkDependencies(itemId);
    }

    return {
      isValid: true,
      missingDependencies: [],
      readyToProgress: true,
      message: "Status change is valid"
    };
  }

  // Get suggested next items to work on
  getSuggestedNextItems(limit: number = 5): string[] {
    const readyItems = this.getReadyItems();
    
    // Sort by priority: items with more dependents should be prioritized
    return readyItems
      .map(itemId => ({
        itemId,
        dependentCount: this.getDependents(itemId).length,
        order: this.getItem(itemId)?.order || 999
      }))
      .sort((a, b) => {
        // First by dependent count (descending), then by order (ascending)
        if (a.dependentCount !== b.dependentCount) {
          return b.dependentCount - a.dependentCount;
        }
        return a.order - b.order;
      })
      .slice(0, limit)
      .map(item => item.itemId);
  }

  // Get completion statistics
  getCompletionStats() {
    const total = this.items.length;
    const confirmed = this.items.filter(item => item.status === "confirmed").length;
    const ready = this.getReadyItems().length;
    const blocked = this.getBlockedItems().length;
    
    return {
      total,
      confirmed,
      ready,
      blocked,
      completionPercentage: (confirmed / total) * 100,
      readyPercentage: (ready / total) * 100,
      blockedPercentage: (blocked / total) * 100
    };
  }

  private getItem(itemId: string): BusinessItemData | undefined {
    return this.items.find(item => item.id === itemId);
  }
}

// Export singleton instance
export const dependencyManager = new DependencyManager();

// Helper functions for easy access
export const checkItemDependencies = (itemId: string) => dependencyManager.checkDependencies(itemId);
export const getReadyItems = () => dependencyManager.getReadyItems();
export const getBlockedItems = () => dependencyManager.getBlockedItems();
export const getSuggestedNextItems = (limit?: number) => dependencyManager.getSuggestedNextItems(limit);
export const getCompletionStats = () => dependencyManager.getCompletionStats();
