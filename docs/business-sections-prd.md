# Business Sections PRD

## Overview
This document outlines the comprehensive business sections system for Siift, including 23 business items across 4 main categories with dependency management, status tracking, and dynamic UI updates.

## Business Categories

### 1. Market (6 items)
- Problem
- Unique Value Proposition  
- Audience
- Alternatives
- Market Size
- Trends

### 2. Solution (4 items)
- Why
- Advantages
- Product or Service
- Tech
- Packages

### 3. Sales & Marketing (7 items)
- Positioning
- Channels
- Messaging
- Brand
- Assets
- Sales Motion
- Metrics

### 4. Company (6 items)
- Risks
- Business Model
- Revenue
- Cost
- Team

## Data Structure

Each business item contains:
- **id**: Unique identifier
- **title**: Display name
- **description**: Detailed explanation
- **question**: Question prompt for user
- **guidance**: Help text for completion
- **category**: Market/Solution/Sales & Marketing/Company
- **order**: Display order within category
- **inputType**: Manual/Auto/Suggest
- **dependencies**: Array of prerequisite item IDs
- **status**: idea/action/confirmed/unproven
- **values**: Array of possible/sample answers
- **actions**: Count of action items
- **ideas**: Count of idea items  
- **results**: Count of confirmed results

## Status System

### Status Types
- **idea**: Initial concept (10% opacity on main cards)
- **action**: Work in progress (20% opacity on main cards)
- **confirmed**: Completed and validated (50% opacity on main cards)
- **unproven**: No data/not started (100% opacity on main cards)

### Dependency Logic
Items can only progress to "confirmed" status if all dependency items have "confirmed" status. This creates a logical flow through the business planning process.

## UI Behavior

### Main Page Cards
Cards display with opacity based on highest status level:
- All unproven: 100% opacity
- Any confirmed: 50% opacity
- Any action: 20% opacity  
- Any idea: 10% opacity

### Business Sections Grid
- Items show dependency status
- Disabled styling for items with unmet dependencies
- Badge counts for actions/ideas/results
- Expandable sections by category

## API Integration
The system is designed to easily replace mock data with real API calls:
- Standardized data structure
- Error handling and loading states
- Optimistic updates for better UX
