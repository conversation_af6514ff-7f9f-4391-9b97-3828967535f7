# Docker Environment Configuration
# This file contains all environment variables needed for Docker deployment

# =============================================================================
# NODE.JS & NEXT.JS CONFIGURATION
# =============================================================================
NODE_ENV=production
NEXT_TELEMETRY_DISABLED=1

# =============================================================================
# APPLICATION CONFIGURATION
# =============================================================================
NEXT_PUBLIC_APP_NAME=Siift
NEXT_PUBLIC_APP_URL=http://localhost:3000

# =============================================================================
# API CONFIGURATION
# =============================================================================
NEXT_PUBLIC_API_URL=/api
NEXT_PUBLIC_ADMIN_API_URL=https://siift-backend.up.railway.app

# =============================================================================
# JWT CONFIGURATION
# =============================================================================
JWT_SECRET=your-super-secret-jwt-key-change-in-production

# =============================================================================
# CLERK AUTHENTICATION CONFIGURATION
# =============================================================================
# Get these from Clerk Dashboard → API Keys
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_test_Zmlyc3QtbW9uaXRvci03NC5jbGVyay5hY2NvdW50cy5kZXYk
CLERK_SECRET_KEY=sk_test_wInjtWLWbkIxVlwL2Pq4A8vsqLzzxKVw6Bow2ai3nF
CLERK_JWT_ISSUER_DOMAIN=https://true-snail-46.clerk.accounts.dev

# Clerk URL Configuration
NEXT_PUBLIC_CLERK_SIGN_IN_URL=/auth/login
NEXT_PUBLIC_CLERK_SIGN_UP_URL=/auth/register
NEXT_PUBLIC_CLERK_AFTER_SIGN_IN_URL=/user-dashboard
NEXT_PUBLIC_CLERK_AFTER_SIGN_UP_URL=/user-dashboard
NEXT_PUBLIC_CLERK_SIGN_IN_FALLBACK_REDIRECT_URL=/user-dashboard
NEXT_PUBLIC_CLERK_SIGN_UP_FALLBACK_REDIRECT_URL=/user-dashboard

# Clerk Webhook Configuration
# Get this from Clerk Dashboard → Webhooks → Add Endpoint
CLERK_WEBHOOK_SECRET=whsec_your_webhook_secret_from_clerk_dashboard

# =============================================================================
# POSTHOG ANALYTICS CONFIGURATION
# =============================================================================
NEXT_PUBLIC_POSTHOG_KEY=phc_2oQSSzXgGcCmTZ0YigOv7yq9RfH49WlVktnZ6HC2vQA
NEXT_PUBLIC_POSTHOG_HOST=https://us.i.posthog.com

# =============================================================================
# DOCKER SPECIFIC CONFIGURATION
# =============================================================================
PORT=3000
HOSTNAME=0.0.0.0

# =============================================================================
# PRODUCTION OVERRIDES (uncomment and modify for production)
# =============================================================================
# NEXT_PUBLIC_APP_URL=https://siift.app
# NEXT_PUBLIC_ADMIN_API_URL=https://api.siift.app
# JWT_SECRET=your-production-jwt-secret-here
# CLERK_SECRET_KEY=your-production-clerk-secret-here
# CLERK_WEBHOOK_SECRET=your-production-webhook-secret-here
