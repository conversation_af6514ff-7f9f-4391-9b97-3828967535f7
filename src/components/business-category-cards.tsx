"use client";

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { 
  Target, 
  Package, 
  Megaphone, 
  Building,
  ArrowRight,
  CheckCircle,
  Clock,
  Lightbulb,
  Zap
} from "lucide-react";
import { 
  getAllCategoryOpacities,
  getCategoryProgress,
  getCategoryStatusIndicator 
} from "@/lib/cardOpacityUtils";

// Category icons mapping
const categoryIcons = {
  "Market": Target,
  "Solution": Package,
  "Sales & Marketing": Megaphone,
  "Company": Building,
};

// Category colors
const categoryColors = {
  "Market": "bg-blue-100 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400",
  "Solution": "bg-green-100 dark:bg-green-900/20 text-green-600 dark:text-green-400",
  "Sales & Marketing": "bg-purple-100 dark:bg-purple-900/20 text-purple-600 dark:text-purple-400",
  "Company": "bg-orange-100 dark:bg-orange-900/20 text-orange-600 dark:text-orange-400",
};

interface BusinessCategoryCardsProps {
  onCategoryClick?: (category: string) => void;
}

export function BusinessCategoryCards({ onCategoryClick }: BusinessCategoryCardsProps) {
  const categoryOpacities = getAllCategoryOpacities();

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      {categoryOpacities.map((categoryInfo) => {
        const Icon = categoryIcons[categoryInfo.category as keyof typeof categoryIcons];
        const progress = getCategoryProgress(categoryInfo.category);
        const statusIndicator = getCategoryStatusIndicator(categoryInfo.category);
        
        return (
          <Card
            key={categoryInfo.category}
            className={`bg-gray-50 dark:bg-card hover:shadow-md hover:bg-gray-100 dark:hover:bg-accent/50 transition-all duration-200 cursor-pointer group border`}
            style={{ opacity: categoryInfo.opacity }}
            onClick={() => onCategoryClick?.(categoryInfo.category)}
          >
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <div className={`p-2 rounded-lg ${categoryColors[categoryInfo.category as keyof typeof categoryColors]}`}>
                  <Icon className="h-5 w-5" />
                </div>
                <Badge 
                  variant="secondary" 
                  className={`text-xs ${statusIndicator.color}`}
                >
                  {statusIndicator.icon} {statusIndicator.text}
                </Badge>
              </div>
              <CardTitle className="text-lg font-semibold">
                {categoryInfo.category}
              </CardTitle>
            </CardHeader>
            
            <CardContent className="pt-0">
              {/* Progress Bar */}
              <div className="mb-4">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm text-muted-foreground">Progress</span>
                  <span className="text-sm font-medium">{progress.percentage}%</span>
                </div>
                <Progress value={progress.percentage} className="h-2" />
                <div className="text-xs text-muted-foreground mt-1">
                  {progress.completed} of {progress.total} completed
                </div>
              </div>

              {/* Status Distribution */}
              <div className="mb-4">
                <div className="flex items-center gap-2 text-xs">
                  {categoryInfo.statusDistribution.confirmed > 0 && (
                    <div className="flex items-center gap-1 text-green-600 dark:text-green-400">
                      <CheckCircle className="h-3 w-3" />
                      {categoryInfo.statusDistribution.confirmed}
                    </div>
                  )}
                  {categoryInfo.statusDistribution.action > 0 && (
                    <div className="flex items-center gap-1 text-blue-600 dark:text-blue-400">
                      <Zap className="h-3 w-3" />
                      {categoryInfo.statusDistribution.action}
                    </div>
                  )}
                  {categoryInfo.statusDistribution.idea > 0 && (
                    <div className="flex items-center gap-1 text-yellow-600 dark:text-yellow-400">
                      <Lightbulb className="h-3 w-3" />
                      {categoryInfo.statusDistribution.idea}
                    </div>
                  )}
                  {categoryInfo.statusDistribution.unproven > 0 && (
                    <div className="flex items-center gap-1 text-gray-600 dark:text-gray-400">
                      <Clock className="h-3 w-3" />
                      {categoryInfo.statusDistribution.unproven}
                    </div>
                  )}
                </div>
              </div>

              {/* Next Items */}
              {progress.nextItems.length > 0 && (
                <div className="border-t pt-3">
                  <div className="text-xs text-muted-foreground mb-2">Next items:</div>
                  <div className="space-y-1">
                    {progress.nextItems.slice(0, 2).map((item, index) => (
                      <div key={index} className="text-xs text-gray-700 dark:text-gray-300 flex items-center gap-1">
                        <ArrowRight className="h-3 w-3 text-muted-foreground" />
                        <span className="truncate">{item}</span>
                      </div>
                    ))}
                    {progress.nextItems.length > 2 && (
                      <div className="text-xs text-muted-foreground">
                        +{progress.nextItems.length - 2} more
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Opacity Debug Info (remove in production) */}
              <div className="mt-3 pt-3 border-t">
                <div className="text-xs text-muted-foreground">
                  Opacity: {Math.round(categoryInfo.opacity * 100)}%
                </div>
                <div className="text-xs text-muted-foreground">
                  {categoryInfo.message}
                </div>
              </div>
            </CardContent>
          </Card>
        );
      })}
    </div>
  );
}
