"use client";

import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { useSidebar } from "@/components/ui/sidebar";
import { SidebarButton } from "@/components/ui/sidebar-button";
import { useAnalytics } from "@/hooks/useAnalytics";
import { ICON_SIZES } from "@/lib/constants";
import { getBusinessItemQA } from "@/mockdata/businessItemQuestions";
import { ArrowLeft, HelpCircle } from "lucide-react";
import { useState } from "react";

// Additional imports for new functionality

interface ProjectDetailHeaderProps {
  selectedBusinessItem: any;
  onBackToItems: () => void;
}

export function ProjectDetailHeader({
  selectedBusinessItem,
  onBackToItems,
}: ProjectDetailHeaderProps) {
  const { trackClick, trackCustomEvent } = useAnalytics();
  const [isHelpOpen, setIsHelpOpen] = useState(false);
  const [isInfoOpen, setIsInfoOpen] = useState(false);
  const { state, isMobile } = useSidebar();

  // Get dynamic question and answer based on business item
  const { question, answer } = getBusinessItemQA(selectedBusinessItem);

  return (
    <header className="flex h-20 shrink-0 items-center gap-2 transition-[width] ease-linear border-b border-border">
      <div className="flex items-center justify-between w-full h-full px-4">
        {/* Left side - Back button on mobile only, Item Title when sidebar is collapsed */}
        <div className="flex items-center gap-4 h-full">
          {isMobile && (
            <SidebarButton
              onClick={() => {
                trackClick("back-to-items", "project-detail-header");
                trackCustomEvent("navigation_clicked", {
                  destination: "items",
                  from_page: "item-detail",
                  location: "header",
                });
                onBackToItems();
              }}
              icon={ArrowLeft}
              variant="ghost"
              size="lg"
              layout="icon-only"
              showBorder={true}
              hoverColor="grey"
              hoverScale={true}
              iconClassName={ICON_SIZES.lg}
            />
          )}
          {state === "collapsed" && (
            <h1 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
              {selectedBusinessItem?.title || "Untitled Item"}
            </h1>
          )}
        </div>

        {/* Left-aligned Question and Answer section */}
        <div className="flex-1 flex flex-col justify-center mr-6">
          {/* Question with Info Icon */}
          <div className="flex items-center gap-2 mb-2">
            <Popover open={isInfoOpen} onOpenChange={setIsInfoOpen}>
              <PopoverTrigger asChild>
                <button className="flex items-center justify-center w-4 h-4 rounded-full bg-blue-100 dark:bg-blue-900 hover:bg-blue-200 dark:hover:bg-blue-800 transition-colors">
                  <svg
                    className="w-2.5 h-2.5 text-blue-600 dark:text-blue-400"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fillRule="evenodd"
                      d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                      clipRule="evenodd"
                    />
                  </svg>
                </button>
              </PopoverTrigger>
              <PopoverContent align="start" side="bottom" className="w-80">
                <div className="space-y-2">
                  <h4 className="font-semibold text-sm">Description</h4>
                  <p className="text-xs text-gray-600 dark:text-gray-400 leading-relaxed">
                    {answer}
                  </p>
                </div>
              </PopoverContent>
            </Popover>
            <div className="text-sm font-bold text-[var(--brand-dark)] dark:text-[var(--primary)]">
              {question}
            </div>
          </div>
        </div>

        {/* Right side - Help button with popover */}
        <div className="flex items-center gap-2 h-full">
          <Popover open={isHelpOpen} onOpenChange={setIsHelpOpen}>
            <PopoverTrigger>
              <SidebarButton
                icon={HelpCircle}
                variant="ghost"
                size="lg"
                layout="icon-only"
                showBorder={true}
                hoverColor="grey"
                hoverScale={true}
                onClick={() => {
                  trackClick("help-button", "project-header");
                  trackCustomEvent("help_clicked", {
                    from_item: selectedBusinessItem?.title,
                    location: "header",
                  });
                }}
                iconClassName={ICON_SIZES.lg}
              />
            </PopoverTrigger>
            <PopoverContent align="end" side="bottom" className="w-80">
              <div className="space-y-3">
                <h3 className="font-semibold text-sm">Table Guide</h3>
                <div className="space-y-2 text-xs">
                  <div className="flex items-center gap-2 p-2 border-gray-200 dark:border-gray-700 rounded-md border-1">
                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                    <span>
                      <strong>Idea:</strong> What is the main idea of the item?
                    </span>
                  </div>
                  <div className="flex items-center gap-2 p-2 border-gray-200 dark:border-gray-700 rounded-md border-1">
                    <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                    <span>
                      <strong>Action:</strong> What was done to achieve the
                      idea?
                    </span>
                  </div>
                  <div className="flex items-center gap-2 p-2 border-gray-200 dark:border-gray-700 rounded-md border-1">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span>
                      <strong>Result:</strong> What was the outcome of the
                      action?
                    </span>
                  </div>
                  <div className="flex items-center gap-2 p-2 border-gray-200 dark:border-gray-700 rounded-md border-1">
                    <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                    <span>
                      <strong>Status:</strong> Current state
                    </span>
                  </div>
                </div>
                <div className="pt-2 border-t border-gray-200 dark:border-gray-700">
                  <p className="text-xs text-gray-600 dark:text-gray-400">
                    Click cells to edit • Drag rows to reorder • Use + to add
                    items
                  </p>
                </div>
              </div>
            </PopoverContent>
          </Popover>
        </div>
      </div>
    </header>
  );
}
