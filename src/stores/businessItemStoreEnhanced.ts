// Enhanced business item store using the new comprehensive data structure
import { businessItemsData } from "@/data/businessItemsData";
import { BusinessSectionsApi } from "@/services/businessSectionsApi";
import type {
  BusinessItem,
  BusinessItemDetail,
} from "@/types/BusinessSection.types";
import { create } from "zustand";
import { devtools } from "zustand/middleware";

export interface BusinessItemStoreEnhanced {
  selectedItem: BusinessItem | null;
  itemDetails: BusinessItemDetail[];
  isLoading: boolean;
  error: string | null;

  // Actions
  setSelectedItem: (item: BusinessItem | null) => void;
  setItemDetails: (details: BusinessItemDetail[]) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;

  // Enhanced actions
  fetchItemDetails: (itemId: string, projectId?: string) => Promise<void>;
  updateItemStatus: (
    itemId: string,
    status: BusinessItem["status"],
    projectId?: string
  ) => Promise<void>;
  addItemDetail: (detail: BusinessItemDetail) => void;
  updateItemDetail: (id: string, updates: Partial<BusinessItemDetail>) => void;
  removeItemDetail: (id: string) => void;

  // Utility actions
  getItemData: (itemId: string) => any;
  checkDependencies: (itemId: string) => boolean;
  getRelatedItems: (itemId: string) => {
    dependencies: any[];
    dependents: any[];
  };
}

export const useBusinessItemStoreEnhanced = create<BusinessItemStoreEnhanced>()(
  devtools(
    (set, get) => ({
      selectedItem: null,
      itemDetails: [],
      isLoading: false,
      error: null,

      setSelectedItem: (item) => set({ selectedItem: item }),

      setItemDetails: (details) => set({ itemDetails: details }),

      setLoading: (isLoading) => set({ isLoading }),

      setError: (error) => set({ error }),

      // Fetch comprehensive item details using the new data structure
      fetchItemDetails: async (
        itemId: string,
        projectId: string = "default"
      ) => {
        try {
          set({ isLoading: true, error: null });

          // First, try to get stored details from browser storage
          const storedDetails = browserStorageService.getItemDetails(
            itemId,
            projectId
          );

          // For now, always regenerate to see fresh data (remove this later)
          // if (storedDetails.length > 0) {
          //   // Use stored details if available
          //   console.log("Using stored details for", itemId, storedDetails);
          //   set({ itemDetails: storedDetails, isLoading: false });
          //   return;
          // }

          console.log(
            "No stored details found, creating initial data for",
            itemId
          );

          // If no stored details, create initial details from comprehensive data
          const itemData = businessItemsData.find((item) => item.id === itemId);

          if (!itemData) {
            throw new Error(`Item with ID ${itemId} not found`);
          }

          // Create initial detail items from sample values
          const initialDetails: BusinessItemDetail[] = [];

          // Add sample values as editable detail items for the table
          if (itemData.values && itemData.values.length > 0) {
            itemData.values.forEach((value, index) => {
              initialDetails.push({
                id: `${itemData.id}-value-${index}`,
                title: `Response ${index + 1}`,
                status: "idea",
                actions: "Sample response - edit to customize",
                result: value,
                description: `Sample response for: ${
                  itemData.question?.replace(
                    "{PROJECT NAME}",
                    "your project"
                  ) || itemData.title
                }`,
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString(),
              });
            });
          }

          // Add a blank item for new entries
          initialDetails.push({
            id: `${itemData.id}-new-entry`,
            title: "Add your response",
            status: "unproven",
            actions: "Click to edit and add your own analysis",
            result: "Enter your findings here...",
            description: `Your analysis for: ${
              itemData.question?.replace("{PROJECT NAME}", "your project") ||
              itemData.title
            }`,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          });

          // Save initial details to browser storage
          browserStorageService.saveItemDetails(
            itemId,
            initialDetails,
            projectId
          );

          console.log("Created initial details for", itemId, initialDetails);
          set({ itemDetails: initialDetails, isLoading: false });
        } catch (error) {
          console.error("Error fetching item details:", error);
          set({
            error:
              error instanceof Error
                ? error.message
                : "Failed to fetch item details",
            isLoading: false,
          });
        }
      },

      // Update item status with dependency validation
      updateItemStatus: async (
        itemId: string,
        status: BusinessItem["status"],
        projectId: string = "default"
      ) => {
        try {
          set({ isLoading: true, error: null });

          // Check dependencies before allowing status change to "confirmed"
          if (status === "confirmed") {
            const canProgress = get().checkDependencies(itemId);
            if (!canProgress) {
              throw new Error(
                "Cannot mark as confirmed: dependencies not satisfied"
              );
            }
          }

          // Use the API to update the item
          await BusinessSectionsApi.updateItem(projectId, itemId, { status });

          // Update local state if we have the item selected
          const { selectedItem } = get();
          if (selectedItem && selectedItem.id === itemId) {
            set({ selectedItem: { ...selectedItem, status } });
          }

          set({ isLoading: false });
        } catch (error) {
          console.error("Error updating item status:", error);
          set({
            error:
              error instanceof Error
                ? error.message
                : "Failed to update item status",
            isLoading: false,
          });
        }
      },

      addItemDetail: (detail) => {
        const { selectedItem } = get();
        if (!selectedItem) return;

        const updatedDetails = [...get().itemDetails, detail];
        set({ itemDetails: updatedDetails });

        // Save to browser storage
        browserStorageService.saveItemDetails(
          selectedItem.id,
          updatedDetails,
          "default"
        );
      },

      updateItemDetail: (id, updates) => {
        const { selectedItem } = get();
        if (!selectedItem) return;

        const updatedDetails = get().itemDetails.map((item) =>
          item.id === id
            ? { ...item, ...updates, updatedAt: new Date().toISOString() }
            : item
        );

        set({ itemDetails: updatedDetails });

        // Save to browser storage
        browserStorageService.saveItemDetails(
          selectedItem.id,
          updatedDetails,
          "default"
        );
      },

      removeItemDetail: (id) => {
        const { selectedItem } = get();
        if (!selectedItem) return;

        const updatedDetails = get().itemDetails.filter(
          (item) => item.id !== id
        );
        set({ itemDetails: updatedDetails });

        // Save to browser storage
        browserStorageService.saveItemDetails(
          selectedItem.id,
          updatedDetails,
          "default"
        );
      },

      // Get comprehensive item data
      getItemData: (itemId: string) => {
        return businessItemsData.find((item) => item.id === itemId);
      },

      // Check if item dependencies are satisfied
      checkDependencies: (itemId: string) => {
        const itemData = businessItemsData.find((item) => item.id === itemId);
        if (
          !itemData ||
          !itemData.dependencies ||
          itemData.dependencies.length === 0
        ) {
          return true;
        }

        return itemData.dependencies.every((depId) => {
          const depItem = businessItemsData.find((dep) => dep.id === depId);
          return depItem?.status === "confirmed";
        });
      },

      // Get related items (dependencies and dependents)
      getRelatedItems: (itemId: string) => {
        const itemData = businessItemsData.find((item) => item.id === itemId);

        // Get dependencies
        const dependencies =
          itemData?.dependencies
            ?.map((depId) =>
              businessItemsData.find((item) => item.id === depId)
            )
            .filter(Boolean) || [];

        // Get dependents (items that depend on this one)
        const dependents = businessItemsData.filter((item) =>
          item.dependencies?.includes(itemId)
        );

        return { dependencies, dependents };
      },
    }),
    {
      name: "business-item-store-enhanced",
    }
  )
);

// Helper functions for easy access
export const getItemData = (itemId: string) => {
  return businessItemsData.find((item) => item.id === itemId);
};

export const checkItemDependencies = (itemId: string) => {
  const itemData = businessItemsData.find((item) => item.id === itemId);
  if (
    !itemData ||
    !itemData.dependencies ||
    itemData.dependencies.length === 0
  ) {
    return true;
  }

  return itemData.dependencies.every((depId) => {
    const depItem = businessItemsData.find((dep) => dep.id === depId);
    return depItem?.status === "confirmed";
  });
};

export const getRelatedItems = (itemId: string) => {
  const itemData = businessItemsData.find((item) => item.id === itemId);

  const dependencies =
    itemData?.dependencies
      ?.map((depId) => businessItemsData.find((item) => item.id === depId))
      .filter(Boolean) || [];

  const dependents = businessItemsData.filter((item) =>
    item.dependencies?.includes(itemId)
  );

  return { dependencies, dependents };
};
