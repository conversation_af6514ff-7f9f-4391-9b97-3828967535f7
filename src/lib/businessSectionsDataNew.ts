// New comprehensive business sections data with all 23 items
import {
  AlertTriangle,
  Star,
  Users,
  Lightbulb,
  BarChart3,
  TrendingUp,
  Heart,
  Shield,
  Package,
  Settings,
  Target,
  Megaphone,
  MessageCircle,
  Palette,
  FileText,
  ShoppingCart,
  Clock,
  Building,
  DollarSign,
  Briefcase,
} from "lucide-react";
import type { BusinessSection, BusinessItem } from "@/types/BusinessSection.types";
import { businessItemsData } from "@/data/businessItemsData";

// Icon mapping for business items
const iconMap: Record<string, any> = {
  problem: AlertTriangle,
  "unique-value-proposition": Star,
  audience: Users,
  alternatives: Lightbulb,
  "market-size": BarChart3,
  trends: TrendingUp,
  why: Heart,
  advantages: Shield,
  product: Package,
  tech: Settings,
  packages: Package,
  positioning: Target,
  channels: Megaphone,
  messaging: MessageCircle,
  brand: Palette,
  assets: FileText,
  "sales-motion": ShoppingCart,
  metrics: Clock,
  risks: Shield,
  "business-model": Building,
  revenue: DollarSign,
  cost: DollarSign,
  team: Briefcase,
};

// Convert BusinessItemData to BusinessItem format
const convertToBusinessItem = (itemData: any): BusinessItem => ({
  id: itemData.id,
  title: itemData.title,
  description: itemData.description,
  question: itemData.question,
  guidance: itemData.guidance,
  category: itemData.category,
  order: itemData.order,
  inputType: itemData.inputType,
  dependencies: itemData.dependencies,
  status: itemData.status,
  values: itemData.values,
  actions: itemData.actions,
  ideas: itemData.ideas,
  results: itemData.results,
  icon: iconMap[itemData.id] || Package,
});

// Group items by category and create sections
const createBusinessSections = (): BusinessSection[] => {
  const categories = ["Market", "Solution", "Sales & Marketing", "Company"];
  
  return categories.map((category) => {
    const categoryItems = businessItemsData
      .filter((item) => item.category === category)
      .sort((a, b) => a.order - b.order)
      .map(convertToBusinessItem);

    return {
      id: category.toLowerCase().replace(/\s+/g, "-"),
      title: category,
      items: categoryItems,
    };
  });
};

// Export the comprehensive mock data
export const mockBusinessSectionsNew: BusinessSection[] = createBusinessSections();

// Helper functions for dependency checking
export const checkDependencies = (itemId: string): boolean => {
  const item = businessItemsData.find((item) => item.id === itemId);
  if (!item || !item.dependencies.length) return true;
  
  return item.dependencies.every((depId) => {
    const depItem = businessItemsData.find((dep) => dep.id === depId);
    return depItem?.status === "confirmed";
  });
};

// Get items that are ready to work on (dependencies met)
export const getReadyItems = (): string[] => {
  return businessItemsData
    .filter((item) => item.status !== "confirmed" && checkDependencies(item.id))
    .map((item) => item.id);
};

// Get status counts for analytics
export const getStatusCounts = () => {
  const counts = {
    idea: 0,
    action: 0,
    confirmed: 0,
    unproven: 0,
  };
  
  businessItemsData.forEach((item) => {
    counts[item.status]++;
  });
  
  return counts;
};

// Get category completion percentage
export const getCategoryCompletion = (category: string): number => {
  const categoryItems = businessItemsData.filter((item) => item.category === category);
  const confirmedItems = categoryItems.filter((item) => item.status === "confirmed");
  
  return categoryItems.length > 0 ? (confirmedItems.length / categoryItems.length) * 100 : 0;
};

// Calculate main page card opacity based on status distribution
export const getCardOpacity = (category: string): number => {
  const categoryItems = businessItemsData.filter((item) => item.category === category);
  
  if (categoryItems.length === 0) return 1; // 100% opacity if no items
  
  const hasConfirmed = categoryItems.some((item) => item.status === "confirmed");
  const hasAction = categoryItems.some((item) => item.status === "action");
  const hasIdea = categoryItems.some((item) => item.status === "idea");
  
  if (hasConfirmed) return 0.5; // 50% opacity
  if (hasAction) return 0.2; // 20% opacity
  if (hasIdea) return 0.1; // 10% opacity
  
  return 1; // 100% opacity for all unproven
};

// API function to fetch business sections (placeholder for future implementation)
export const fetchBusinessSectionsNew = async (projectId: string): Promise<BusinessSection[]> => {
  // This will be replaced with actual API call
  // For now, return mock data
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(mockBusinessSectionsNew);
    }, 500);
  });
};
