"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/projects/[id]/page",{

/***/ "(app-pages-browser)/./src/components/business-item-table.tsx":
/*!************************************************!*\
  !*** ./src/components/business-item-table.tsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BusinessItemTable: () => (/* binding */ BusinessItemTable)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/dropdown-menu */ \"(app-pages-browser)/./src/components/ui/dropdown-menu.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./src/components/ui/select.tsx\");\n/* harmony import */ var _components_ui_table__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/table */ \"(app-pages-browser)/./src/components/ui/table.tsx\");\n/* harmony import */ var _components_ui_tooltip__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/tooltip */ \"(app-pages-browser)/./src/components/ui/tooltip.tsx\");\n/* harmony import */ var _stores_businessItemStore__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/stores/businessItemStore */ \"(app-pages-browser)/./src/stores/businessItemStore.ts\");\n/* harmony import */ var _hello_pangea_dnd__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @hello-pangea/dnd */ \"(app-pages-browser)/./node_modules/@hello-pangea/dnd/dist/dnd.esm.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Eye_GripVertical_MoreHorizontal_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Eye,GripVertical,MoreHorizontal,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/grip-vertical.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Eye_GripVertical_MoreHorizontal_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Eye,GripVertical,MoreHorizontal,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/ellipsis.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Eye_GripVertical_MoreHorizontal_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Eye,GripVertical,MoreHorizontal,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Eye_GripVertical_MoreHorizontal_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Eye,GripVertical,MoreHorizontal,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Eye_GripVertical_MoreHorizontal_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Eye,GripVertical,MoreHorizontal,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_9__);\n/* harmony import */ var _EditableCell__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./EditableCell */ \"(app-pages-browser)/./src/components/EditableCell.tsx\");\n/* __next_internal_client_entry_do_not_use__ BusinessItemTable auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n// Get status color helper\nconst getStatusColor = (status)=>{\n    switch(status){\n        case \"idea\":\n            return \"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200\";\n        case \"action\":\n            return \"bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200\";\n        case \"confirmed\":\n            return \"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200\";\n        case \"unproven\":\n            return \"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200\";\n        default:\n            return \"bg-muted text-muted-foreground\";\n    }\n};\n// Helper function to determine status based on content\nconst getAutoStatus = (title, actions, result)=>{\n    const hasTitle = title.trim() !== \"\";\n    const hasActions = actions.trim() !== \"\";\n    const hasResult = result.trim() !== \"\";\n    if (hasTitle && !hasActions && !hasResult) {\n        return \"idea\";\n    } else if (hasTitle && hasActions && !hasResult) {\n        return \"action\";\n    } else if (hasTitle && hasActions && hasResult) {\n        return \"confirmed\"; // Default when all fields are filled\n    }\n    return \"idea\"; // Default fallback\n};\n// Helper function to check if status should be editable\nconst isStatusEditable = (title, actions, result)=>{\n    const hasTitle = title.trim() !== \"\";\n    const hasActions = actions.trim() !== \"\";\n    const hasResult = result.trim() !== \"\";\n    // Only editable when all three fields are filled (can choose between confirmed/unproven)\n    return hasTitle && hasActions && hasResult;\n};\n// StatusSelect component\nfunction StatusSelect(param) {\n    let { value, onChange, placeholder, disabled = false, detail } = param;\n    const editable = isStatusEditable(detail.title, detail.actions, detail.result);\n    if (!editable) {\n        // Show as non-editable badge\n        const autoStatus = getAutoStatus(detail.title, detail.actions, detail.result);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_1__.Badge, {\n            className: \"\".concat(getStatusColor(autoStatus), \" capitalize\"),\n            children: autoStatus\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx\",\n            lineNumber: 122,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.Select, {\n        value: value,\n        onValueChange: onChange,\n        disabled: disabled,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectTrigger, {\n                className: \"w-[120px] h-7 px-2\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectValue, {\n                    placeholder: placeholder\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx\",\n                    lineNumber: 131,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx\",\n                lineNumber: 130,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectContent, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                        value: \"confirmed\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_1__.Badge, {\n                            className: getStatusColor(\"confirmed\"),\n                            children: \"Confirmed\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx\",\n                            lineNumber: 135,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx\",\n                        lineNumber: 134,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                        value: \"unproven\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_1__.Badge, {\n                            className: getStatusColor(\"unproven\"),\n                            children: \"Unproven\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx\",\n                            lineNumber: 138,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx\",\n                        lineNumber: 137,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx\",\n                lineNumber: 133,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx\",\n        lineNumber: 129,\n        columnNumber: 5\n    }, this);\n}\n_c = StatusSelect;\n// DragHandle component\nfunction DragHandle(param) {\n    let { provided } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ...provided.dragHandleProps,\n        className: \"cursor-grab py-3 px-3 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-200 hover:bg-muted/50\",\n        title: \"Drag to reorder\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Eye_GripVertical_MoreHorizontal_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n            className: \"h-4 w-4 text-muted-foreground hover:text-foreground transition-colors\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx\",\n            lineNumber: 153,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx\",\n        lineNumber: 148,\n        columnNumber: 5\n    }, this);\n}\n_c1 = DragHandle;\n// BusinessItemRow component\nfunction BusinessItemRow(param) {\n    let { detail, index, editingCell, setEditingCell, onSave, onStatusChange } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_hello_pangea_dnd__WEBPACK_IMPORTED_MODULE_12__.Draggable, {\n        draggableId: detail.id,\n        index: index,\n        children: (provided, snapshot)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableRow, {\n                ref: provided.innerRef,\n                ...provided.draggableProps,\n                className: \"group hover:bg-gray-100 dark:hover:bg-gray-800 transition-all duration-200 \".concat(snapshot.isDragging ? \"bg-gray-200 dark:bg-gray-700 shadow-lg scale-[1.02] rotate-1\" : \"\", \" \").concat(index % 2 === 1 ? \"bg-gray-50 dark:bg-gray-900\" : \"bg-background\"),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableCell, {\n                        className: \"py-0 px-0 border-r\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DragHandle, {\n                            provided: provided\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx\",\n                            lineNumber: 189,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx\",\n                        lineNumber: 188,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableCell, {\n                        className: \"font-medium py-3 px-3 border-r w-1/3\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_EditableCell__WEBPACK_IMPORTED_MODULE_10__.EditableCell, {\n                            id: detail.id,\n                            field: \"title\",\n                            value: detail.title,\n                            multiline: true,\n                            className: \"font-semibold\",\n                            editingCell: editingCell,\n                            setEditingCell: setEditingCell,\n                            onSave: onSave\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx\",\n                            lineNumber: 192,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx\",\n                        lineNumber: 191,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableCell, {\n                        className: \"py-3 px-3 border-r w-1/3\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_EditableCell__WEBPACK_IMPORTED_MODULE_10__.EditableCell, {\n                            id: detail.id,\n                            field: \"actions\",\n                            value: detail.actions,\n                            multiline: true,\n                            editingCell: editingCell,\n                            setEditingCell: setEditingCell,\n                            onSave: onSave\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx\",\n                            lineNumber: 204,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx\",\n                        lineNumber: 203,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableCell, {\n                        className: \"py-3 px-3 border-r w-1/3\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_EditableCell__WEBPACK_IMPORTED_MODULE_10__.EditableCell, {\n                            id: detail.id,\n                            field: \"result\",\n                            value: detail.result,\n                            multiline: true,\n                            editingCell: editingCell,\n                            setEditingCell: setEditingCell,\n                            onSave: onSave\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx\",\n                            lineNumber: 215,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx\",\n                        lineNumber: 214,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableCell, {\n                        className: \"py-3 px-3 border-r\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatusSelect, {\n                            value: detail.status,\n                            onChange: (value)=>onStatusChange(detail.id, value),\n                            detail: detail\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx\",\n                            lineNumber: 226,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx\",\n                        lineNumber: 225,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableCell, {\n                        className: \"py-3 px-3\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__.DropdownMenu, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__.DropdownMenuTrigger, {\n                                    asChild: true,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        variant: \"ghost\",\n                                        className: \"h-8 w-8 p-0\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"sr-only\",\n                                                children: \"Open menu\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx\",\n                                                lineNumber: 236,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Eye_GripVertical_MoreHorizontal_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx\",\n                                                lineNumber: 237,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx\",\n                                        lineNumber: 235,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx\",\n                                    lineNumber: 234,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__.DropdownMenuContent, {\n                                    align: \"end\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__.DropdownMenuItem, {\n                                            className: \"gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Eye_GripVertical_MoreHorizontal_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx\",\n                                                    lineNumber: 242,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"View\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx\",\n                                            lineNumber: 241,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__.DropdownMenuItem, {\n                                            className: \"gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Eye_GripVertical_MoreHorizontal_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx\",\n                                                    lineNumber: 246,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Edit\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx\",\n                                            lineNumber: 245,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__.DropdownMenuItem, {\n                                            className: \"gap-2 text-red-600\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Eye_GripVertical_MoreHorizontal_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx\",\n                                                    lineNumber: 250,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Delete\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx\",\n                                            lineNumber: 249,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx\",\n                                    lineNumber: 240,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx\",\n                            lineNumber: 233,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx\",\n                        lineNumber: 232,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx\",\n                lineNumber: 177,\n                columnNumber: 9\n            }, this)\n    }, detail.id, false, {\n        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx\",\n        lineNumber: 175,\n        columnNumber: 5\n    }, this);\n}\n_c2 = BusinessItemRow;\n// NewRow component\nfunction NewRow(param) {\n    let { newRowData, editingCell, setEditingCell, onSave, onStatusChange } = param;\n    // Create a mock detail object for status determination\n    const mockDetail = {\n        id: \"new-row\",\n        title: newRowData.title,\n        actions: newRowData.actions,\n        result: newRowData.result,\n        status: newRowData.status\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableRow, {\n        className: \"bg-gray-50 dark:bg-gray-900 border-t-2 border-dashed border-gray-300 dark:border-gray-600\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableCell, {\n                className: \"py-3 px-3 border-r\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx\",\n                    lineNumber: 294,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx\",\n                lineNumber: 293,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableCell, {\n                className: \"font-medium py-3 px-3 border-r w-1/3\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_EditableCell__WEBPACK_IMPORTED_MODULE_10__.EditableCell, {\n                    id: \"new-row\",\n                    field: \"title\",\n                    value: \"\",\n                    multiline: true,\n                    className: \"font-semibold\",\n                    editingCell: editingCell,\n                    setEditingCell: setEditingCell,\n                    onSave: onSave,\n                    newRowData: newRowData\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx\",\n                    lineNumber: 297,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx\",\n                lineNumber: 296,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableCell, {\n                className: \"py-3 px-3 border-r w-1/3\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_EditableCell__WEBPACK_IMPORTED_MODULE_10__.EditableCell, {\n                    id: \"new-row\",\n                    field: \"actions\",\n                    value: \"\",\n                    multiline: true,\n                    editingCell: editingCell,\n                    setEditingCell: setEditingCell,\n                    onSave: onSave,\n                    newRowData: newRowData\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx\",\n                    lineNumber: 310,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx\",\n                lineNumber: 309,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableCell, {\n                className: \"py-3 px-3 border-r w-1/3\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_EditableCell__WEBPACK_IMPORTED_MODULE_10__.EditableCell, {\n                    id: \"new-row\",\n                    field: \"result\",\n                    value: \"\",\n                    multiline: true,\n                    editingCell: editingCell,\n                    setEditingCell: setEditingCell,\n                    onSave: onSave,\n                    newRowData: newRowData\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx\",\n                    lineNumber: 322,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx\",\n                lineNumber: 321,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableCell, {\n                className: \"py-3 px-3 border-r\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatusSelect, {\n                    value: newRowData.status,\n                    onChange: (value)=>onStatusChange(\"new-row\", value),\n                    placeholder: \"Status\",\n                    detail: mockDetail\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx\",\n                    lineNumber: 334,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx\",\n                lineNumber: 333,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableCell, {\n                className: \"py-3 px-3\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-8 w-8\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx\",\n                    lineNumber: 342,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx\",\n                lineNumber: 341,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx\",\n        lineNumber: 292,\n        columnNumber: 5\n    }, this);\n}\n_c3 = NewRow;\nfunction BusinessItemTable(param) {\n    let { itemDetails } = param;\n    _s();\n    const { updateItemDetail, addItemDetail } = (0,_stores_businessItemStore__WEBPACK_IMPORTED_MODULE_8__.useBusinessItemStore)();\n    // Local state for reordering\n    const [localItemDetails, setLocalItemDetails] = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)(itemDetails);\n    const [editingCell, setEditingCell] = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)(null);\n    const [isHowItWorksOpen, setIsHowItWorksOpen] = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)(false);\n    const [newRowData, setNewRowData] = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)({\n        id: \"new-row\",\n        title: \"\",\n        actions: \"\",\n        result: \"\",\n        status: \"idea\"\n    });\n    // Update local state when props change\n    (0,react__WEBPACK_IMPORTED_MODULE_9__.useEffect)({\n        \"BusinessItemTable.useEffect\": ()=>{\n            setLocalItemDetails(itemDetails);\n        }\n    }[\"BusinessItemTable.useEffect\"], [\n        itemDetails\n    ]);\n    const handleSave = (id, field, value)=>{\n        if (id === \"new-row\") {\n            // Handle new row creation\n            const updatedNewRowData = {\n                ...newRowData,\n                [field]: value\n            };\n            // Calculate the appropriate status based on filled fields\n            const autoStatus = getAutoStatus(field === \"title\" ? value : newRowData.title, field === \"actions\" ? value : newRowData.actions, field === \"result\" ? value : newRowData.result);\n            const newId = \"item-\".concat(Date.now());\n            const newItem = {\n                id: newId,\n                title: field === \"title\" ? value : newRowData.title,\n                actions: field === \"actions\" ? value : newRowData.actions,\n                result: field === \"result\" ? value : newRowData.result,\n                status: autoStatus,\n                description: \"\",\n                updatedAt: new Date().toISOString()\n            };\n            // Update the specific field that was edited\n            newItem[field] = value;\n            // Only create if there's actual content\n            if (value.trim() !== \"\") {\n                const updatedItems = [\n                    ...localItemDetails,\n                    newItem\n                ];\n                setLocalItemDetails(updatedItems);\n                addItemDetail(newItem);\n                // Reset new row data\n                setNewRowData({\n                    id: \"new-row\",\n                    title: \"\",\n                    actions: \"\",\n                    result: \"\",\n                    status: \"idea\"\n                });\n            } else {\n                // Just update the new row data for display\n                setNewRowData(updatedNewRowData);\n            }\n        } else {\n            // Handle existing item update\n            const item = localItemDetails.find((item)=>item.id === id);\n            if (item) {\n                const updatedItem = {\n                    ...item,\n                    [field]: value\n                };\n                // Auto-update status based on content\n                const autoStatus = getAutoStatus(field === \"title\" ? value : item.title, field === \"actions\" ? value : item.actions, field === \"result\" ? value : item.result);\n                // Only auto-update status if it's not manually set to confirmed/unproven\n                if (!isStatusEditable(updatedItem.title, updatedItem.actions, updatedItem.result) || item.status !== \"confirmed\" && item.status !== \"unproven\") {\n                    updatedItem.status = autoStatus;\n                }\n                const updatedItems = localItemDetails.map((existingItem)=>existingItem.id === id ? updatedItem : existingItem);\n                setLocalItemDetails(updatedItems);\n                updateItemDetail(id, {\n                    [field]: value,\n                    status: updatedItem.status\n                });\n            }\n        }\n        setEditingCell(null);\n    };\n    const handleDragEnd = (result)=>{\n        // Check if the drop was outside the droppable area\n        if (!result.destination) {\n            return;\n        }\n        const { source, destination } = result;\n        // If dropped in the same position, do nothing\n        if (source.index === destination.index) {\n            return;\n        }\n        // Reorder items\n        const newItems = Array.from(localItemDetails);\n        const [reorderedItem] = newItems.splice(source.index, 1);\n        newItems.splice(destination.index, 0, reorderedItem);\n        setLocalItemDetails(newItems);\n    };\n    // Handle status change (only for confirmed/unproven when all fields are filled)\n    const handleStatusChange = (id, newStatus)=>{\n        if (id === \"new-row\") {\n            setNewRowData((prev)=>({\n                    ...prev,\n                    status: newStatus\n                }));\n            return;\n        }\n        const updatedItems = localItemDetails.map((item)=>item.id === id ? {\n                ...item,\n                status: newStatus\n            } : item);\n        setLocalItemDetails(updatedItems);\n        updateItemDetail(id, {\n            status: newStatus\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_7__.TooltipProvider, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-full max-w-full overflow-hidden -m-0 -p-0\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                className: \"w-full p-0 m-0 overflow-hidden border-0 shadow-none rounded-none\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                    className: \"p-0 m-0\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_hello_pangea_dnd__WEBPACK_IMPORTED_MODULE_12__.DragDropContext, {\n                        onDragEnd: handleDragEnd,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-full overflow-auto max-h-[calc(100vh-200px)] m-0 p-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.Table, {\n                                className: \"m-0 rounded-none\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableHeader, {\n                                        className: \"sticky top-0 bg-gray-200 dark:bg-gray-800 backdrop-blur-sm z-10\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableRow, {\n                                            className: \"border-b-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableHead, {\n                                                    className: \"bg-gray-200 dark:bg-gray-800 border-r\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx\",\n                                                    lineNumber: 505,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableHead, {\n                                                    className: \"font-semibold border-r bg-gray-200 dark:bg-gray-800 w-1/3\",\n                                                    children: \"Idea\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx\",\n                                                    lineNumber: 506,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableHead, {\n                                                    className: \"font-semibold border-r bg-gray-200 dark:bg-gray-800 w-1/3\",\n                                                    children: \"Action\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx\",\n                                                    lineNumber: 509,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableHead, {\n                                                    className: \"font-semibold border-r bg-gray-200 dark:bg-gray-800 w-1/3\",\n                                                    children: \"Result\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx\",\n                                                    lineNumber: 512,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableHead, {\n                                                    className: \"font-semibold border-r bg-gray-200 dark:bg-gray-800 w-32\",\n                                                    children: \"Status\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx\",\n                                                    lineNumber: 515,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableHead, {\n                                                    className: \"font-semibold bg-gray-200 dark:bg-gray-800 w-20\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx\",\n                                                    lineNumber: 518,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx\",\n                                            lineNumber: 504,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx\",\n                                        lineNumber: 503,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_hello_pangea_dnd__WEBPACK_IMPORTED_MODULE_12__.Droppable, {\n                                        droppableId: \"business-items\",\n                                        children: (provided)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableBody, {\n                                                ...provided.droppableProps,\n                                                ref: provided.innerRef,\n                                                children: [\n                                                    localItemDetails.map((detail, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BusinessItemRow, {\n                                                            detail: detail,\n                                                            index: index,\n                                                            editingCell: editingCell,\n                                                            setEditingCell: setEditingCell,\n                                                            onSave: handleSave,\n                                                            onStatusChange: handleStatusChange\n                                                        }, detail.id, false, {\n                                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx\",\n                                                            lineNumber: 529,\n                                                            columnNumber: 27\n                                                        }, this)),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NewRow, {\n                                                        newRowData: newRowData,\n                                                        editingCell: editingCell,\n                                                        setEditingCell: setEditingCell,\n                                                        onSave: handleSave,\n                                                        onStatusChange: handleStatusChange\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx\",\n                                                        lineNumber: 540,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    provided.placeholder\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx\",\n                                                lineNumber: 524,\n                                                columnNumber: 23\n                                            }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx\",\n                                        lineNumber: 522,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx\",\n                                lineNumber: 502,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx\",\n                            lineNumber: 501,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx\",\n                        lineNumber: 500,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx\",\n                    lineNumber: 499,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx\",\n                lineNumber: 498,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx\",\n            lineNumber: 497,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx\",\n        lineNumber: 496,\n        columnNumber: 5\n    }, this);\n}\n_s(BusinessItemTable, \"hsghXYR8cMt5O7DWFS+NX589ziY=\", false, function() {\n    return [\n        _stores_businessItemStore__WEBPACK_IMPORTED_MODULE_8__.useBusinessItemStore\n    ];\n});\n_c4 = BusinessItemTable;\nvar _c, _c1, _c2, _c3, _c4;\n$RefreshReg$(_c, \"StatusSelect\");\n$RefreshReg$(_c1, \"DragHandle\");\n$RefreshReg$(_c2, \"BusinessItemRow\");\n$RefreshReg$(_c3, \"NewRow\");\n$RefreshReg$(_c4, \"BusinessItemTable\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/business-item-table.tsx\n"));

/***/ })

});