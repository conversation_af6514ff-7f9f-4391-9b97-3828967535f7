"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/projects/[id]/page",{

/***/ "(app-pages-browser)/./src/app/projects/[id]/page.tsx":
/*!****************************************!*\
  !*** ./src/app/projects/[id]/page.tsx ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProjectDetailPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_project__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/project */ \"(app-pages-browser)/./src/components/project/index.ts\");\n/* harmony import */ var _components_project_sidebar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/project-sidebar */ \"(app-pages-browser)/./src/components/project-sidebar.tsx\");\n/* harmony import */ var _components_ui_sidebar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/sidebar */ \"(app-pages-browser)/./src/components/ui/sidebar.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _hooks_useResizable__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../../hooks/useResizable */ \"(app-pages-browser)/./src/hooks/useResizable.ts\");\n/* harmony import */ var _lib_businessSectionsDataNew__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../../lib/businessSectionsDataNew */ \"(app-pages-browser)/./src/lib/businessSectionsDataNew.ts\");\n/* harmony import */ var _stores_businessItemStore__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../../../stores/businessItemStore */ \"(app-pages-browser)/./src/stores/businessItemStore.ts\");\n/* harmony import */ var _stores_businessSectionStore__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../../../stores/businessSectionStore */ \"(app-pages-browser)/./src/stores/businessSectionStore.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n// Mock data\nconst mockDraftItems = [\n    {\n        id: 1,\n        title: \"Project proposal\",\n        status: \"draft\",\n        lastModified: \"2 hours ago\"\n    },\n    {\n        id: 2,\n        title: \"Design brief\",\n        status: \"draft\",\n        lastModified: \"1 day ago\"\n    },\n    {\n        id: 3,\n        title: \"Technical specs\",\n        status: \"draft\",\n        lastModified: \"3 days ago\"\n    }\n];\n// Mock file items data\nconst mockFileItems = [\n    {\n        id: 1,\n        title: \"logo.svg\",\n        type: \"image\",\n        size: \"24KB\"\n    },\n    {\n        id: 2,\n        title: \"wireframes.fig\",\n        type: \"design\",\n        size: \"1.2MB\"\n    },\n    {\n        id: 3,\n        title: \"requirements.pdf\",\n        type: \"document\",\n        size: \"156KB\"\n    },\n    {\n        id: 4,\n        title: \"styleguide.pdf\",\n        type: \"document\",\n        size: \"2.1MB\"\n    }\n];\nfunction ProjectDetailPage() {\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.useParams)();\n    const projectId = params.id;\n    const [activeContent, setActiveContent] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(null);\n    const [chatWidth, setChatWidth] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(\"45%\");\n    const [isChatCollapsed, setIsChatCollapsed] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    const [sidebarWidth, setSidebarWidth] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(\"45vw\");\n    // Sync sidebar width with chat width\n    const handleChatWidthChange = (width)=>{\n        setChatWidth(width);\n        setSidebarWidth(width === \"45%\" ? \"45vw\" : \"45vw\");\n    };\n    const { sections, isLoading, error, setSections, setLoading, setError } = (0,_stores_businessSectionStore__WEBPACK_IMPORTED_MODULE_9__.useBusinessSectionStore)();\n    const { selectedItem, itemDetails, setSelectedItem, setItemDetails } = (0,_stores_businessItemStore__WEBPACK_IMPORTED_MODULE_8__.useBusinessItemStore)();\n    // Dynamic sidebar width based on selected item\n    const currentSidebarWidth = selectedItem ? \"30vw\" : sidebarWidth;\n    // Reset to default view on page load/refresh\n    (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)({\n        \"ProjectDetailPage.useEffect\": ()=>{\n            setSelectedItem(null);\n            setItemDetails([]);\n        }\n    }[\"ProjectDetailPage.useEffect\"], [\n        setSelectedItem,\n        setItemDetails\n    ]);\n    // Load business sections on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)({\n        \"ProjectDetailPage.useEffect\": ()=>{\n            const loadBusinessSections = {\n                \"ProjectDetailPage.useEffect.loadBusinessSections\": async ()=>{\n                    try {\n                        setLoading(true);\n                        setError(null);\n                        const data = await (0,_lib_businessSectionsDataNew__WEBPACK_IMPORTED_MODULE_7__.fetchBusinessSectionsNew)(projectId);\n                        setSections(data);\n                    } catch (err) {\n                        setError(\"Failed to load business sections\");\n                        console.error(\"Error loading business sections:\", err);\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"ProjectDetailPage.useEffect.loadBusinessSections\"];\n            loadBusinessSections();\n        }\n    }[\"ProjectDetailPage.useEffect\"], [\n        setSections,\n        setLoading,\n        setError\n    ]);\n    // Handle business item selection - show detail view\n    const handleBusinessItemClick = (item)=>{\n        setSelectedItem(item);\n        // Clear item details since we're using the enhanced view with comprehensive data\n        setItemDetails([]);\n    };\n    // Handle back to items\n    const handleBackToItems = ()=>{\n        setSelectedItem(null);\n    };\n    // Resize functionality\n    const resizable = (0,_hooks_useResizable__WEBPACK_IMPORTED_MODULE_6__.useResizable)({\n        initialWidth: sidebarWidth,\n        minWidthPercent: 10,\n        maxWidthPercent: 70,\n        onWidthChange: {\n            \"ProjectDetailPage.useResizable[resizable]\": (width)=>{\n                // Convert percentage to viewport width\n                const widthPercent = parseFloat(width.replace(\"%\", \"\"));\n                const vwWidth = \"\".concat(widthPercent, \"vw\");\n                setSidebarWidth(vwWidth);\n                // Update chatWidth to match sidebarWidth for consistency\n                if (widthPercent <= 70) {\n                    setChatWidth(\"45%\");\n                } else {\n                    setChatWidth(\"45%\");\n                }\n            }\n        }[\"ProjectDetailPage.useResizable[resizable]\"],\n        onCollapse: {\n            \"ProjectDetailPage.useResizable[resizable]\": ()=>{\n                setIsChatCollapsed(true);\n            }\n        }[\"ProjectDetailPage.useResizable[resizable]\"]\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-screen overflow-hidden\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_3__.SidebarProvider, {\n            defaultOpen: true,\n            style: {\n                \"--sidebar-width\": currentSidebarWidth,\n                \"--sidebar-width-mobile\": \"18rem\",\n                \"--sidebar-width-icon\": \"5rem\",\n                transition: \"all 0.3s ease-in-out\"\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_project_sidebar__WEBPACK_IMPORTED_MODULE_2__.ProjectSidebar, {\n                    projectId: projectId,\n                    chatWidth: chatWidth,\n                    setChatWidth: handleChatWidthChange,\n                    isChatCollapsed: isChatCollapsed,\n                    setIsChatCollapsed: setIsChatCollapsed,\n                    selectedBusinessItem: selectedItem,\n                    showDescription: !!selectedItem,\n                    onBackToProject: handleBackToItems,\n                    resizeHandle: {\n                        onMouseDown: resizable.handleMouseDown,\n                        isDragging: resizable.isDragging\n                    }\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/app/projects/[id]/page.tsx\",\n                    lineNumber: 135,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_3__.SidebarInset, {\n                    className: \"flex-1 flex flex-col h-screen overflow-hidden\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_project__WEBPACK_IMPORTED_MODULE_1__.ProjectHeader, {\n                            activeContent: activeContent,\n                            setActiveContent: setActiveContent,\n                            selectedBusinessItem: selectedItem,\n                            onBackToItems: handleBackToItems\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/app/projects/[id]/page.tsx\",\n                            lineNumber: 150,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_project__WEBPACK_IMPORTED_MODULE_1__.ProjectMainContent, {\n                            activeContent: activeContent,\n                            setActiveContent: setActiveContent,\n                            mockDraftItems: mockDraftItems,\n                            mockFileItems: mockFileItems,\n                            selectedItem: selectedItem,\n                            itemDetails: itemDetails,\n                            sections: sections,\n                            isLoading: isLoading,\n                            error: error,\n                            onBusinessItemClick: handleBusinessItemClick,\n                            onBackToItems: handleBackToItems\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/app/projects/[id]/page.tsx\",\n                            lineNumber: 157,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"fixed bottom-0 h-16 bg-gradient-to-t from-gray-50 via-gray-50/80 to-transparent dark:from-gray-900 dark:via-gray-900/80 dark:to-transparent pointer-events-none z-10\",\n                            style: {\n                                left: \"var(--sidebar-width, 45vw)\",\n                                right: \"0\"\n                            }\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/app/projects/[id]/page.tsx\",\n                            lineNumber: 172,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"fixed bottom-0 h-8 backdrop-blur-sm pointer-events-none z-10\",\n                            style: {\n                                left: \"var(--sidebar-width, 45vw)\",\n                                right: \"0\"\n                            }\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/app/projects/[id]/page.tsx\",\n                            lineNumber: 179,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/app/projects/[id]/page.tsx\",\n                    lineNumber: 149,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Data/new era/siift-next/src/app/projects/[id]/page.tsx\",\n            lineNumber: 124,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Data/new era/siift-next/src/app/projects/[id]/page.tsx\",\n        lineNumber: 123,\n        columnNumber: 5\n    }, this);\n}\n_s(ProjectDetailPage, \"ZRFTZ7FQRtw8sqsm2d34g9VD/0U=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_4__.useParams,\n        _stores_businessSectionStore__WEBPACK_IMPORTED_MODULE_9__.useBusinessSectionStore,\n        _stores_businessItemStore__WEBPACK_IMPORTED_MODULE_8__.useBusinessItemStore,\n        _hooks_useResizable__WEBPACK_IMPORTED_MODULE_6__.useResizable\n    ];\n});\n_c = ProjectDetailPage;\nvar _c;\n$RefreshReg$(_c, \"ProjectDetailPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvcHJvamVjdHMvW2lkXS9wYWdlLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7OztBQUV5RTtBQUNYO0FBQ1U7QUFDNUI7QUFDQTtBQUNlO0FBQ3FCO0FBQ1A7QUFDTTtBQUUvRSxZQUFZO0FBQ1osTUFBTVksaUJBQWlCO0lBQ3JCO1FBQ0VDLElBQUk7UUFDSkMsT0FBTztRQUNQQyxRQUFRO1FBQ1JDLGNBQWM7SUFDaEI7SUFDQTtRQUFFSCxJQUFJO1FBQUdDLE9BQU87UUFBZ0JDLFFBQVE7UUFBU0MsY0FBYztJQUFZO0lBQzNFO1FBQ0VILElBQUk7UUFDSkMsT0FBTztRQUNQQyxRQUFRO1FBQ1JDLGNBQWM7SUFDaEI7Q0FDRDtBQUVELHVCQUF1QjtBQUN2QixNQUFNQyxnQkFBZ0I7SUFDcEI7UUFBRUosSUFBSTtRQUFHQyxPQUFPO1FBQVlJLE1BQU07UUFBU0MsTUFBTTtJQUFPO0lBQ3hEO1FBQUVOLElBQUk7UUFBR0MsT0FBTztRQUFrQkksTUFBTTtRQUFVQyxNQUFNO0lBQVE7SUFDaEU7UUFBRU4sSUFBSTtRQUFHQyxPQUFPO1FBQW9CSSxNQUFNO1FBQVlDLE1BQU07SUFBUTtJQUNwRTtRQUFFTixJQUFJO1FBQUdDLE9BQU87UUFBa0JJLE1BQU07UUFBWUMsTUFBTTtJQUFRO0NBQ25FO0FBRWMsU0FBU0M7O0lBQ3RCLE1BQU1DLFNBQVNoQiwwREFBU0E7SUFDeEIsTUFBTWlCLFlBQVlELE9BQU9SLEVBQUU7SUFDM0IsTUFBTSxDQUFDVSxlQUFlQyxpQkFBaUIsR0FBR2pCLCtDQUFRQSxDQUNoRDtJQUVGLE1BQU0sQ0FBQ2tCLFdBQVdDLGFBQWEsR0FBR25CLCtDQUFRQSxDQUFnQjtJQUMxRCxNQUFNLENBQUNvQixpQkFBaUJDLG1CQUFtQixHQUFHckIsK0NBQVFBLENBQUM7SUFDdkQsTUFBTSxDQUFDc0IsY0FBY0MsZ0JBQWdCLEdBQUd2QiwrQ0FBUUEsQ0FBQztJQUVqRCxxQ0FBcUM7SUFDckMsTUFBTXdCLHdCQUF3QixDQUFDQztRQUM3Qk4sYUFBYU07UUFDYkYsZ0JBQWdCRSxVQUFVLFFBQVEsU0FBUztJQUM3QztJQUVBLE1BQU0sRUFBRUMsUUFBUSxFQUFFQyxTQUFTLEVBQUVDLEtBQUssRUFBRUMsV0FBVyxFQUFFQyxVQUFVLEVBQUVDLFFBQVEsRUFBRSxHQUNyRTNCLHFGQUF1QkE7SUFDekIsTUFBTSxFQUFFNEIsWUFBWSxFQUFFQyxXQUFXLEVBQUVDLGVBQWUsRUFBRUMsY0FBYyxFQUFFLEdBQ2xFaEMsK0VBQW9CQTtJQUV0QiwrQ0FBK0M7SUFDL0MsTUFBTWlDLHNCQUFzQkosZUFBZSxTQUFTVjtJQUVwRCw2Q0FBNkM7SUFDN0N2QixnREFBU0E7dUNBQUM7WUFDUm1DLGdCQUFnQjtZQUNoQkMsZUFBZSxFQUFFO1FBQ25CO3NDQUFHO1FBQUNEO1FBQWlCQztLQUFlO0lBRXBDLGtDQUFrQztJQUNsQ3BDLGdEQUFTQTt1Q0FBQztZQUNSLE1BQU1zQztvRUFBdUI7b0JBQzNCLElBQUk7d0JBQ0ZQLFdBQVc7d0JBQ1hDLFNBQVM7d0JBQ1QsTUFBTU8sT0FBTyxNQUFNcEMsc0ZBQXdCQSxDQUFDYTt3QkFDNUNjLFlBQVlTO29CQUNkLEVBQUUsT0FBT0MsS0FBSzt3QkFDWlIsU0FBUzt3QkFDVFMsUUFBUVosS0FBSyxDQUFDLG9DQUFvQ1c7b0JBQ3BELFNBQVU7d0JBQ1JULFdBQVc7b0JBQ2I7Z0JBQ0Y7O1lBRUFPO1FBQ0Y7c0NBQUc7UUFBQ1I7UUFBYUM7UUFBWUM7S0FBUztJQUV0QyxvREFBb0Q7SUFDcEQsTUFBTVUsMEJBQTBCLENBQUNDO1FBQy9CUixnQkFBZ0JRO1FBQ2hCLGlGQUFpRjtRQUNqRlAsZUFBZSxFQUFFO0lBQ25CO0lBRUEsdUJBQXVCO0lBQ3ZCLE1BQU1RLG9CQUFvQjtRQUN4QlQsZ0JBQWdCO0lBQ2xCO0lBRUEsdUJBQXVCO0lBQ3ZCLE1BQU1VLFlBQVkzQyxpRUFBWUEsQ0FBQztRQUM3QjRDLGNBQWN2QjtRQUNkd0IsaUJBQWlCO1FBQ2pCQyxpQkFBaUI7UUFDakJDLGFBQWE7eURBQUUsQ0FBQ3ZCO2dCQUNkLHVDQUF1QztnQkFDdkMsTUFBTXdCLGVBQWVDLFdBQVd6QixNQUFNMEIsT0FBTyxDQUFDLEtBQUs7Z0JBQ25ELE1BQU1DLFVBQVUsR0FBZ0IsT0FBYkgsY0FBYTtnQkFDaEMxQixnQkFBZ0I2QjtnQkFFaEIseURBQXlEO2dCQUN6RCxJQUFJSCxnQkFBZ0IsSUFBSTtvQkFDdEI5QixhQUFhO2dCQUNmLE9BQU87b0JBQ0xBLGFBQWE7Z0JBQ2Y7WUFDRjs7UUFDQWtDLFVBQVU7eURBQUU7Z0JBQ1ZoQyxtQkFBbUI7WUFDckI7O0lBQ0Y7SUFFQSxxQkFDRSw4REFBQ2lDO1FBQUlDLFdBQVU7a0JBQ2IsNEVBQUMxRCxtRUFBZUE7WUFDZDJELGFBQWE7WUFDYkMsT0FDRTtnQkFDRSxtQkFBbUJyQjtnQkFDbkIsMEJBQTBCO2dCQUMxQix3QkFBd0I7Z0JBQ3hCc0IsWUFBWTtZQUNkOzs4QkFHRiw4REFBQy9ELHVFQUFjQTtvQkFDYm9CLFdBQVdBO29CQUNYRyxXQUFXQTtvQkFDWEMsY0FBY0s7b0JBQ2RKLGlCQUFpQkE7b0JBQ2pCQyxvQkFBb0JBO29CQUNwQnNDLHNCQUFzQjNCO29CQUN0QjRCLGlCQUFpQixDQUFDLENBQUM1QjtvQkFDbkI2QixpQkFBaUJsQjtvQkFDakJtQixjQUFjO3dCQUNaQyxhQUFhbkIsVUFBVW9CLGVBQWU7d0JBQ3RDQyxZQUFZckIsVUFBVXFCLFVBQVU7b0JBQ2xDOzs7Ozs7OEJBRUYsOERBQUNyRSxnRUFBWUE7b0JBQUMyRCxXQUFVOztzQ0FDdEIsOERBQUM5RCw4REFBYUE7NEJBQ1p1QixlQUFlQTs0QkFDZkMsa0JBQWtCQTs0QkFDbEIwQyxzQkFBc0IzQjs0QkFDdEJrQyxlQUFldkI7Ozs7OztzQ0FHakIsOERBQUNqRCxtRUFBa0JBOzRCQUNqQnNCLGVBQWVBOzRCQUNmQyxrQkFBa0JBOzRCQUNsQlosZ0JBQWdCQTs0QkFDaEJLLGVBQWVBOzRCQUNmc0IsY0FBY0E7NEJBQ2RDLGFBQWFBOzRCQUNiUCxVQUFVQTs0QkFDVkMsV0FBV0E7NEJBQ1hDLE9BQU9BOzRCQUNQdUMscUJBQXFCMUI7NEJBQ3JCeUIsZUFBZXZCOzs7Ozs7c0NBSWpCLDhEQUFDVzs0QkFDQ0MsV0FBVTs0QkFDVkUsT0FBTztnQ0FDTFcsTUFBTztnQ0FDUEMsT0FBTzs0QkFDVDs7Ozs7O3NDQUVGLDhEQUFDZjs0QkFDQ0MsV0FBVTs0QkFDVkUsT0FBTztnQ0FDTFcsTUFBTztnQ0FDUEMsT0FBTzs0QkFDVDs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFNWjtHQXhKd0J4RDs7UUFDUGYsc0RBQVNBO1FBZ0J0Qk0saUZBQXVCQTtRQUV2QkQsMkVBQW9CQTtRQTJDSkYsNkRBQVlBOzs7S0E5RFJZIiwic291cmNlcyI6WyIvVXNlcnMvYXJlZi9EYXRhL25ldyBlcmEvc2lpZnQtbmV4dC9zcmMvYXBwL3Byb2plY3RzL1tpZF0vcGFnZS50c3giXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCI7XG5cbmltcG9ydCB7IFByb2plY3RIZWFkZXIsIFByb2plY3RNYWluQ29udGVudCB9IGZyb20gXCJAL2NvbXBvbmVudHMvcHJvamVjdFwiO1xuaW1wb3J0IHsgUHJvamVjdFNpZGViYXIgfSBmcm9tIFwiQC9jb21wb25lbnRzL3Byb2plY3Qtc2lkZWJhclwiO1xuaW1wb3J0IHsgU2lkZWJhckluc2V0LCBTaWRlYmFyUHJvdmlkZXIgfSBmcm9tIFwiQC9jb21wb25lbnRzL3VpL3NpZGViYXJcIjtcbmltcG9ydCB7IHVzZVBhcmFtcyB9IGZyb20gXCJuZXh0L25hdmlnYXRpb25cIjtcbmltcG9ydCB7IHVzZUVmZmVjdCwgdXNlU3RhdGUgfSBmcm9tIFwicmVhY3RcIjtcbmltcG9ydCB7IHVzZVJlc2l6YWJsZSB9IGZyb20gXCIuLi8uLi8uLi9ob29rcy91c2VSZXNpemFibGVcIjtcbmltcG9ydCB7IGZldGNoQnVzaW5lc3NTZWN0aW9uc05ldyB9IGZyb20gXCIuLi8uLi8uLi9saWIvYnVzaW5lc3NTZWN0aW9uc0RhdGFOZXdcIjtcbmltcG9ydCB7IHVzZUJ1c2luZXNzSXRlbVN0b3JlIH0gZnJvbSBcIi4uLy4uLy4uL3N0b3Jlcy9idXNpbmVzc0l0ZW1TdG9yZVwiO1xuaW1wb3J0IHsgdXNlQnVzaW5lc3NTZWN0aW9uU3RvcmUgfSBmcm9tIFwiLi4vLi4vLi4vc3RvcmVzL2J1c2luZXNzU2VjdGlvblN0b3JlXCI7XG5cbi8vIE1vY2sgZGF0YVxuY29uc3QgbW9ja0RyYWZ0SXRlbXMgPSBbXG4gIHtcbiAgICBpZDogMSxcbiAgICB0aXRsZTogXCJQcm9qZWN0IHByb3Bvc2FsXCIsXG4gICAgc3RhdHVzOiBcImRyYWZ0XCIsXG4gICAgbGFzdE1vZGlmaWVkOiBcIjIgaG91cnMgYWdvXCIsXG4gIH0sXG4gIHsgaWQ6IDIsIHRpdGxlOiBcIkRlc2lnbiBicmllZlwiLCBzdGF0dXM6IFwiZHJhZnRcIiwgbGFzdE1vZGlmaWVkOiBcIjEgZGF5IGFnb1wiIH0sXG4gIHtcbiAgICBpZDogMyxcbiAgICB0aXRsZTogXCJUZWNobmljYWwgc3BlY3NcIixcbiAgICBzdGF0dXM6IFwiZHJhZnRcIixcbiAgICBsYXN0TW9kaWZpZWQ6IFwiMyBkYXlzIGFnb1wiLFxuICB9LFxuXTtcblxuLy8gTW9jayBmaWxlIGl0ZW1zIGRhdGFcbmNvbnN0IG1vY2tGaWxlSXRlbXMgPSBbXG4gIHsgaWQ6IDEsIHRpdGxlOiBcImxvZ28uc3ZnXCIsIHR5cGU6IFwiaW1hZ2VcIiwgc2l6ZTogXCIyNEtCXCIgfSxcbiAgeyBpZDogMiwgdGl0bGU6IFwid2lyZWZyYW1lcy5maWdcIiwgdHlwZTogXCJkZXNpZ25cIiwgc2l6ZTogXCIxLjJNQlwiIH0sXG4gIHsgaWQ6IDMsIHRpdGxlOiBcInJlcXVpcmVtZW50cy5wZGZcIiwgdHlwZTogXCJkb2N1bWVudFwiLCBzaXplOiBcIjE1NktCXCIgfSxcbiAgeyBpZDogNCwgdGl0bGU6IFwic3R5bGVndWlkZS5wZGZcIiwgdHlwZTogXCJkb2N1bWVudFwiLCBzaXplOiBcIjIuMU1CXCIgfSxcbl07XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFByb2plY3REZXRhaWxQYWdlKCkge1xuICBjb25zdCBwYXJhbXMgPSB1c2VQYXJhbXMoKTtcbiAgY29uc3QgcHJvamVjdElkID0gcGFyYW1zLmlkO1xuICBjb25zdCBbYWN0aXZlQ29udGVudCwgc2V0QWN0aXZlQ29udGVudF0gPSB1c2VTdGF0ZTxcImRyYWZ0c1wiIHwgXCJmaWxlc1wiIHwgbnVsbD4oXG4gICAgbnVsbFxuICApO1xuICBjb25zdCBbY2hhdFdpZHRoLCBzZXRDaGF0V2lkdGhdID0gdXNlU3RhdGU8XCI0NSVcIiB8IFwiNDUlXCI+KFwiNDUlXCIpO1xuICBjb25zdCBbaXNDaGF0Q29sbGFwc2VkLCBzZXRJc0NoYXRDb2xsYXBzZWRdID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbc2lkZWJhcldpZHRoLCBzZXRTaWRlYmFyV2lkdGhdID0gdXNlU3RhdGUoXCI0NXZ3XCIpO1xuXG4gIC8vIFN5bmMgc2lkZWJhciB3aWR0aCB3aXRoIGNoYXQgd2lkdGhcbiAgY29uc3QgaGFuZGxlQ2hhdFdpZHRoQ2hhbmdlID0gKHdpZHRoOiBcIjQ1JVwiIHwgXCI0NSVcIikgPT4ge1xuICAgIHNldENoYXRXaWR0aCh3aWR0aCk7XG4gICAgc2V0U2lkZWJhcldpZHRoKHdpZHRoID09PSBcIjQ1JVwiID8gXCI0NXZ3XCIgOiBcIjQ1dndcIik7XG4gIH07XG5cbiAgY29uc3QgeyBzZWN0aW9ucywgaXNMb2FkaW5nLCBlcnJvciwgc2V0U2VjdGlvbnMsIHNldExvYWRpbmcsIHNldEVycm9yIH0gPVxuICAgIHVzZUJ1c2luZXNzU2VjdGlvblN0b3JlKCk7XG4gIGNvbnN0IHsgc2VsZWN0ZWRJdGVtLCBpdGVtRGV0YWlscywgc2V0U2VsZWN0ZWRJdGVtLCBzZXRJdGVtRGV0YWlscyB9ID1cbiAgICB1c2VCdXNpbmVzc0l0ZW1TdG9yZSgpO1xuXG4gIC8vIER5bmFtaWMgc2lkZWJhciB3aWR0aCBiYXNlZCBvbiBzZWxlY3RlZCBpdGVtXG4gIGNvbnN0IGN1cnJlbnRTaWRlYmFyV2lkdGggPSBzZWxlY3RlZEl0ZW0gPyBcIjMwdndcIiA6IHNpZGViYXJXaWR0aDtcblxuICAvLyBSZXNldCB0byBkZWZhdWx0IHZpZXcgb24gcGFnZSBsb2FkL3JlZnJlc2hcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBzZXRTZWxlY3RlZEl0ZW0obnVsbCk7XG4gICAgc2V0SXRlbURldGFpbHMoW10pO1xuICB9LCBbc2V0U2VsZWN0ZWRJdGVtLCBzZXRJdGVtRGV0YWlsc10pO1xuXG4gIC8vIExvYWQgYnVzaW5lc3Mgc2VjdGlvbnMgb24gbW91bnRcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBjb25zdCBsb2FkQnVzaW5lc3NTZWN0aW9ucyA9IGFzeW5jICgpID0+IHtcbiAgICAgIHRyeSB7XG4gICAgICAgIHNldExvYWRpbmcodHJ1ZSk7XG4gICAgICAgIHNldEVycm9yKG51bGwpO1xuICAgICAgICBjb25zdCBkYXRhID0gYXdhaXQgZmV0Y2hCdXNpbmVzc1NlY3Rpb25zTmV3KHByb2plY3RJZCBhcyBzdHJpbmcpO1xuICAgICAgICBzZXRTZWN0aW9ucyhkYXRhKTtcbiAgICAgIH0gY2F0Y2ggKGVycikge1xuICAgICAgICBzZXRFcnJvcihcIkZhaWxlZCB0byBsb2FkIGJ1c2luZXNzIHNlY3Rpb25zXCIpO1xuICAgICAgICBjb25zb2xlLmVycm9yKFwiRXJyb3IgbG9hZGluZyBidXNpbmVzcyBzZWN0aW9uczpcIiwgZXJyKTtcbiAgICAgIH0gZmluYWxseSB7XG4gICAgICAgIHNldExvYWRpbmcoZmFsc2UpO1xuICAgICAgfVxuICAgIH07XG5cbiAgICBsb2FkQnVzaW5lc3NTZWN0aW9ucygpO1xuICB9LCBbc2V0U2VjdGlvbnMsIHNldExvYWRpbmcsIHNldEVycm9yXSk7XG5cbiAgLy8gSGFuZGxlIGJ1c2luZXNzIGl0ZW0gc2VsZWN0aW9uIC0gc2hvdyBkZXRhaWwgdmlld1xuICBjb25zdCBoYW5kbGVCdXNpbmVzc0l0ZW1DbGljayA9IChpdGVtOiBhbnkpID0+IHtcbiAgICBzZXRTZWxlY3RlZEl0ZW0oaXRlbSk7XG4gICAgLy8gQ2xlYXIgaXRlbSBkZXRhaWxzIHNpbmNlIHdlJ3JlIHVzaW5nIHRoZSBlbmhhbmNlZCB2aWV3IHdpdGggY29tcHJlaGVuc2l2ZSBkYXRhXG4gICAgc2V0SXRlbURldGFpbHMoW10pO1xuICB9O1xuXG4gIC8vIEhhbmRsZSBiYWNrIHRvIGl0ZW1zXG4gIGNvbnN0IGhhbmRsZUJhY2tUb0l0ZW1zID0gKCkgPT4ge1xuICAgIHNldFNlbGVjdGVkSXRlbShudWxsKTtcbiAgfTtcblxuICAvLyBSZXNpemUgZnVuY3Rpb25hbGl0eVxuICBjb25zdCByZXNpemFibGUgPSB1c2VSZXNpemFibGUoe1xuICAgIGluaXRpYWxXaWR0aDogc2lkZWJhcldpZHRoLFxuICAgIG1pbldpZHRoUGVyY2VudDogMTAsXG4gICAgbWF4V2lkdGhQZXJjZW50OiA3MCxcbiAgICBvbldpZHRoQ2hhbmdlOiAod2lkdGgpID0+IHtcbiAgICAgIC8vIENvbnZlcnQgcGVyY2VudGFnZSB0byB2aWV3cG9ydCB3aWR0aFxuICAgICAgY29uc3Qgd2lkdGhQZXJjZW50ID0gcGFyc2VGbG9hdCh3aWR0aC5yZXBsYWNlKFwiJVwiLCBcIlwiKSk7XG4gICAgICBjb25zdCB2d1dpZHRoID0gYCR7d2lkdGhQZXJjZW50fXZ3YDtcbiAgICAgIHNldFNpZGViYXJXaWR0aCh2d1dpZHRoKTtcblxuICAgICAgLy8gVXBkYXRlIGNoYXRXaWR0aCB0byBtYXRjaCBzaWRlYmFyV2lkdGggZm9yIGNvbnNpc3RlbmN5XG4gICAgICBpZiAod2lkdGhQZXJjZW50IDw9IDcwKSB7XG4gICAgICAgIHNldENoYXRXaWR0aChcIjQ1JVwiKTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIHNldENoYXRXaWR0aChcIjQ1JVwiKTtcbiAgICAgIH1cbiAgICB9LFxuICAgIG9uQ29sbGFwc2U6ICgpID0+IHtcbiAgICAgIHNldElzQ2hhdENvbGxhcHNlZCh0cnVlKTtcbiAgICB9LFxuICB9KTtcblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwiaC1zY3JlZW4gb3ZlcmZsb3ctaGlkZGVuXCI+XG4gICAgICA8U2lkZWJhclByb3ZpZGVyXG4gICAgICAgIGRlZmF1bHRPcGVuPXt0cnVlfVxuICAgICAgICBzdHlsZT17XG4gICAgICAgICAge1xuICAgICAgICAgICAgXCItLXNpZGViYXItd2lkdGhcIjogY3VycmVudFNpZGViYXJXaWR0aCxcbiAgICAgICAgICAgIFwiLS1zaWRlYmFyLXdpZHRoLW1vYmlsZVwiOiBcIjE4cmVtXCIsXG4gICAgICAgICAgICBcIi0tc2lkZWJhci13aWR0aC1pY29uXCI6IFwiNXJlbVwiLFxuICAgICAgICAgICAgdHJhbnNpdGlvbjogXCJhbGwgMC4zcyBlYXNlLWluLW91dFwiLFxuICAgICAgICAgIH0gYXMgUmVhY3QuQ1NTUHJvcGVydGllc1xuICAgICAgICB9XG4gICAgICA+XG4gICAgICAgIDxQcm9qZWN0U2lkZWJhclxuICAgICAgICAgIHByb2plY3RJZD17cHJvamVjdElkIGFzIHN0cmluZ31cbiAgICAgICAgICBjaGF0V2lkdGg9e2NoYXRXaWR0aH1cbiAgICAgICAgICBzZXRDaGF0V2lkdGg9e2hhbmRsZUNoYXRXaWR0aENoYW5nZX1cbiAgICAgICAgICBpc0NoYXRDb2xsYXBzZWQ9e2lzQ2hhdENvbGxhcHNlZH1cbiAgICAgICAgICBzZXRJc0NoYXRDb2xsYXBzZWQ9e3NldElzQ2hhdENvbGxhcHNlZH1cbiAgICAgICAgICBzZWxlY3RlZEJ1c2luZXNzSXRlbT17c2VsZWN0ZWRJdGVtfVxuICAgICAgICAgIHNob3dEZXNjcmlwdGlvbj17ISFzZWxlY3RlZEl0ZW19XG4gICAgICAgICAgb25CYWNrVG9Qcm9qZWN0PXtoYW5kbGVCYWNrVG9JdGVtc31cbiAgICAgICAgICByZXNpemVIYW5kbGU9e3tcbiAgICAgICAgICAgIG9uTW91c2VEb3duOiByZXNpemFibGUuaGFuZGxlTW91c2VEb3duLFxuICAgICAgICAgICAgaXNEcmFnZ2luZzogcmVzaXphYmxlLmlzRHJhZ2dpbmcsXG4gICAgICAgICAgfX1cbiAgICAgICAgLz5cbiAgICAgICAgPFNpZGViYXJJbnNldCBjbGFzc05hbWU9XCJmbGV4LTEgZmxleCBmbGV4LWNvbCBoLXNjcmVlbiBvdmVyZmxvdy1oaWRkZW5cIj5cbiAgICAgICAgICA8UHJvamVjdEhlYWRlclxuICAgICAgICAgICAgYWN0aXZlQ29udGVudD17YWN0aXZlQ29udGVudH1cbiAgICAgICAgICAgIHNldEFjdGl2ZUNvbnRlbnQ9e3NldEFjdGl2ZUNvbnRlbnR9XG4gICAgICAgICAgICBzZWxlY3RlZEJ1c2luZXNzSXRlbT17c2VsZWN0ZWRJdGVtfVxuICAgICAgICAgICAgb25CYWNrVG9JdGVtcz17aGFuZGxlQmFja1RvSXRlbXN9XG4gICAgICAgICAgLz5cblxuICAgICAgICAgIDxQcm9qZWN0TWFpbkNvbnRlbnRcbiAgICAgICAgICAgIGFjdGl2ZUNvbnRlbnQ9e2FjdGl2ZUNvbnRlbnR9XG4gICAgICAgICAgICBzZXRBY3RpdmVDb250ZW50PXtzZXRBY3RpdmVDb250ZW50fVxuICAgICAgICAgICAgbW9ja0RyYWZ0SXRlbXM9e21vY2tEcmFmdEl0ZW1zfVxuICAgICAgICAgICAgbW9ja0ZpbGVJdGVtcz17bW9ja0ZpbGVJdGVtc31cbiAgICAgICAgICAgIHNlbGVjdGVkSXRlbT17c2VsZWN0ZWRJdGVtfVxuICAgICAgICAgICAgaXRlbURldGFpbHM9e2l0ZW1EZXRhaWxzfVxuICAgICAgICAgICAgc2VjdGlvbnM9e3NlY3Rpb25zfVxuICAgICAgICAgICAgaXNMb2FkaW5nPXtpc0xvYWRpbmd9XG4gICAgICAgICAgICBlcnJvcj17ZXJyb3J9XG4gICAgICAgICAgICBvbkJ1c2luZXNzSXRlbUNsaWNrPXtoYW5kbGVCdXNpbmVzc0l0ZW1DbGlja31cbiAgICAgICAgICAgIG9uQmFja1RvSXRlbXM9e2hhbmRsZUJhY2tUb0l0ZW1zfVxuICAgICAgICAgIC8+XG5cbiAgICAgICAgICB7LyogQm90dG9tIGZhZGUgZWZmZWN0IHRvIGluZGljYXRlIG1vcmUgY29udGVudCAtIGZpeGVkIGF0IGJvdHRvbSBvZiB2aWV3cG9ydCAqL31cbiAgICAgICAgICA8ZGl2XG4gICAgICAgICAgICBjbGFzc05hbWU9XCJmaXhlZCBib3R0b20tMCBoLTE2IGJnLWdyYWRpZW50LXRvLXQgZnJvbS1ncmF5LTUwIHZpYS1ncmF5LTUwLzgwIHRvLXRyYW5zcGFyZW50IGRhcms6ZnJvbS1ncmF5LTkwMCBkYXJrOnZpYS1ncmF5LTkwMC84MCBkYXJrOnRvLXRyYW5zcGFyZW50IHBvaW50ZXItZXZlbnRzLW5vbmUgei0xMFwiXG4gICAgICAgICAgICBzdHlsZT17e1xuICAgICAgICAgICAgICBsZWZ0OiBgdmFyKC0tc2lkZWJhci13aWR0aCwgNDV2dylgLFxuICAgICAgICAgICAgICByaWdodDogXCIwXCIsXG4gICAgICAgICAgICB9fVxuICAgICAgICAgIC8+XG4gICAgICAgICAgPGRpdlxuICAgICAgICAgICAgY2xhc3NOYW1lPVwiZml4ZWQgYm90dG9tLTAgaC04IGJhY2tkcm9wLWJsdXItc20gcG9pbnRlci1ldmVudHMtbm9uZSB6LTEwXCJcbiAgICAgICAgICAgIHN0eWxlPXt7XG4gICAgICAgICAgICAgIGxlZnQ6IGB2YXIoLS1zaWRlYmFyLXdpZHRoLCA0NXZ3KWAsXG4gICAgICAgICAgICAgIHJpZ2h0OiBcIjBcIixcbiAgICAgICAgICAgIH19XG4gICAgICAgICAgLz5cbiAgICAgICAgPC9TaWRlYmFySW5zZXQ+XG4gICAgICA8L1NpZGViYXJQcm92aWRlcj5cbiAgICA8L2Rpdj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJQcm9qZWN0SGVhZGVyIiwiUHJvamVjdE1haW5Db250ZW50IiwiUHJvamVjdFNpZGViYXIiLCJTaWRlYmFySW5zZXQiLCJTaWRlYmFyUHJvdmlkZXIiLCJ1c2VQYXJhbXMiLCJ1c2VFZmZlY3QiLCJ1c2VTdGF0ZSIsInVzZVJlc2l6YWJsZSIsImZldGNoQnVzaW5lc3NTZWN0aW9uc05ldyIsInVzZUJ1c2luZXNzSXRlbVN0b3JlIiwidXNlQnVzaW5lc3NTZWN0aW9uU3RvcmUiLCJtb2NrRHJhZnRJdGVtcyIsImlkIiwidGl0bGUiLCJzdGF0dXMiLCJsYXN0TW9kaWZpZWQiLCJtb2NrRmlsZUl0ZW1zIiwidHlwZSIsInNpemUiLCJQcm9qZWN0RGV0YWlsUGFnZSIsInBhcmFtcyIsInByb2plY3RJZCIsImFjdGl2ZUNvbnRlbnQiLCJzZXRBY3RpdmVDb250ZW50IiwiY2hhdFdpZHRoIiwic2V0Q2hhdFdpZHRoIiwiaXNDaGF0Q29sbGFwc2VkIiwic2V0SXNDaGF0Q29sbGFwc2VkIiwic2lkZWJhcldpZHRoIiwic2V0U2lkZWJhcldpZHRoIiwiaGFuZGxlQ2hhdFdpZHRoQ2hhbmdlIiwid2lkdGgiLCJzZWN0aW9ucyIsImlzTG9hZGluZyIsImVycm9yIiwic2V0U2VjdGlvbnMiLCJzZXRMb2FkaW5nIiwic2V0RXJyb3IiLCJzZWxlY3RlZEl0ZW0iLCJpdGVtRGV0YWlscyIsInNldFNlbGVjdGVkSXRlbSIsInNldEl0ZW1EZXRhaWxzIiwiY3VycmVudFNpZGViYXJXaWR0aCIsImxvYWRCdXNpbmVzc1NlY3Rpb25zIiwiZGF0YSIsImVyciIsImNvbnNvbGUiLCJoYW5kbGVCdXNpbmVzc0l0ZW1DbGljayIsIml0ZW0iLCJoYW5kbGVCYWNrVG9JdGVtcyIsInJlc2l6YWJsZSIsImluaXRpYWxXaWR0aCIsIm1pbldpZHRoUGVyY2VudCIsIm1heFdpZHRoUGVyY2VudCIsIm9uV2lkdGhDaGFuZ2UiLCJ3aWR0aFBlcmNlbnQiLCJwYXJzZUZsb2F0IiwicmVwbGFjZSIsInZ3V2lkdGgiLCJvbkNvbGxhcHNlIiwiZGl2IiwiY2xhc3NOYW1lIiwiZGVmYXVsdE9wZW4iLCJzdHlsZSIsInRyYW5zaXRpb24iLCJzZWxlY3RlZEJ1c2luZXNzSXRlbSIsInNob3dEZXNjcmlwdGlvbiIsIm9uQmFja1RvUHJvamVjdCIsInJlc2l6ZUhhbmRsZSIsIm9uTW91c2VEb3duIiwiaGFuZGxlTW91c2VEb3duIiwiaXNEcmFnZ2luZyIsIm9uQmFja1RvSXRlbXMiLCJvbkJ1c2luZXNzSXRlbUNsaWNrIiwibGVmdCIsInJpZ2h0Il0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/projects/[id]/page.tsx\n"));

/***/ })

});