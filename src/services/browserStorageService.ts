// Browser storage service for persisting business item data
import type { BusinessItemDetail } from "@/types/BusinessSection.types";

const STORAGE_KEYS = {
  BUSINESS_ITEMS: 'siift_business_items',
  BUSINESS_ITEM_DETAILS: 'siift_business_item_details',
  LAST_UPDATED: 'siift_last_updated',
} as const;

export interface StoredBusinessItemDetail extends BusinessItemDetail {
  projectId?: string;
  lastModified: string;
}

export interface StorageData {
  [itemId: string]: StoredBusinessItemDetail[];
}

class BrowserStorageService {
  private isClient = typeof window !== 'undefined';

  // Get all stored business item details
  getAllItemDetails(): StorageData {
    if (!this.isClient) return {};
    
    try {
      const stored = localStorage.getItem(STORAGE_KEYS.BUSINESS_ITEM_DETAILS);
      return stored ? JSON.parse(stored) : {};
    } catch (error) {
      console.error('Error reading from localStorage:', error);
      return {};
    }
  }

  // Get details for a specific business item
  getItemDetails(itemId: string, projectId?: string): StoredBusinessItemDetail[] {
    const allData = this.getAllItemDetails();
    const key = projectId ? `${projectId}_${itemId}` : itemId;
    return allData[key] || [];
  }

  // Save details for a specific business item
  saveItemDetails(
    itemId: string, 
    details: BusinessItemDetail[], 
    projectId?: string
  ): void {
    if (!this.isClient) return;

    try {
      const allData = this.getAllItemDetails();
      const key = projectId ? `${projectId}_${itemId}` : itemId;
      
      // Convert to stored format with timestamps
      const storedDetails: StoredBusinessItemDetail[] = details.map(detail => ({
        ...detail,
        projectId,
        lastModified: new Date().toISOString(),
      }));

      allData[key] = storedDetails;
      
      localStorage.setItem(STORAGE_KEYS.BUSINESS_ITEM_DETAILS, JSON.stringify(allData));
      localStorage.setItem(STORAGE_KEYS.LAST_UPDATED, new Date().toISOString());
    } catch (error) {
      console.error('Error saving to localStorage:', error);
    }
  }

  // Add a new detail item
  addItemDetail(
    itemId: string, 
    detail: Omit<BusinessItemDetail, 'id'>, 
    projectId?: string
  ): BusinessItemDetail {
    const newDetail: BusinessItemDetail = {
      ...detail,
      id: `${itemId}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    const existingDetails = this.getItemDetails(itemId, projectId);
    const updatedDetails = [...existingDetails, newDetail];
    
    this.saveItemDetails(itemId, updatedDetails, projectId);
    return newDetail;
  }

  // Update an existing detail item
  updateItemDetail(
    itemId: string, 
    detailId: string, 
    updates: Partial<BusinessItemDetail>,
    projectId?: string
  ): boolean {
    const existingDetails = this.getItemDetails(itemId, projectId);
    const detailIndex = existingDetails.findIndex(d => d.id === detailId);
    
    if (detailIndex === -1) return false;

    const updatedDetails = [...existingDetails];
    updatedDetails[detailIndex] = {
      ...updatedDetails[detailIndex],
      ...updates,
      updatedAt: new Date().toISOString(),
    };

    this.saveItemDetails(itemId, updatedDetails, projectId);
    return true;
  }

  // Delete a detail item
  deleteItemDetail(itemId: string, detailId: string, projectId?: string): boolean {
    const existingDetails = this.getItemDetails(itemId, projectId);
    const filteredDetails = existingDetails.filter(d => d.id !== detailId);
    
    if (filteredDetails.length === existingDetails.length) return false;

    this.saveItemDetails(itemId, filteredDetails, projectId);
    return true;
  }

  // Clear all data for a specific item
  clearItemDetails(itemId: string, projectId?: string): void {
    if (!this.isClient) return;

    try {
      const allData = this.getAllItemDetails();
      const key = projectId ? `${projectId}_${itemId}` : itemId;
      delete allData[key];
      
      localStorage.setItem(STORAGE_KEYS.BUSINESS_ITEM_DETAILS, JSON.stringify(allData));
      localStorage.setItem(STORAGE_KEYS.LAST_UPDATED, new Date().toISOString());
    } catch (error) {
      console.error('Error clearing localStorage:', error);
    }
  }

  // Clear all stored data
  clearAllData(): void {
    if (!this.isClient) return;

    try {
      localStorage.removeItem(STORAGE_KEYS.BUSINESS_ITEM_DETAILS);
      localStorage.removeItem(STORAGE_KEYS.LAST_UPDATED);
    } catch (error) {
      console.error('Error clearing all localStorage:', error);
    }
  }

  // Get storage statistics
  getStorageStats(): {
    totalItems: number;
    totalDetails: number;
    storageSize: number;
    lastUpdated: string | null;
  } {
    if (!this.isClient) {
      return { totalItems: 0, totalDetails: 0, storageSize: 0, lastUpdated: null };
    }

    try {
      const allData = this.getAllItemDetails();
      const totalItems = Object.keys(allData).length;
      const totalDetails = Object.values(allData).reduce((sum, details) => sum + details.length, 0);
      
      const dataString = localStorage.getItem(STORAGE_KEYS.BUSINESS_ITEM_DETAILS) || '';
      const storageSize = new Blob([dataString]).size;
      
      const lastUpdated = localStorage.getItem(STORAGE_KEYS.LAST_UPDATED);

      return {
        totalItems,
        totalDetails,
        storageSize,
        lastUpdated,
      };
    } catch (error) {
      console.error('Error getting storage stats:', error);
      return { totalItems: 0, totalDetails: 0, storageSize: 0, lastUpdated: null };
    }
  }

  // Export data for backup
  exportData(): string {
    const allData = this.getAllItemDetails();
    const stats = this.getStorageStats();
    
    return JSON.stringify({
      version: '1.0',
      exportDate: new Date().toISOString(),
      stats,
      data: allData,
    }, null, 2);
  }

  // Import data from backup
  importData(jsonData: string): boolean {
    if (!this.isClient) return false;

    try {
      const importedData = JSON.parse(jsonData);
      
      if (!importedData.data || typeof importedData.data !== 'object') {
        throw new Error('Invalid data format');
      }

      localStorage.setItem(STORAGE_KEYS.BUSINESS_ITEM_DETAILS, JSON.stringify(importedData.data));
      localStorage.setItem(STORAGE_KEYS.LAST_UPDATED, new Date().toISOString());
      
      return true;
    } catch (error) {
      console.error('Error importing data:', error);
      return false;
    }
  }

  // Check if data exists for an item
  hasItemDetails(itemId: string, projectId?: string): boolean {
    const details = this.getItemDetails(itemId, projectId);
    return details.length > 0;
  }

  // Get items that have been modified recently
  getRecentlyModified(hours: number = 24): { itemId: string; projectId?: string; lastModified: string }[] {
    const allData = this.getAllItemDetails();
    const cutoffTime = new Date(Date.now() - hours * 60 * 60 * 1000);
    const recentItems: { itemId: string; projectId?: string; lastModified: string }[] = [];

    Object.entries(allData).forEach(([key, details]) => {
      const latestDetail = details.reduce((latest, current) => {
        return new Date(current.lastModified) > new Date(latest.lastModified) ? current : latest;
      });

      if (new Date(latestDetail.lastModified) > cutoffTime) {
        const [projectId, itemId] = key.includes('_') ? key.split('_') : [undefined, key];
        recentItems.push({
          itemId: itemId || key,
          projectId,
          lastModified: latestDetail.lastModified,
        });
      }
    });

    return recentItems.sort((a, b) => 
      new Date(b.lastModified).getTime() - new Date(a.lastModified).getTime()
    );
  }
}

// Export singleton instance
export const browserStorageService = new BrowserStorageService();

// Export convenience functions
export const {
  getAllItemDetails,
  getItemDetails,
  saveItemDetails,
  addItemDetail,
  updateItemDetail,
  deleteItemDetail,
  clearItemDetails,
  clearAllData,
  getStorageStats,
  exportData,
  importData,
  hasItemDetails,
  getRecentlyModified,
} = browserStorageService;
