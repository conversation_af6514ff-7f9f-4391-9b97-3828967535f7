# Production Environment Variables for Railway
# Set these in your Railway project's environment variables

# Node Environment
NODE_ENV=production
NEXT_TELEMETRY_DISABLED=1

# Build Configuration
ESLINT_NO_DEV_ERRORS=true
TYPESCRIPT_NO_BUILD_ERRORS=true

# JWT Secret
JWT_SECRET=your-super-secret-jwt-key-change-in-production

# API Configuration
NEXT_PUBLIC_API_URL=/api
NEXT_PUBLIC_ADMIN_API_URL=https://siift-backend.up.railway.app

# Application Configuration
NEXT_PUBLIC_APP_NAME=Siift
NEXT_PUBLIC_APP_URL=https://siift.up.railway.app

# Admin Configuration
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=123456789@
