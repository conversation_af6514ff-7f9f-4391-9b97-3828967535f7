/**
 * Mock data for business item questions and answers
 * Each business item type has specific questions and default answers
 */

import { businessItemsData } from "@/data/businessItemsData";

export interface BusinessItemQA {
  question: string;
  defaultAnswer: string;
  getAnswer?: (item: any) => string;
}

export const BUSINESS_ITEM_QUESTIONS: Record<string, BusinessItemQA> = {
  // Problem-related items
  problem: {
    question: "What specific problem are we trying to solve?",
    defaultAnswer:
      "Define the core problem that needs to be addressed and its impact on the business.",
    getAnswer: (item) =>
      item?.title
        ? `Problem identified: ${item.title}`
        : "Define the core problem that needs to be addressed and its impact on the business.",
  },

  // Audience-related items
  audience: {
    question: "Who is our target audience for this initiative?",
    defaultAnswer:
      "Identify the specific user groups, demographics, and stakeholders who will be affected.",
    getAnswer: (item) =>
      item?.title
        ? `Target audience: ${item.title}`
        : "Identify the specific user groups, demographics, and stakeholders who will be affected.",
  },

  // Alternatives-related items
  alternatives: {
    question: "What alternative solutions have we considered?",
    defaultAnswer:
      "List and evaluate different approaches, technologies, or strategies that could address this need.",
    getAnswer: (item) =>
      item?.title
        ? `Alternative approach: ${item.title}`
        : "List and evaluate different approaches, technologies, or strategies that could address this need.",
  },

  // Solution-related items
  solution: {
    question: "What is our proposed solution approach?",
    defaultAnswer:
      "Describe the recommended solution, its key features, and implementation strategy.",
    getAnswer: (item) =>
      item?.title
        ? `Proposed solution: ${item.title}`
        : "Describe the recommended solution, its key features, and implementation strategy.",
  },

  // Market-related items
  market: {
    question: "What market opportunity does this address?",
    defaultAnswer:
      "Define the market size, competitive landscape, and business opportunity.",
    getAnswer: (item) =>
      item?.title
        ? `Market opportunity: ${item.title}`
        : "Define the market size, competitive landscape, and business opportunity.",
  },

  // Technology-related items
  technology: {
    question: "What technology stack and architecture will we use?",
    defaultAnswer:
      "Outline the technical requirements, tools, platforms, and infrastructure needed.",
    getAnswer: (item) =>
      item?.title
        ? `Technology approach: ${item.title}`
        : "Outline the technical requirements, tools, platforms, and infrastructure needed.",
  },

  // Timeline-related items
  timeline: {
    question: "What is our project timeline and key milestones?",
    defaultAnswer:
      "Define project phases, deadlines, dependencies, and critical path items.",
    getAnswer: (item) =>
      item?.title
        ? `Timeline focus: ${item.title}`
        : "Define project phases, deadlines, dependencies, and critical path items.",
  },

  // Budget-related items
  budget: {
    question: "What are the financial requirements and ROI expectations?",
    defaultAnswer:
      "Specify budget allocation, cost breakdown, and expected return on investment.",
    getAnswer: (item) =>
      item?.title
        ? `Budget consideration: ${item.title}`
        : "Specify budget allocation, cost breakdown, and expected return on investment.",
  },

  // Risk-related items
  risk: {
    question: "What are the key risks and mitigation strategies?",
    defaultAnswer:
      "Identify potential risks, their impact, probability, and mitigation plans.",
    getAnswer: (item) =>
      item?.title
        ? `Risk factor: ${item.title}`
        : "Identify potential risks, their impact, probability, and mitigation plans.",
  },

  // Success metrics
  metrics: {
    question: "How will we measure success and track progress?",
    defaultAnswer:
      "Define KPIs, success criteria, measurement methods, and reporting frequency.",
    getAnswer: (item) =>
      item?.title
        ? `Success metric: ${item.title}`
        : "Define KPIs, success criteria, measurement methods, and reporting frequency.",
  },

  // Team and resources
  team: {
    question: "What team structure and resources do we need?",
    defaultAnswer:
      "Specify roles, responsibilities, skill requirements, and resource allocation.",
    getAnswer: (item) =>
      item?.title
        ? `Team focus: ${item.title}`
        : "Specify roles, responsibilities, skill requirements, and resource allocation.",
  },

  // Default fallback for any other item types
  default: {
    question: "What is the main objective of this business item?",
    defaultAnswer:
      "Define the core purpose and expected outcomes for this business initiative.",
    getAnswer: (item) =>
      item?.title
        ? `This item focuses on: ${item.title}`
        : "Define the core purpose and expected outcomes for this business initiative.",
  },
};

/**
 * Helper function to get question and answer for a business item
 * @param item - The business item object
 * @returns Object with question and answer
 */
export function getBusinessItemQA(item: any): {
  question: string;
  answer: string;
} {
  if (!item) {
    return {
      question: BUSINESS_ITEM_QUESTIONS.default.question,
      answer: BUSINESS_ITEM_QUESTIONS.default.defaultAnswer,
    };
  }

  // First, try to get comprehensive data from businessItemsData
  const comprehensiveData = businessItemsData.find(
    (dataItem) => dataItem.id === item.id
  );

  if (comprehensiveData) {
    // Use the comprehensive data with project name replacement
    const question =
      comprehensiveData.question?.replace("{PROJECT NAME}", "siift") ||
      comprehensiveData.title;
    const answer =
      comprehensiveData.description || `Working on: ${comprehensiveData.title}`;

    return {
      question,
      answer,
    };
  }

  // Fallback to the old logic if no comprehensive data found
  const itemType = determineItemType(item);
  const qaData =
    BUSINESS_ITEM_QUESTIONS[itemType] || BUSINESS_ITEM_QUESTIONS.default;

  return {
    question: qaData.question,
    answer: qaData.getAnswer ? qaData.getAnswer(item) : qaData.defaultAnswer,
  };
}

/**
 * Determine the type of business item based on its properties
 * @param item - The business item object
 * @returns The item type string
 */
function determineItemType(item: any): string {
  if (!item?.title) return "default";

  const title = item.title.toLowerCase();

  // Check for keywords in the title to determine type
  if (
    title.includes("problem") ||
    title.includes("issue") ||
    title.includes("challenge")
  ) {
    return "problem";
  }
  if (
    title.includes("audience") ||
    title.includes("user") ||
    title.includes("customer")
  ) {
    return "audience";
  }
  if (
    title.includes("alternative") ||
    title.includes("option") ||
    title.includes("approach")
  ) {
    return "alternatives";
  }
  if (
    title.includes("solution") ||
    title.includes("resolve") ||
    title.includes("fix")
  ) {
    return "solution";
  }
  if (
    title.includes("market") ||
    title.includes("opportunity") ||
    title.includes("competition")
  ) {
    return "market";
  }
  if (
    title.includes("technology") ||
    title.includes("tech") ||
    title.includes("platform")
  ) {
    return "technology";
  }
  if (
    title.includes("timeline") ||
    title.includes("schedule") ||
    title.includes("deadline")
  ) {
    return "timeline";
  }
  if (
    title.includes("budget") ||
    title.includes("cost") ||
    title.includes("financial")
  ) {
    return "budget";
  }
  if (
    title.includes("risk") ||
    title.includes("threat") ||
    title.includes("danger")
  ) {
    return "risk";
  }
  if (
    title.includes("metric") ||
    title.includes("kpi") ||
    title.includes("measure")
  ) {
    return "metrics";
  }
  if (
    title.includes("team") ||
    title.includes("resource") ||
    title.includes("staff")
  ) {
    return "team";
  }

  return "default";
}
