# Siift.ai Product Requirements Document (PRD)

## Overview
Siift.ai is a Next.js-based project management and collaboration platform that helps users transform ideas into structured projects through AI-powered assistance. The platform features an intuitive dashboard, project creation workflows, and comprehensive project management tools designed for founders, entrepreneurs, and teams at various stages of their journey.

## Core Features

### 1. Authentication & User Management
- **JWT-based authentication** with remember me functionality
- **Clerk integration** for social logins (Google, Apple)
- **Admin panel** with dedicated authentication (<EMAIL>/123456789@)
- **Referral system** with code validation and mock error handling
- **Password reset flow** with streamlined single-page experience
- **Conditional form fields** (referral code toggle, confirm password)

### 2. Project Creation & Management
- **AI-powered project creation** from natural language descriptions
- **User type-casting questionnaire** to personalize experience
- **Real-time progress tracking** with Socket.IO event streaming
- **Project completion animations** with expanding circle transitions
- **Project dashboard** with actions, ideas, drafts, and files organization
- **Task management** with priority, status, and description tracking

### 3. Dashboard & Navigation
- **Unified dashboard** with hero section and project grid
- **Responsive sidebar** with collapsible navigation
- **Breadcrumb navigation** with in-page project list
- **Theme switching** (light/dark mode) with consistent styling
- **Mobile-responsive design** with adaptive layouts

### 4. UI/UX Design System
- **shadcn/ui components** with consistent styling patterns
- **Card-based layouts** with hover effects and borders
- **BGPattern component** with dots variant and fade-edges mask
- **Loading indicators** (simple circles, max 64px)
- **Toast notifications** with proper color coding
- **Sticky headers** with blur and noise effects

### 5. Admin Panel
- **Analytics dashboard** with charts and data visualization
- **Feedback management** with advanced filtering and pagination
- **Collapsible sidebar** with expandable sub-items
- **API data display** using well-formatted UI components
- **User and project management** capabilities

## Technical Architecture

### Frontend Stack
- **Next.js 14** with App Router and TypeScript
- **React 18** with modern hooks and patterns
- **Tailwind CSS** for styling with custom design tokens
- **Framer Motion** for animations and transitions
- **Zustand** for state management
- **React Hook Form** with Zod validation
- **Clerk** for authentication
- **Socket.IO** for real-time communication

### Backend Integration
- **API routes** in Next.js for server-side logic
- **External backend** on port 3002 with CORS configuration
- **Mock data** for development and testing
- **JWT token management** for secure API communication

### State Management
- **Project creation store** with Zustand for creation flow
- **Authentication context** with Clerk integration
- **Theme management** with next-themes
- **Form state** with React Hook Form

### Styling & Design
- **Design tokens** centralized for consistent theming
- **Component variants** for different use cases
- **Responsive breakpoints** for mobile-first design
- **Animation presets** for consistent motion design

## User Experience

### User Personas
1. **First-time Founders** - Need guidance and structure
2. **Experienced Builders** - Want efficiency and advanced features
3. **Teams** - Require collaboration and project sharing
4. **Administrators** - Need oversight and analytics

### Key User Flows

#### Project Creation Flow
1. User enters project idea in natural language
2. System presents type-casting questionnaire
3. Background project creation with progress tracking
4. Completion animation and project dashboard redirect

#### Dashboard Experience
1. Landing on unified dashboard with hero section
2. Viewing project grid with status indicators
3. Accessing project details with sidebar navigation
4. Managing tasks, actions, and ideas

#### Authentication Flow
1. Streamlined sign-in/sign-up with social options
2. Referral code handling with validation
3. Password reset with single-page experience
4. Automatic redirection to dashboard

### UI/UX Considerations
- **Consistent design patterns** across all pages
- **Responsive layouts** that work on all devices
- **Accessibility compliance** with proper ARIA labels
- **Performance optimization** with lazy loading
- **Error handling** with user-friendly messages

## Development Roadmap

### Phase 1: Foundation (MVP)
- [ ] Core authentication system with Clerk
- [ ] Basic project creation and management
- [ ] Dashboard with project listing
- [ ] Responsive design implementation
- [ ] Admin panel basic functionality

### Phase 2: Enhanced UX
- [ ] Project creation questionnaire
- [ ] Advanced animations and transitions
- [ ] Real-time features with Socket.IO
- [ ] Comprehensive task management
- [ ] Mobile optimization

### Phase 3: Advanced Features
- [ ] AI-powered project insights
- [ ] Team collaboration features
- [ ] Advanced analytics dashboard
- [ ] API integrations and webhooks
- [ ] Performance monitoring

### Phase 4: Scale & Polish
- [ ] Advanced admin capabilities
- [ ] Multi-tenant architecture
- [ ] Advanced security features
- [ ] Performance optimizations
- [ ] Comprehensive testing suite

## Logical Dependency Chain

### Foundation Layer
1. **Authentication system** - Required for all user features
2. **Basic routing and navigation** - Core app structure
3. **Design system components** - UI foundation
4. **State management setup** - Data flow architecture

### Feature Layer
1. **Project creation flow** - Core value proposition
2. **Dashboard implementation** - User home base
3. **Project management** - Task and content organization
4. **Admin panel** - Administrative oversight

### Enhancement Layer
1. **Real-time features** - Live updates and collaboration
2. **Advanced animations** - Polish and user delight
3. **Mobile optimization** - Cross-platform accessibility
4. **Performance optimization** - Scale and reliability

## Configuration & Deployment

### Environment Setup
- **Development**: Local with mock data and hot reload
- **Staging**: Railway deployment with test data
- **Production**: Railway with production database and CDN

### Key Configuration Files
- `next.config.ts` - Next.js configuration
- `tailwind.config.ts` - Design system configuration
- `tsconfig.json` - TypeScript configuration
- `package.json` - Dependencies and scripts

### Deployment Strategy
- **Docker containerization** for consistent environments
- **Railway deployment** with automatic builds
- **Environment variables** for configuration management
- **CI/CD pipeline** for automated testing and deployment

## Risks and Mitigations

### Technical Challenges
- **Real-time synchronization** - Implement robust Socket.IO handling
- **State management complexity** - Use Zustand for predictable state
- **Performance with animations** - Optimize with Framer Motion best practices

### User Experience Risks
- **Complex onboarding** - Implement progressive disclosure
- **Mobile responsiveness** - Mobile-first design approach
- **Accessibility compliance** - Regular accessibility audits

### Development Risks
- **Scope creep** - Strict MVP definition and phased approach
- **Technical debt** - Regular refactoring and code reviews
- **Integration complexity** - Modular architecture with clear interfaces

## Success Metrics

### User Engagement
- Project creation completion rate
- Daily active users
- Session duration and page views
- Feature adoption rates

### Technical Performance
- Page load times under 2 seconds
- 99.9% uptime
- Error rates below 1%
- Mobile performance scores above 90

### Business Metrics
- User retention rates
- Project completion rates
- Admin panel usage
- API response times

## Appendix

### Design Preferences Summary
- Next.js with App Router and TypeScript
- shadcn/ui components with consistent styling
- Dark green theme colors (#166534)
- Card-based layouts with hover effects
- Responsive sidebar navigation
- BGPattern with dots and fade-edges
- Sticky headers with blur effects
- Mobile-first responsive design

### API Endpoints
- Authentication: `/auth/signin`, `/auth/signup`
- Projects: `/api/projects` (GET, POST)
- Admin: `/admin/analytics`, `/admin/feedback`
- Real-time: Socket.IO on port 3002

### Key Dependencies
- Next.js 14, React 18, TypeScript
- Tailwind CSS, Framer Motion
- Clerk, Zustand, React Hook Form
- Socket.IO, Zod, Lucide React
