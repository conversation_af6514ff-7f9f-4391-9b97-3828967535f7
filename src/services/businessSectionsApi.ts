// API service layer for business sections
import type { BusinessSection, BusinessItem } from "@/types/BusinessSection.types";
import type { BusinessItemData } from "@/types/BusinessItemData.types";
import { mockBusinessSectionsNew } from "@/lib/businessSectionsDataNew";

// API Configuration
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || "http://localhost:3002";
const API_TIMEOUT = 10000; // 10 seconds

// Error types
export class ApiError extends Error {
  constructor(
    message: string,
    public status: number,
    public code?: string
  ) {
    super(message);
    this.name = "ApiError";
  }
}

// Request wrapper with error handling
async function apiRequest<T>(
  endpoint: string,
  options: RequestInit = {}
): Promise<T> {
  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), API_TIMEOUT);

  try {
    const response = await fetch(`${API_BASE_URL}${endpoint}`, {
      ...options,
      signal: controller.signal,
      headers: {
        "Content-Type": "application/json",
        ...options.headers,
      },
    });

    clearTimeout(timeoutId);

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new ApiError(
        errorData.message || `HTTP ${response.status}: ${response.statusText}`,
        response.status,
        errorData.code
      );
    }

    return await response.json();
  } catch (error) {
    clearTimeout(timeoutId);
    
    if (error instanceof ApiError) {
      throw error;
    }
    
    if (error instanceof Error) {
      if (error.name === "AbortError") {
        throw new ApiError("Request timeout", 408, "TIMEOUT");
      }
      throw new ApiError(error.message, 0, "NETWORK_ERROR");
    }
    
    throw new ApiError("Unknown error occurred", 0, "UNKNOWN_ERROR");
  }
}

// Business Sections API
export class BusinessSectionsApi {
  // Fetch all business sections for a project
  static async fetchSections(projectId: string): Promise<BusinessSection[]> {
    try {
      // For now, return mock data with simulated delay
      // Replace this with actual API call when backend is ready
      return new Promise((resolve) => {
        setTimeout(() => {
          resolve(mockBusinessSectionsNew);
        }, 500);
      });

      // Uncomment when API is ready:
      // return await apiRequest<BusinessSection[]>(`/projects/${projectId}/business-sections`);
    } catch (error) {
      console.error("Failed to fetch business sections:", error);
      throw error;
    }
  }

  // Fetch a specific business item
  static async fetchItem(projectId: string, itemId: string): Promise<BusinessItem> {
    try {
      // Mock implementation
      const sections = await this.fetchSections(projectId);
      const item = sections
        .flatMap(section => section.items)
        .find(item => item.id === itemId);
      
      if (!item) {
        throw new ApiError("Business item not found", 404, "ITEM_NOT_FOUND");
      }
      
      return item;

      // Uncomment when API is ready:
      // return await apiRequest<BusinessItem>(`/projects/${projectId}/business-sections/items/${itemId}`);
    } catch (error) {
      console.error("Failed to fetch business item:", error);
      throw error;
    }
  }

  // Update a business item
  static async updateItem(
    projectId: string,
    itemId: string,
    updates: Partial<BusinessItem>
  ): Promise<BusinessItem> {
    try {
      // Mock implementation - in real app, this would update the backend
      const item = await this.fetchItem(projectId, itemId);
      const updatedItem = { ...item, ...updates };
      
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 300));
      
      return updatedItem;

      // Uncomment when API is ready:
      // return await apiRequest<BusinessItem>(
      //   `/projects/${projectId}/business-sections/items/${itemId}`,
      //   {
      //     method: "PATCH",
      //     body: JSON.stringify(updates),
      //   }
      // );
    } catch (error) {
      console.error("Failed to update business item:", error);
      throw error;
    }
  }

  // Create a new business item
  static async createItem(
    projectId: string,
    sectionId: string,
    item: Omit<BusinessItem, "id">
  ): Promise<BusinessItem> {
    try {
      // Mock implementation
      const newItem: BusinessItem = {
        ...item,
        id: `item-${Date.now()}`, // Generate temporary ID
      };
      
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 300));
      
      return newItem;

      // Uncomment when API is ready:
      // return await apiRequest<BusinessItem>(
      //   `/projects/${projectId}/business-sections/${sectionId}/items`,
      //   {
      //     method: "POST",
      //     body: JSON.stringify(item),
      //   }
      // );
    } catch (error) {
      console.error("Failed to create business item:", error);
      throw error;
    }
  }

  // Delete a business item
  static async deleteItem(projectId: string, itemId: string): Promise<void> {
    try {
      // Mock implementation
      await new Promise(resolve => setTimeout(resolve, 300));

      // Uncomment when API is ready:
      // await apiRequest<void>(
      //   `/projects/${projectId}/business-sections/items/${itemId}`,
      //   {
      //     method: "DELETE",
      //   }
      // );
    } catch (error) {
      console.error("Failed to delete business item:", error);
      throw error;
    }
  }

  // Bulk update multiple items
  static async bulkUpdateItems(
    projectId: string,
    updates: Array<{ itemId: string; updates: Partial<BusinessItem> }>
  ): Promise<BusinessItem[]> {
    try {
      // Mock implementation
      const updatedItems: BusinessItem[] = [];
      
      for (const { itemId, updates: itemUpdates } of updates) {
        const updatedItem = await this.updateItem(projectId, itemId, itemUpdates);
        updatedItems.push(updatedItem);
      }
      
      return updatedItems;

      // Uncomment when API is ready:
      // return await apiRequest<BusinessItem[]>(
      //   `/projects/${projectId}/business-sections/items/bulk-update`,
      //   {
      //     method: "PATCH",
      //     body: JSON.stringify({ updates }),
      //   }
      // );
    } catch (error) {
      console.error("Failed to bulk update business items:", error);
      throw error;
    }
  }

  // Get business sections analytics
  static async getAnalytics(projectId: string): Promise<{
    totalItems: number;
    completedItems: number;
    completionPercentage: number;
    categoryBreakdown: Record<string, { total: number; completed: number }>;
  }> {
    try {
      const sections = await this.fetchSections(projectId);
      const allItems = sections.flatMap(section => section.items);
      
      const totalItems = allItems.length;
      const completedItems = allItems.filter(item => item.status === "confirmed").length;
      const completionPercentage = totalItems > 0 ? (completedItems / totalItems) * 100 : 0;
      
      const categoryBreakdown: Record<string, { total: number; completed: number }> = {};
      
      sections.forEach(section => {
        const total = section.items.length;
        const completed = section.items.filter(item => item.status === "confirmed").length;
        categoryBreakdown[section.title] = { total, completed };
      });

      return {
        totalItems,
        completedItems,
        completionPercentage,
        categoryBreakdown,
      };

      // Uncomment when API is ready:
      // return await apiRequest<any>(`/projects/${projectId}/business-sections/analytics`);
    } catch (error) {
      console.error("Failed to fetch business sections analytics:", error);
      throw error;
    }
  }
}

// Export convenience functions
export const {
  fetchSections,
  fetchItem,
  updateItem,
  createItem,
  deleteItem,
  bulkUpdateItems,
  getAnalytics,
} = BusinessSectionsApi;
