"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/projects/[id]/page",{

/***/ "(app-pages-browser)/./src/mockdata/businessItemQuestions.ts":
/*!***********************************************!*\
  !*** ./src/mockdata/businessItemQuestions.ts ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BUSINESS_ITEM_QUESTIONS: () => (/* binding */ BUSINESS_ITEM_QUESTIONS),\n/* harmony export */   getBusinessItemQA: () => (/* binding */ getBusinessItemQA)\n/* harmony export */ });\n/**\n * Mock data for business item questions and answers\n * Each business item type has specific questions and default answers\n */ const BUSINESS_ITEM_QUESTIONS = {\n    // Problem-related items\n    problem: {\n        question: \"What specific problem are we trying to solve?\",\n        defaultAnswer: \"Define the core problem that needs to be addressed and its impact on the business.\",\n        getAnswer: (item)=>(item === null || item === void 0 ? void 0 : item.title) ? \"Problem identified: \".concat(item.title) : \"Define the core problem that needs to be addressed and its impact on the business.\"\n    },\n    // Audience-related items\n    audience: {\n        question: \"Who is our target audience for this initiative?\",\n        defaultAnswer: \"Identify the specific user groups, demographics, and stakeholders who will be affected.\",\n        getAnswer: (item)=>(item === null || item === void 0 ? void 0 : item.title) ? \"Target audience: \".concat(item.title) : \"Identify the specific user groups, demographics, and stakeholders who will be affected.\"\n    },\n    // Alternatives-related items\n    alternatives: {\n        question: \"What alternative solutions have we considered?\",\n        defaultAnswer: \"List and evaluate different approaches, technologies, or strategies that could address this need.\",\n        getAnswer: (item)=>(item === null || item === void 0 ? void 0 : item.title) ? \"Alternative approach: \".concat(item.title) : \"List and evaluate different approaches, technologies, or strategies that could address this need.\"\n    },\n    // Solution-related items\n    solution: {\n        question: \"What is our proposed solution approach?\",\n        defaultAnswer: \"Describe the recommended solution, its key features, and implementation strategy.\",\n        getAnswer: (item)=>(item === null || item === void 0 ? void 0 : item.title) ? \"Proposed solution: \".concat(item.title) : \"Describe the recommended solution, its key features, and implementation strategy.\"\n    },\n    // Market-related items\n    market: {\n        question: \"What market opportunity does this address?\",\n        defaultAnswer: \"Define the market size, competitive landscape, and business opportunity.\",\n        getAnswer: (item)=>(item === null || item === void 0 ? void 0 : item.title) ? \"Market opportunity: \".concat(item.title) : \"Define the market size, competitive landscape, and business opportunity.\"\n    },\n    // Technology-related items\n    technology: {\n        question: \"What technology stack and architecture will we use?\",\n        defaultAnswer: \"Outline the technical requirements, tools, platforms, and infrastructure needed.\",\n        getAnswer: (item)=>(item === null || item === void 0 ? void 0 : item.title) ? \"Technology approach: \".concat(item.title) : \"Outline the technical requirements, tools, platforms, and infrastructure needed.\"\n    },\n    // Timeline-related items\n    timeline: {\n        question: \"What is our project timeline and key milestones?\",\n        defaultAnswer: \"Define project phases, deadlines, dependencies, and critical path items.\",\n        getAnswer: (item)=>(item === null || item === void 0 ? void 0 : item.title) ? \"Timeline focus: \".concat(item.title) : \"Define project phases, deadlines, dependencies, and critical path items.\"\n    },\n    // Budget-related items\n    budget: {\n        question: \"What are the financial requirements and ROI expectations?\",\n        defaultAnswer: \"Specify budget allocation, cost breakdown, and expected return on investment.\",\n        getAnswer: (item)=>(item === null || item === void 0 ? void 0 : item.title) ? \"Budget consideration: \".concat(item.title) : \"Specify budget allocation, cost breakdown, and expected return on investment.\"\n    },\n    // Risk-related items\n    risk: {\n        question: \"What are the key risks and mitigation strategies?\",\n        defaultAnswer: \"Identify potential risks, their impact, probability, and mitigation plans.\",\n        getAnswer: (item)=>(item === null || item === void 0 ? void 0 : item.title) ? \"Risk factor: \".concat(item.title) : \"Identify potential risks, their impact, probability, and mitigation plans.\"\n    },\n    // Success metrics\n    metrics: {\n        question: \"How will we measure success and track progress?\",\n        defaultAnswer: \"Define KPIs, success criteria, measurement methods, and reporting frequency.\",\n        getAnswer: (item)=>(item === null || item === void 0 ? void 0 : item.title) ? \"Success metric: \".concat(item.title) : \"Define KPIs, success criteria, measurement methods, and reporting frequency.\"\n    },\n    // Team and resources\n    team: {\n        question: \"What team structure and resources do we need?\",\n        defaultAnswer: \"Specify roles, responsibilities, skill requirements, and resource allocation.\",\n        getAnswer: (item)=>(item === null || item === void 0 ? void 0 : item.title) ? \"Team focus: \".concat(item.title) : \"Specify roles, responsibilities, skill requirements, and resource allocation.\"\n    },\n    // Default fallback for any other item types\n    default: {\n        question: \"What is the main objective of this business item?\",\n        defaultAnswer: \"Define the core purpose and expected outcomes for this business initiative.\",\n        getAnswer: (item)=>(item === null || item === void 0 ? void 0 : item.title) ? \"This item focuses on: \".concat(item.title) : \"Define the core purpose and expected outcomes for this business initiative.\"\n    }\n};\n/**\n * Helper function to get question and answer for a business item\n * @param item - The business item object\n * @returns Object with question and answer\n */ function getBusinessItemQA(item) {\n    if (!item) {\n        return {\n            question: BUSINESS_ITEM_QUESTIONS.default.question,\n            answer: BUSINESS_ITEM_QUESTIONS.default.defaultAnswer\n        };\n    }\n    // Try to determine item type from title or other properties\n    const itemType = determineItemType(item);\n    const qaData = BUSINESS_ITEM_QUESTIONS[itemType] || BUSINESS_ITEM_QUESTIONS.default;\n    return {\n        question: qaData.question,\n        answer: qaData.getAnswer ? qaData.getAnswer(item) : qaData.defaultAnswer\n    };\n}\n/**\n * Determine the type of business item based on its properties\n * @param item - The business item object\n * @returns The item type string\n */ function determineItemType(item) {\n    if (!(item === null || item === void 0 ? void 0 : item.title)) return \"default\";\n    const title = item.title.toLowerCase();\n    // Check for keywords in the title to determine type\n    if (title.includes(\"problem\") || title.includes(\"issue\") || title.includes(\"challenge\")) {\n        return \"problem\";\n    }\n    if (title.includes(\"audience\") || title.includes(\"user\") || title.includes(\"customer\")) {\n        return \"audience\";\n    }\n    if (title.includes(\"alternative\") || title.includes(\"option\") || title.includes(\"approach\")) {\n        return \"alternatives\";\n    }\n    if (title.includes(\"solution\") || title.includes(\"resolve\") || title.includes(\"fix\")) {\n        return \"solution\";\n    }\n    if (title.includes(\"market\") || title.includes(\"opportunity\") || title.includes(\"competition\")) {\n        return \"market\";\n    }\n    if (title.includes(\"technology\") || title.includes(\"tech\") || title.includes(\"platform\")) {\n        return \"technology\";\n    }\n    if (title.includes(\"timeline\") || title.includes(\"schedule\") || title.includes(\"deadline\")) {\n        return \"timeline\";\n    }\n    if (title.includes(\"budget\") || title.includes(\"cost\") || title.includes(\"financial\")) {\n        return \"budget\";\n    }\n    if (title.includes(\"risk\") || title.includes(\"threat\") || title.includes(\"danger\")) {\n        return \"risk\";\n    }\n    if (title.includes(\"metric\") || title.includes(\"kpi\") || title.includes(\"measure\")) {\n        return \"metrics\";\n    }\n    if (title.includes(\"team\") || title.includes(\"resource\") || title.includes(\"staff\")) {\n        return \"team\";\n    }\n    return \"default\";\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/mockdata/businessItemQuestions.ts\n"));

/***/ })

});