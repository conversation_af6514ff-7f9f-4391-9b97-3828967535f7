"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/projects/[id]/page",{

/***/ "(app-pages-browser)/./src/stores/businessItemStoreEnhanced.ts":
/*!*************************************************!*\
  !*** ./src/stores/businessItemStoreEnhanced.ts ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkItemDependencies: () => (/* binding */ checkItemDependencies),\n/* harmony export */   getItemData: () => (/* binding */ getItemData),\n/* harmony export */   getRelatedItems: () => (/* binding */ getRelatedItems),\n/* harmony export */   useBusinessItemStoreEnhanced: () => (/* binding */ useBusinessItemStoreEnhanced)\n/* harmony export */ });\n/* harmony import */ var _data_businessItemsData__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/data/businessItemsData */ \"(app-pages-browser)/./src/data/businessItemsData.ts\");\n/* harmony import */ var _services_businessSectionsApi__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/services/businessSectionsApi */ \"(app-pages-browser)/./src/services/businessSectionsApi.ts\");\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! zustand */ \"(app-pages-browser)/./node_modules/zustand/esm/react.mjs\");\n/* harmony import */ var zustand_middleware__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! zustand/middleware */ \"(app-pages-browser)/./node_modules/zustand/esm/middleware.mjs\");\n/* harmony import */ var _services_browserStorageService__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../services/browserStorageService */ \"(app-pages-browser)/./src/services/browserStorageService.ts\");\n// Enhanced business item store using the new comprehensive data structure\n\n\n\n\n\nconst useBusinessItemStoreEnhanced = (0,zustand__WEBPACK_IMPORTED_MODULE_3__.create)()((0,zustand_middleware__WEBPACK_IMPORTED_MODULE_4__.devtools)((set, get)=>({\n        selectedItem: null,\n        itemDetails: [],\n        isLoading: false,\n        error: null,\n        setSelectedItem: (item)=>set({\n                selectedItem: item\n            }),\n        setItemDetails: (details)=>set({\n                itemDetails: details\n            }),\n        setLoading: (isLoading)=>set({\n                isLoading\n            }),\n        setError: (error)=>set({\n                error\n            }),\n        // Fetch comprehensive item details using the new data structure\n        fetchItemDetails: async function(itemId) {\n            let projectId = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"default\";\n            try {\n                var _itemData_question;\n                set({\n                    isLoading: true,\n                    error: null\n                });\n                // For now, always regenerate to see fresh data with sample values\n                console.log(\"Creating fresh initial data for\", itemId);\n                // If no stored details, create initial details from comprehensive data\n                const itemData = _data_businessItemsData__WEBPACK_IMPORTED_MODULE_0__.businessItemsData.find((item)=>item.id === itemId);\n                if (!itemData) {\n                    throw new Error(\"Item with ID \".concat(itemId, \" not found\"));\n                }\n                // Create initial detail items from sample values\n                const initialDetails = [];\n                // Add sample values as editable detail items for the table\n                if (itemData.values && itemData.values.length > 0) {\n                    itemData.values.forEach((value, index)=>{\n                        var _itemData_question;\n                        initialDetails.push({\n                            id: \"\".concat(itemData.id, \"-value-\").concat(index),\n                            title: \"Response \".concat(index + 1),\n                            status: \"idea\",\n                            actions: \"Sample response - edit to customize\",\n                            result: value,\n                            description: \"Sample response for: \".concat(((_itemData_question = itemData.question) === null || _itemData_question === void 0 ? void 0 : _itemData_question.replace(\"{PROJECT NAME}\", \"your project\")) || itemData.title),\n                            createdAt: new Date().toISOString(),\n                            updatedAt: new Date().toISOString()\n                        });\n                    });\n                }\n                // Add a blank item for new entries\n                initialDetails.push({\n                    id: \"\".concat(itemData.id, \"-new-entry\"),\n                    title: \"Add your response\",\n                    status: \"unproven\",\n                    actions: \"Click to edit and add your own analysis\",\n                    result: \"Enter your findings here...\",\n                    description: \"Your analysis for: \".concat(((_itemData_question = itemData.question) === null || _itemData_question === void 0 ? void 0 : _itemData_question.replace(\"{PROJECT NAME}\", \"your project\")) || itemData.title),\n                    createdAt: new Date().toISOString(),\n                    updatedAt: new Date().toISOString()\n                });\n                // Save initial details to browser storage\n                _services_browserStorageService__WEBPACK_IMPORTED_MODULE_2__.browserStorageService.saveItemDetails(itemId, initialDetails, projectId);\n                console.log(\"Created initial details for\", itemId, initialDetails);\n                set({\n                    itemDetails: initialDetails,\n                    isLoading: false\n                });\n            } catch (error) {\n                console.error(\"Error fetching item details:\", error);\n                set({\n                    error: error instanceof Error ? error.message : \"Failed to fetch item details\",\n                    isLoading: false\n                });\n            }\n        },\n        // Update item status with dependency validation\n        updateItemStatus: async function(itemId, status) {\n            let projectId = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : \"default\";\n            try {\n                set({\n                    isLoading: true,\n                    error: null\n                });\n                // Check dependencies before allowing status change to \"confirmed\"\n                if (status === \"confirmed\") {\n                    const canProgress = get().checkDependencies(itemId);\n                    if (!canProgress) {\n                        throw new Error(\"Cannot mark as confirmed: dependencies not satisfied\");\n                    }\n                }\n                // Use the API to update the item\n                await _services_businessSectionsApi__WEBPACK_IMPORTED_MODULE_1__.BusinessSectionsApi.updateItem(projectId, itemId, {\n                    status\n                });\n                // Update local state if we have the item selected\n                const { selectedItem } = get();\n                if (selectedItem && selectedItem.id === itemId) {\n                    set({\n                        selectedItem: {\n                            ...selectedItem,\n                            status\n                        }\n                    });\n                }\n                set({\n                    isLoading: false\n                });\n            } catch (error) {\n                console.error(\"Error updating item status:\", error);\n                set({\n                    error: error instanceof Error ? error.message : \"Failed to update item status\",\n                    isLoading: false\n                });\n            }\n        },\n        addItemDetail: (detail)=>{\n            const { selectedItem } = get();\n            if (!selectedItem) return;\n            const updatedDetails = [\n                ...get().itemDetails,\n                detail\n            ];\n            set({\n                itemDetails: updatedDetails\n            });\n            // Save to browser storage\n            _services_browserStorageService__WEBPACK_IMPORTED_MODULE_2__.browserStorageService.saveItemDetails(selectedItem.id, updatedDetails, \"default\");\n        },\n        updateItemDetail: (id, updates)=>{\n            const { selectedItem } = get();\n            if (!selectedItem) return;\n            const updatedDetails = get().itemDetails.map((item)=>item.id === id ? {\n                    ...item,\n                    ...updates,\n                    updatedAt: new Date().toISOString()\n                } : item);\n            set({\n                itemDetails: updatedDetails\n            });\n            // Save to browser storage\n            _services_browserStorageService__WEBPACK_IMPORTED_MODULE_2__.browserStorageService.saveItemDetails(selectedItem.id, updatedDetails, \"default\");\n        },\n        removeItemDetail: (id)=>{\n            const { selectedItem } = get();\n            if (!selectedItem) return;\n            const updatedDetails = get().itemDetails.filter((item)=>item.id !== id);\n            set({\n                itemDetails: updatedDetails\n            });\n            // Save to browser storage\n            _services_browserStorageService__WEBPACK_IMPORTED_MODULE_2__.browserStorageService.saveItemDetails(selectedItem.id, updatedDetails, \"default\");\n        },\n        // Get comprehensive item data\n        getItemData: (itemId)=>{\n            return _data_businessItemsData__WEBPACK_IMPORTED_MODULE_0__.businessItemsData.find((item)=>item.id === itemId);\n        },\n        // Check if item dependencies are satisfied\n        checkDependencies: (itemId)=>{\n            const itemData = _data_businessItemsData__WEBPACK_IMPORTED_MODULE_0__.businessItemsData.find((item)=>item.id === itemId);\n            if (!itemData || !itemData.dependencies || itemData.dependencies.length === 0) {\n                return true;\n            }\n            return itemData.dependencies.every((depId)=>{\n                const depItem = _data_businessItemsData__WEBPACK_IMPORTED_MODULE_0__.businessItemsData.find((dep)=>dep.id === depId);\n                return (depItem === null || depItem === void 0 ? void 0 : depItem.status) === \"confirmed\";\n            });\n        },\n        // Get related items (dependencies and dependents)\n        getRelatedItems: (itemId)=>{\n            var _itemData_dependencies;\n            const itemData = _data_businessItemsData__WEBPACK_IMPORTED_MODULE_0__.businessItemsData.find((item)=>item.id === itemId);\n            // Get dependencies\n            const dependencies = (itemData === null || itemData === void 0 ? void 0 : (_itemData_dependencies = itemData.dependencies) === null || _itemData_dependencies === void 0 ? void 0 : _itemData_dependencies.map((depId)=>_data_businessItemsData__WEBPACK_IMPORTED_MODULE_0__.businessItemsData.find((item)=>item.id === depId)).filter(Boolean)) || [];\n            // Get dependents (items that depend on this one)\n            const dependents = _data_businessItemsData__WEBPACK_IMPORTED_MODULE_0__.businessItemsData.filter((item)=>{\n                var _item_dependencies;\n                return (_item_dependencies = item.dependencies) === null || _item_dependencies === void 0 ? void 0 : _item_dependencies.includes(itemId);\n            });\n            return {\n                dependencies,\n                dependents\n            };\n        }\n    }), {\n    name: \"business-item-store-enhanced\"\n}));\n// Helper functions for easy access\nconst getItemData = (itemId)=>{\n    return _data_businessItemsData__WEBPACK_IMPORTED_MODULE_0__.businessItemsData.find((item)=>item.id === itemId);\n};\nconst checkItemDependencies = (itemId)=>{\n    const itemData = _data_businessItemsData__WEBPACK_IMPORTED_MODULE_0__.businessItemsData.find((item)=>item.id === itemId);\n    if (!itemData || !itemData.dependencies || itemData.dependencies.length === 0) {\n        return true;\n    }\n    return itemData.dependencies.every((depId)=>{\n        const depItem = _data_businessItemsData__WEBPACK_IMPORTED_MODULE_0__.businessItemsData.find((dep)=>dep.id === depId);\n        return (depItem === null || depItem === void 0 ? void 0 : depItem.status) === \"confirmed\";\n    });\n};\nconst getRelatedItems = (itemId)=>{\n    var _itemData_dependencies;\n    const itemData = _data_businessItemsData__WEBPACK_IMPORTED_MODULE_0__.businessItemsData.find((item)=>item.id === itemId);\n    const dependencies = (itemData === null || itemData === void 0 ? void 0 : (_itemData_dependencies = itemData.dependencies) === null || _itemData_dependencies === void 0 ? void 0 : _itemData_dependencies.map((depId)=>_data_businessItemsData__WEBPACK_IMPORTED_MODULE_0__.businessItemsData.find((item)=>item.id === depId)).filter(Boolean)) || [];\n    const dependents = _data_businessItemsData__WEBPACK_IMPORTED_MODULE_0__.businessItemsData.filter((item)=>{\n        var _item_dependencies;\n        return (_item_dependencies = item.dependencies) === null || _item_dependencies === void 0 ? void 0 : _item_dependencies.includes(itemId);\n    });\n    return {\n        dependencies,\n        dependents\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/stores/businessItemStoreEnhanced.ts\n"));

/***/ })

});