"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/projects/[id]/page",{

/***/ "(app-pages-browser)/./src/app/projects/[id]/page.tsx":
/*!****************************************!*\
  !*** ./src/app/projects/[id]/page.tsx ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProjectDetailPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_project__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/project */ \"(app-pages-browser)/./src/components/project/index.ts\");\n/* harmony import */ var _components_project_sidebar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/project-sidebar */ \"(app-pages-browser)/./src/components/project-sidebar.tsx\");\n/* harmony import */ var _components_ui_sidebar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/sidebar */ \"(app-pages-browser)/./src/components/ui/sidebar.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _hooks_useResizable__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../../hooks/useResizable */ \"(app-pages-browser)/./src/hooks/useResizable.ts\");\n/* harmony import */ var _lib_businessSectionsDataNew__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../../lib/businessSectionsDataNew */ \"(app-pages-browser)/./src/lib/businessSectionsDataNew.ts\");\n/* harmony import */ var _stores_businessItemStoreEnhanced__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../../../stores/businessItemStoreEnhanced */ \"(app-pages-browser)/./src/stores/businessItemStoreEnhanced.ts\");\n/* harmony import */ var _stores_businessSectionStore__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../../../stores/businessSectionStore */ \"(app-pages-browser)/./src/stores/businessSectionStore.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n// Mock data\nconst mockDraftItems = [\n    {\n        id: 1,\n        title: \"Project proposal\",\n        status: \"draft\",\n        lastModified: \"2 hours ago\"\n    },\n    {\n        id: 2,\n        title: \"Design brief\",\n        status: \"draft\",\n        lastModified: \"1 day ago\"\n    },\n    {\n        id: 3,\n        title: \"Technical specs\",\n        status: \"draft\",\n        lastModified: \"3 days ago\"\n    }\n];\n// Mock file items data\nconst mockFileItems = [\n    {\n        id: 1,\n        title: \"logo.svg\",\n        type: \"image\",\n        size: \"24KB\"\n    },\n    {\n        id: 2,\n        title: \"wireframes.fig\",\n        type: \"design\",\n        size: \"1.2MB\"\n    },\n    {\n        id: 3,\n        title: \"requirements.pdf\",\n        type: \"document\",\n        size: \"156KB\"\n    },\n    {\n        id: 4,\n        title: \"styleguide.pdf\",\n        type: \"document\",\n        size: \"2.1MB\"\n    }\n];\nfunction ProjectDetailPage() {\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.useParams)();\n    const projectId = params.id;\n    const [activeContent, setActiveContent] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(null);\n    const [chatWidth, setChatWidth] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(\"45%\");\n    const [isChatCollapsed, setIsChatCollapsed] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    const [sidebarWidth, setSidebarWidth] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(\"45vw\");\n    // Sync sidebar width with chat width\n    const handleChatWidthChange = (width)=>{\n        setChatWidth(width);\n        setSidebarWidth(width === \"45%\" ? \"45vw\" : \"45vw\");\n    };\n    const { sections, isLoading, error, setSections, setLoading, setError } = (0,_stores_businessSectionStore__WEBPACK_IMPORTED_MODULE_9__.useBusinessSectionStore)();\n    const { selectedItem, itemDetails, setSelectedItem, setItemDetails, fetchItemDetails } = (0,_stores_businessItemStoreEnhanced__WEBPACK_IMPORTED_MODULE_8__.useBusinessItemStoreEnhanced)();\n    // Dynamic sidebar width based on selected item\n    const currentSidebarWidth = selectedItem ? \"30vw\" : sidebarWidth;\n    // Reset to default view on page load/refresh\n    (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)({\n        \"ProjectDetailPage.useEffect\": ()=>{\n            setSelectedItem(null);\n            setItemDetails([]);\n        }\n    }[\"ProjectDetailPage.useEffect\"], [\n        setSelectedItem,\n        setItemDetails\n    ]);\n    // Load business sections on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)({\n        \"ProjectDetailPage.useEffect\": ()=>{\n            const loadBusinessSections = {\n                \"ProjectDetailPage.useEffect.loadBusinessSections\": async ()=>{\n                    try {\n                        setLoading(true);\n                        setError(null);\n                        const data = await (0,_lib_businessSectionsDataNew__WEBPACK_IMPORTED_MODULE_7__.fetchBusinessSectionsNew)(projectId);\n                        setSections(data);\n                    } catch (err) {\n                        setError(\"Failed to load business sections\");\n                        console.error(\"Error loading business sections:\", err);\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"ProjectDetailPage.useEffect.loadBusinessSections\"];\n            loadBusinessSections();\n        }\n    }[\"ProjectDetailPage.useEffect\"], [\n        setSections,\n        setLoading,\n        setError\n    ]);\n    // Handle business item selection - show detail view\n    const handleBusinessItemClick = async (item)=>{\n        setSelectedItem(item);\n        // Fetch comprehensive item details using the enhanced store\n        await fetchItemDetails(item.id, projectId);\n    };\n    // Handle back to items\n    const handleBackToItems = ()=>{\n        setSelectedItem(null);\n    };\n    // Resize functionality\n    const resizable = (0,_hooks_useResizable__WEBPACK_IMPORTED_MODULE_6__.useResizable)({\n        initialWidth: sidebarWidth,\n        minWidthPercent: 10,\n        maxWidthPercent: 70,\n        onWidthChange: {\n            \"ProjectDetailPage.useResizable[resizable]\": (width)=>{\n                // Convert percentage to viewport width\n                const widthPercent = parseFloat(width.replace(\"%\", \"\"));\n                const vwWidth = \"\".concat(widthPercent, \"vw\");\n                setSidebarWidth(vwWidth);\n                // Update chatWidth to match sidebarWidth for consistency\n                if (widthPercent <= 70) {\n                    setChatWidth(\"45%\");\n                } else {\n                    setChatWidth(\"45%\");\n                }\n            }\n        }[\"ProjectDetailPage.useResizable[resizable]\"],\n        onCollapse: {\n            \"ProjectDetailPage.useResizable[resizable]\": ()=>{\n                setIsChatCollapsed(true);\n            }\n        }[\"ProjectDetailPage.useResizable[resizable]\"]\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-screen overflow-hidden\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_3__.SidebarProvider, {\n            defaultOpen: true,\n            style: {\n                \"--sidebar-width\": currentSidebarWidth,\n                \"--sidebar-width-mobile\": \"18rem\",\n                \"--sidebar-width-icon\": \"5rem\",\n                transition: \"all 0.3s ease-in-out\"\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_project_sidebar__WEBPACK_IMPORTED_MODULE_2__.ProjectSidebar, {\n                    projectId: projectId,\n                    chatWidth: chatWidth,\n                    setChatWidth: handleChatWidthChange,\n                    isChatCollapsed: isChatCollapsed,\n                    setIsChatCollapsed: setIsChatCollapsed,\n                    selectedBusinessItem: selectedItem,\n                    showDescription: !!selectedItem,\n                    onBackToProject: handleBackToItems,\n                    resizeHandle: {\n                        onMouseDown: resizable.handleMouseDown,\n                        isDragging: resizable.isDragging\n                    }\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/app/projects/[id]/page.tsx\",\n                    lineNumber: 140,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_3__.SidebarInset, {\n                    className: \"flex-1 flex flex-col h-screen overflow-hidden\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_project__WEBPACK_IMPORTED_MODULE_1__.ProjectHeader, {\n                            activeContent: activeContent,\n                            setActiveContent: setActiveContent,\n                            selectedBusinessItem: selectedItem,\n                            onBackToItems: handleBackToItems\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/app/projects/[id]/page.tsx\",\n                            lineNumber: 155,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_project__WEBPACK_IMPORTED_MODULE_1__.ProjectMainContent, {\n                            activeContent: activeContent,\n                            setActiveContent: setActiveContent,\n                            mockDraftItems: mockDraftItems,\n                            mockFileItems: mockFileItems,\n                            selectedItem: selectedItem,\n                            itemDetails: itemDetails,\n                            sections: sections,\n                            isLoading: isLoading,\n                            error: error,\n                            onBusinessItemClick: handleBusinessItemClick,\n                            onBackToItems: handleBackToItems\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/app/projects/[id]/page.tsx\",\n                            lineNumber: 162,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"fixed bottom-0 h-16 bg-gradient-to-t from-gray-50 via-gray-50/80 to-transparent dark:from-gray-900 dark:via-gray-900/80 dark:to-transparent pointer-events-none z-10\",\n                            style: {\n                                left: \"var(--sidebar-width, 45vw)\",\n                                right: \"0\"\n                            }\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/app/projects/[id]/page.tsx\",\n                            lineNumber: 177,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"fixed bottom-0 h-8 backdrop-blur-sm pointer-events-none z-10\",\n                            style: {\n                                left: \"var(--sidebar-width, 45vw)\",\n                                right: \"0\"\n                            }\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/app/projects/[id]/page.tsx\",\n                            lineNumber: 184,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/app/projects/[id]/page.tsx\",\n                    lineNumber: 154,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Data/new era/siift-next/src/app/projects/[id]/page.tsx\",\n            lineNumber: 129,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Data/new era/siift-next/src/app/projects/[id]/page.tsx\",\n        lineNumber: 128,\n        columnNumber: 5\n    }, this);\n}\n_s(ProjectDetailPage, \"VVb0RU0eT8v9bmztSSyYjcIe8Ik=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_4__.useParams,\n        _stores_businessSectionStore__WEBPACK_IMPORTED_MODULE_9__.useBusinessSectionStore,\n        _stores_businessItemStoreEnhanced__WEBPACK_IMPORTED_MODULE_8__.useBusinessItemStoreEnhanced,\n        _hooks_useResizable__WEBPACK_IMPORTED_MODULE_6__.useResizable\n    ];\n});\n_c = ProjectDetailPage;\nvar _c;\n$RefreshReg$(_c, \"ProjectDetailPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/projects/[id]/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/services/businessSectionsApi.ts":
/*!*********************************************!*\
  !*** ./src/services/businessSectionsApi.ts ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ApiError: () => (/* binding */ ApiError),\n/* harmony export */   BusinessSectionsApi: () => (/* binding */ BusinessSectionsApi),\n/* harmony export */   bulkUpdateItems: () => (/* binding */ bulkUpdateItems),\n/* harmony export */   createItem: () => (/* binding */ createItem),\n/* harmony export */   deleteItem: () => (/* binding */ deleteItem),\n/* harmony export */   fetchItem: () => (/* binding */ fetchItem),\n/* harmony export */   fetchSections: () => (/* binding */ fetchSections),\n/* harmony export */   getAnalytics: () => (/* binding */ getAnalytics),\n/* harmony export */   updateItem: () => (/* binding */ updateItem)\n/* harmony export */ });\n/* harmony import */ var _lib_businessSectionsDataNew__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/businessSectionsDataNew */ \"(app-pages-browser)/./src/lib/businessSectionsDataNew.ts\");\n// API service layer for business sections\n\n// API Configuration\nconst API_BASE_URL = \"/api\" || 0;\nconst API_TIMEOUT = 10000; // 10 seconds\n// Error types\nclass ApiError extends Error {\n    constructor(message, status, code){\n        super(message), this.status = status, this.code = code;\n        this.name = \"ApiError\";\n    }\n}\n// Request wrapper with error handling\nasync function apiRequest(endpoint) {\n    let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n    const controller = new AbortController();\n    const timeoutId = setTimeout(()=>controller.abort(), API_TIMEOUT);\n    try {\n        const response = await fetch(\"\".concat(API_BASE_URL).concat(endpoint), {\n            ...options,\n            signal: controller.signal,\n            headers: {\n                \"Content-Type\": \"application/json\",\n                ...options.headers\n            }\n        });\n        clearTimeout(timeoutId);\n        if (!response.ok) {\n            const errorData = await response.json().catch(()=>({}));\n            throw new ApiError(errorData.message || \"HTTP \".concat(response.status, \": \").concat(response.statusText), response.status, errorData.code);\n        }\n        return await response.json();\n    } catch (error) {\n        clearTimeout(timeoutId);\n        if (error instanceof ApiError) {\n            throw error;\n        }\n        if (error instanceof Error) {\n            if (error.name === \"AbortError\") {\n                throw new ApiError(\"Request timeout\", 408, \"TIMEOUT\");\n            }\n            throw new ApiError(error.message, 0, \"NETWORK_ERROR\");\n        }\n        throw new ApiError(\"Unknown error occurred\", 0, \"UNKNOWN_ERROR\");\n    }\n}\n// Business Sections API\nclass BusinessSectionsApi {\n    // Fetch all business sections for a project\n    static async fetchSections(projectId) {\n        try {\n            // Use the new comprehensive data with proper delay simulation\n            return await (0,_lib_businessSectionsDataNew__WEBPACK_IMPORTED_MODULE_0__.fetchBusinessSectionsNew)(projectId);\n        // Uncomment when API is ready:\n        // return await apiRequest<BusinessSection[]>(`/projects/${projectId}/business-sections`);\n        } catch (error) {\n            console.error(\"Failed to fetch business sections:\", error);\n            throw error;\n        }\n    }\n    // Fetch a specific business item\n    static async fetchItem(projectId, itemId) {\n        try {\n            // Mock implementation\n            const sections = await this.fetchSections(projectId);\n            const item = sections.flatMap((section)=>section.items).find((item)=>item.id === itemId);\n            if (!item) {\n                throw new ApiError(\"Business item not found\", 404, \"ITEM_NOT_FOUND\");\n            }\n            return item;\n        // Uncomment when API is ready:\n        // return await apiRequest<BusinessItem>(`/projects/${projectId}/business-sections/items/${itemId}`);\n        } catch (error) {\n            console.error(\"Failed to fetch business item:\", error);\n            throw error;\n        }\n    }\n    // Update a business item\n    static async updateItem(projectId, itemId, updates) {\n        try {\n            // Mock implementation - in real app, this would update the backend\n            const item = await this.fetchItem(projectId, itemId);\n            const updatedItem = {\n                ...item,\n                ...updates\n            };\n            // Simulate API delay\n            await new Promise((resolve)=>setTimeout(resolve, 300));\n            return updatedItem;\n        // Uncomment when API is ready:\n        // return await apiRequest<BusinessItem>(\n        //   `/projects/${projectId}/business-sections/items/${itemId}`,\n        //   {\n        //     method: \"PATCH\",\n        //     body: JSON.stringify(updates),\n        //   }\n        // );\n        } catch (error) {\n            console.error(\"Failed to update business item:\", error);\n            throw error;\n        }\n    }\n    // Create a new business item\n    static async createItem(projectId, sectionId, item) {\n        try {\n            // Mock implementation\n            const newItem = {\n                ...item,\n                id: \"item-\".concat(Date.now())\n            };\n            // Simulate API delay\n            await new Promise((resolve)=>setTimeout(resolve, 300));\n            return newItem;\n        // Uncomment when API is ready:\n        // return await apiRequest<BusinessItem>(\n        //   `/projects/${projectId}/business-sections/${sectionId}/items`,\n        //   {\n        //     method: \"POST\",\n        //     body: JSON.stringify(item),\n        //   }\n        // );\n        } catch (error) {\n            console.error(\"Failed to create business item:\", error);\n            throw error;\n        }\n    }\n    // Delete a business item\n    static async deleteItem(projectId, itemId) {\n        try {\n            // Mock implementation\n            await new Promise((resolve)=>setTimeout(resolve, 300));\n        // Uncomment when API is ready:\n        // await apiRequest<void>(\n        //   `/projects/${projectId}/business-sections/items/${itemId}`,\n        //   {\n        //     method: \"DELETE\",\n        //   }\n        // );\n        } catch (error) {\n            console.error(\"Failed to delete business item:\", error);\n            throw error;\n        }\n    }\n    // Bulk update multiple items\n    static async bulkUpdateItems(projectId, updates) {\n        try {\n            // Mock implementation\n            const updatedItems = [];\n            for (const { itemId, updates: itemUpdates } of updates){\n                const updatedItem = await this.updateItem(projectId, itemId, itemUpdates);\n                updatedItems.push(updatedItem);\n            }\n            return updatedItems;\n        // Uncomment when API is ready:\n        // return await apiRequest<BusinessItem[]>(\n        //   `/projects/${projectId}/business-sections/items/bulk-update`,\n        //   {\n        //     method: \"PATCH\",\n        //     body: JSON.stringify({ updates }),\n        //   }\n        // );\n        } catch (error) {\n            console.error(\"Failed to bulk update business items:\", error);\n            throw error;\n        }\n    }\n    // Get business sections analytics\n    static async getAnalytics(projectId) {\n        try {\n            const sections = await this.fetchSections(projectId);\n            const allItems = sections.flatMap((section)=>section.items);\n            const totalItems = allItems.length;\n            const completedItems = allItems.filter((item)=>item.status === \"confirmed\").length;\n            const completionPercentage = totalItems > 0 ? completedItems / totalItems * 100 : 0;\n            const categoryBreakdown = {};\n            sections.forEach((section)=>{\n                const total = section.items.length;\n                const completed = section.items.filter((item)=>item.status === \"confirmed\").length;\n                categoryBreakdown[section.title] = {\n                    total,\n                    completed\n                };\n            });\n            return {\n                totalItems,\n                completedItems,\n                completionPercentage,\n                categoryBreakdown\n            };\n        // Uncomment when API is ready:\n        // return await apiRequest<any>(`/projects/${projectId}/business-sections/analytics`);\n        } catch (error) {\n            console.error(\"Failed to fetch business sections analytics:\", error);\n            throw error;\n        }\n    }\n}\n// Export convenience functions\nconst { fetchSections, fetchItem, updateItem, createItem, deleteItem, bulkUpdateItems, getAnalytics } = BusinessSectionsApi;\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/services/businessSectionsApi.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/stores/businessItemStoreEnhanced.ts":
/*!*************************************************!*\
  !*** ./src/stores/businessItemStoreEnhanced.ts ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkItemDependencies: () => (/* binding */ checkItemDependencies),\n/* harmony export */   getItemData: () => (/* binding */ getItemData),\n/* harmony export */   getRelatedItems: () => (/* binding */ getRelatedItems),\n/* harmony export */   useBusinessItemStoreEnhanced: () => (/* binding */ useBusinessItemStoreEnhanced)\n/* harmony export */ });\n/* harmony import */ var _data_businessItemsData__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/data/businessItemsData */ \"(app-pages-browser)/./src/data/businessItemsData.ts\");\n/* harmony import */ var _services_businessSectionsApi__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/services/businessSectionsApi */ \"(app-pages-browser)/./src/services/businessSectionsApi.ts\");\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! zustand */ \"(app-pages-browser)/./node_modules/zustand/esm/react.mjs\");\n/* harmony import */ var zustand_middleware__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! zustand/middleware */ \"(app-pages-browser)/./node_modules/zustand/esm/middleware.mjs\");\n// Enhanced business item store using the new comprehensive data structure\n\n\n\n\nconst useBusinessItemStoreEnhanced = (0,zustand__WEBPACK_IMPORTED_MODULE_2__.create)()((0,zustand_middleware__WEBPACK_IMPORTED_MODULE_3__.devtools)((set, get)=>({\n        selectedItem: null,\n        itemDetails: [],\n        isLoading: false,\n        error: null,\n        setSelectedItem: (item)=>set({\n                selectedItem: item\n            }),\n        setItemDetails: (details)=>set({\n                itemDetails: details\n            }),\n        setLoading: (isLoading)=>set({\n                isLoading\n            }),\n        setError: (error)=>set({\n                error\n            }),\n        // Fetch comprehensive item details using the new data structure\n        fetchItemDetails: async function(itemId) {\n            let projectId = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"default\";\n            try {\n                set({\n                    isLoading: true,\n                    error: null\n                });\n                // First, try to get stored details from browser storage\n                const storedDetails = browserStorageService.getItemDetails(itemId, projectId);\n                if (storedDetails.length > 0) {\n                    // Use stored details if available\n                    set({\n                        itemDetails: storedDetails,\n                        isLoading: false\n                    });\n                    return;\n                }\n                // If no stored details, create initial details from comprehensive data\n                const itemData = _data_businessItemsData__WEBPACK_IMPORTED_MODULE_0__.businessItemsData.find((item)=>item.id === itemId);\n                if (!itemData) {\n                    throw new Error(\"Item with ID \".concat(itemId, \" not found\"));\n                }\n                // Create initial detail items\n                const initialDetails = [\n                    {\n                        id: \"\".concat(itemData.id, \"-main\"),\n                        title: \"Main Analysis\",\n                        description: itemData.description,\n                        status: itemData.status,\n                        actions: \"\".concat(itemData.actions, \" actions tracked\"),\n                        result: \"\".concat(itemData.results, \" results achieved\"),\n                        createdAt: new Date().toISOString(),\n                        updatedAt: new Date().toISOString()\n                    }\n                ];\n                // Add sample values as editable detail items\n                if (itemData.values && itemData.values.length > 0) {\n                    itemData.values.forEach((value, index)=>{\n                        initialDetails.push({\n                            id: \"\".concat(itemData.id, \"-sample-\").concat(index),\n                            title: \"Example \".concat(index + 1),\n                            status: \"idea\",\n                            actions: \"Reference example\",\n                            result: value,\n                            createdAt: new Date().toISOString(),\n                            updatedAt: new Date().toISOString()\n                        });\n                    });\n                }\n                // Save initial details to browser storage\n                browserStorageService.saveItemDetails(itemId, initialDetails, projectId);\n                set({\n                    itemDetails: initialDetails,\n                    isLoading: false\n                });\n            } catch (error) {\n                console.error(\"Error fetching item details:\", error);\n                set({\n                    error: error instanceof Error ? error.message : \"Failed to fetch item details\",\n                    isLoading: false\n                });\n            }\n        },\n        // Update item status with dependency validation\n        updateItemStatus: async function(itemId, status) {\n            let projectId = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : \"default\";\n            try {\n                set({\n                    isLoading: true,\n                    error: null\n                });\n                // Check dependencies before allowing status change to \"confirmed\"\n                if (status === \"confirmed\") {\n                    const canProgress = get().checkDependencies(itemId);\n                    if (!canProgress) {\n                        throw new Error(\"Cannot mark as confirmed: dependencies not satisfied\");\n                    }\n                }\n                // Use the API to update the item\n                await _services_businessSectionsApi__WEBPACK_IMPORTED_MODULE_1__.BusinessSectionsApi.updateItem(projectId, itemId, {\n                    status\n                });\n                // Update local state if we have the item selected\n                const { selectedItem } = get();\n                if (selectedItem && selectedItem.id === itemId) {\n                    set({\n                        selectedItem: {\n                            ...selectedItem,\n                            status\n                        }\n                    });\n                }\n                set({\n                    isLoading: false\n                });\n            } catch (error) {\n                console.error(\"Error updating item status:\", error);\n                set({\n                    error: error instanceof Error ? error.message : \"Failed to update item status\",\n                    isLoading: false\n                });\n            }\n        },\n        addItemDetail: (detail)=>{\n            const { selectedItem } = get();\n            if (!selectedItem) return;\n            const updatedDetails = [\n                ...get().itemDetails,\n                detail\n            ];\n            set({\n                itemDetails: updatedDetails\n            });\n            // Save to browser storage\n            browserStorageService.saveItemDetails(selectedItem.id, updatedDetails, \"default\");\n        },\n        updateItemDetail: (id, updates)=>{\n            const { selectedItem } = get();\n            if (!selectedItem) return;\n            const updatedDetails = get().itemDetails.map((item)=>item.id === id ? {\n                    ...item,\n                    ...updates,\n                    updatedAt: new Date().toISOString()\n                } : item);\n            set({\n                itemDetails: updatedDetails\n            });\n            // Save to browser storage\n            browserStorageService.saveItemDetails(selectedItem.id, updatedDetails, \"default\");\n        },\n        removeItemDetail: (id)=>{\n            const { selectedItem } = get();\n            if (!selectedItem) return;\n            const updatedDetails = get().itemDetails.filter((item)=>item.id !== id);\n            set({\n                itemDetails: updatedDetails\n            });\n            // Save to browser storage\n            browserStorageService.saveItemDetails(selectedItem.id, updatedDetails, \"default\");\n        },\n        // Get comprehensive item data\n        getItemData: (itemId)=>{\n            return _data_businessItemsData__WEBPACK_IMPORTED_MODULE_0__.businessItemsData.find((item)=>item.id === itemId);\n        },\n        // Check if item dependencies are satisfied\n        checkDependencies: (itemId)=>{\n            const itemData = _data_businessItemsData__WEBPACK_IMPORTED_MODULE_0__.businessItemsData.find((item)=>item.id === itemId);\n            if (!itemData || !itemData.dependencies || itemData.dependencies.length === 0) {\n                return true;\n            }\n            return itemData.dependencies.every((depId)=>{\n                const depItem = _data_businessItemsData__WEBPACK_IMPORTED_MODULE_0__.businessItemsData.find((dep)=>dep.id === depId);\n                return (depItem === null || depItem === void 0 ? void 0 : depItem.status) === \"confirmed\";\n            });\n        },\n        // Get related items (dependencies and dependents)\n        getRelatedItems: (itemId)=>{\n            var _itemData_dependencies;\n            const itemData = _data_businessItemsData__WEBPACK_IMPORTED_MODULE_0__.businessItemsData.find((item)=>item.id === itemId);\n            // Get dependencies\n            const dependencies = (itemData === null || itemData === void 0 ? void 0 : (_itemData_dependencies = itemData.dependencies) === null || _itemData_dependencies === void 0 ? void 0 : _itemData_dependencies.map((depId)=>_data_businessItemsData__WEBPACK_IMPORTED_MODULE_0__.businessItemsData.find((item)=>item.id === depId)).filter(Boolean)) || [];\n            // Get dependents (items that depend on this one)\n            const dependents = _data_businessItemsData__WEBPACK_IMPORTED_MODULE_0__.businessItemsData.filter((item)=>{\n                var _item_dependencies;\n                return (_item_dependencies = item.dependencies) === null || _item_dependencies === void 0 ? void 0 : _item_dependencies.includes(itemId);\n            });\n            return {\n                dependencies,\n                dependents\n            };\n        }\n    }), {\n    name: \"business-item-store-enhanced\"\n}));\n// Helper functions for easy access\nconst getItemData = (itemId)=>{\n    return _data_businessItemsData__WEBPACK_IMPORTED_MODULE_0__.businessItemsData.find((item)=>item.id === itemId);\n};\nconst checkItemDependencies = (itemId)=>{\n    const itemData = _data_businessItemsData__WEBPACK_IMPORTED_MODULE_0__.businessItemsData.find((item)=>item.id === itemId);\n    if (!itemData || !itemData.dependencies || itemData.dependencies.length === 0) {\n        return true;\n    }\n    return itemData.dependencies.every((depId)=>{\n        const depItem = _data_businessItemsData__WEBPACK_IMPORTED_MODULE_0__.businessItemsData.find((dep)=>dep.id === depId);\n        return (depItem === null || depItem === void 0 ? void 0 : depItem.status) === \"confirmed\";\n    });\n};\nconst getRelatedItems = (itemId)=>{\n    var _itemData_dependencies;\n    const itemData = _data_businessItemsData__WEBPACK_IMPORTED_MODULE_0__.businessItemsData.find((item)=>item.id === itemId);\n    const dependencies = (itemData === null || itemData === void 0 ? void 0 : (_itemData_dependencies = itemData.dependencies) === null || _itemData_dependencies === void 0 ? void 0 : _itemData_dependencies.map((depId)=>_data_businessItemsData__WEBPACK_IMPORTED_MODULE_0__.businessItemsData.find((item)=>item.id === depId)).filter(Boolean)) || [];\n    const dependents = _data_businessItemsData__WEBPACK_IMPORTED_MODULE_0__.businessItemsData.filter((item)=>{\n        var _item_dependencies;\n        return (_item_dependencies = item.dependencies) === null || _item_dependencies === void 0 ? void 0 : _item_dependencies.includes(itemId);\n    });\n    return {\n        dependencies,\n        dependents\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/stores/businessItemStoreEnhanced.ts\n"));

/***/ })

});