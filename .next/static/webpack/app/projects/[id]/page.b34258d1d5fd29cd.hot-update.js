"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/projects/[id]/page",{

/***/ "(app-pages-browser)/./src/stores/businessItemStoreEnhanced.ts":
/*!*************************************************!*\
  !*** ./src/stores/businessItemStoreEnhanced.ts ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkItemDependencies: () => (/* binding */ checkItemDependencies),\n/* harmony export */   getItemData: () => (/* binding */ getItemData),\n/* harmony export */   getRelatedItems: () => (/* binding */ getRelatedItems),\n/* harmony export */   useBusinessItemStoreEnhanced: () => (/* binding */ useBusinessItemStoreEnhanced)\n/* harmony export */ });\n/* harmony import */ var _data_businessItemsData__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/data/businessItemsData */ \"(app-pages-browser)/./src/data/businessItemsData.ts\");\n/* harmony import */ var _services_businessSectionsApi__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/services/businessSectionsApi */ \"(app-pages-browser)/./src/services/businessSectionsApi.ts\");\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! zustand */ \"(app-pages-browser)/./node_modules/zustand/esm/react.mjs\");\n/* harmony import */ var zustand_middleware__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! zustand/middleware */ \"(app-pages-browser)/./node_modules/zustand/esm/middleware.mjs\");\n// Enhanced business item store using the new comprehensive data structure\n\n\n\n\nconst useBusinessItemStoreEnhanced = (0,zustand__WEBPACK_IMPORTED_MODULE_2__.create)()((0,zustand_middleware__WEBPACK_IMPORTED_MODULE_3__.devtools)((set, get)=>({\n        selectedItem: null,\n        itemDetails: [],\n        isLoading: false,\n        error: null,\n        setSelectedItem: (item)=>set({\n                selectedItem: item\n            }),\n        setItemDetails: (details)=>set({\n                itemDetails: details\n            }),\n        setLoading: (isLoading)=>set({\n                isLoading\n            }),\n        setError: (error)=>set({\n                error\n            }),\n        // Fetch comprehensive item details using the new data structure\n        fetchItemDetails: async function(itemId) {\n            let projectId = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"default\";\n            try {\n                var _itemData_question;\n                set({\n                    isLoading: true,\n                    error: null\n                });\n                // First, try to get stored details from browser storage\n                const storedDetails = browserStorageService.getItemDetails(itemId, projectId);\n                // For now, always regenerate to see fresh data (remove this later)\n                // if (storedDetails.length > 0) {\n                //   // Use stored details if available\n                //   console.log(\"Using stored details for\", itemId, storedDetails);\n                //   set({ itemDetails: storedDetails, isLoading: false });\n                //   return;\n                // }\n                console.log(\"No stored details found, creating initial data for\", itemId);\n                // If no stored details, create initial details from comprehensive data\n                const itemData = _data_businessItemsData__WEBPACK_IMPORTED_MODULE_0__.businessItemsData.find((item)=>item.id === itemId);\n                if (!itemData) {\n                    throw new Error(\"Item with ID \".concat(itemId, \" not found\"));\n                }\n                // Create initial detail items from sample values\n                const initialDetails = [];\n                // Add sample values as editable detail items for the table\n                if (itemData.values && itemData.values.length > 0) {\n                    itemData.values.forEach((value, index)=>{\n                        var _itemData_question;\n                        initialDetails.push({\n                            id: \"\".concat(itemData.id, \"-value-\").concat(index),\n                            title: \"Response \".concat(index + 1),\n                            status: \"idea\",\n                            actions: \"Sample response - edit to customize\",\n                            result: value,\n                            description: \"Sample response for: \".concat(((_itemData_question = itemData.question) === null || _itemData_question === void 0 ? void 0 : _itemData_question.replace(\"{PROJECT NAME}\", \"your project\")) || itemData.title),\n                            createdAt: new Date().toISOString(),\n                            updatedAt: new Date().toISOString()\n                        });\n                    });\n                }\n                // Add a blank item for new entries\n                initialDetails.push({\n                    id: \"\".concat(itemData.id, \"-new-entry\"),\n                    title: \"Add your response\",\n                    status: \"unproven\",\n                    actions: \"Click to edit and add your own analysis\",\n                    result: \"Enter your findings here...\",\n                    description: \"Your analysis for: \".concat(((_itemData_question = itemData.question) === null || _itemData_question === void 0 ? void 0 : _itemData_question.replace(\"{PROJECT NAME}\", \"your project\")) || itemData.title),\n                    createdAt: new Date().toISOString(),\n                    updatedAt: new Date().toISOString()\n                });\n                // Save initial details to browser storage\n                browserStorageService.saveItemDetails(itemId, initialDetails, projectId);\n                console.log(\"Created initial details for\", itemId, initialDetails);\n                set({\n                    itemDetails: initialDetails,\n                    isLoading: false\n                });\n            } catch (error) {\n                console.error(\"Error fetching item details:\", error);\n                set({\n                    error: error instanceof Error ? error.message : \"Failed to fetch item details\",\n                    isLoading: false\n                });\n            }\n        },\n        // Update item status with dependency validation\n        updateItemStatus: async function(itemId, status) {\n            let projectId = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : \"default\";\n            try {\n                set({\n                    isLoading: true,\n                    error: null\n                });\n                // Check dependencies before allowing status change to \"confirmed\"\n                if (status === \"confirmed\") {\n                    const canProgress = get().checkDependencies(itemId);\n                    if (!canProgress) {\n                        throw new Error(\"Cannot mark as confirmed: dependencies not satisfied\");\n                    }\n                }\n                // Use the API to update the item\n                await _services_businessSectionsApi__WEBPACK_IMPORTED_MODULE_1__.BusinessSectionsApi.updateItem(projectId, itemId, {\n                    status\n                });\n                // Update local state if we have the item selected\n                const { selectedItem } = get();\n                if (selectedItem && selectedItem.id === itemId) {\n                    set({\n                        selectedItem: {\n                            ...selectedItem,\n                            status\n                        }\n                    });\n                }\n                set({\n                    isLoading: false\n                });\n            } catch (error) {\n                console.error(\"Error updating item status:\", error);\n                set({\n                    error: error instanceof Error ? error.message : \"Failed to update item status\",\n                    isLoading: false\n                });\n            }\n        },\n        addItemDetail: (detail)=>{\n            const { selectedItem } = get();\n            if (!selectedItem) return;\n            const updatedDetails = [\n                ...get().itemDetails,\n                detail\n            ];\n            set({\n                itemDetails: updatedDetails\n            });\n            // Save to browser storage\n            browserStorageService.saveItemDetails(selectedItem.id, updatedDetails, \"default\");\n        },\n        updateItemDetail: (id, updates)=>{\n            const { selectedItem } = get();\n            if (!selectedItem) return;\n            const updatedDetails = get().itemDetails.map((item)=>item.id === id ? {\n                    ...item,\n                    ...updates,\n                    updatedAt: new Date().toISOString()\n                } : item);\n            set({\n                itemDetails: updatedDetails\n            });\n            // Save to browser storage\n            browserStorageService.saveItemDetails(selectedItem.id, updatedDetails, \"default\");\n        },\n        removeItemDetail: (id)=>{\n            const { selectedItem } = get();\n            if (!selectedItem) return;\n            const updatedDetails = get().itemDetails.filter((item)=>item.id !== id);\n            set({\n                itemDetails: updatedDetails\n            });\n            // Save to browser storage\n            browserStorageService.saveItemDetails(selectedItem.id, updatedDetails, \"default\");\n        },\n        // Get comprehensive item data\n        getItemData: (itemId)=>{\n            return _data_businessItemsData__WEBPACK_IMPORTED_MODULE_0__.businessItemsData.find((item)=>item.id === itemId);\n        },\n        // Check if item dependencies are satisfied\n        checkDependencies: (itemId)=>{\n            const itemData = _data_businessItemsData__WEBPACK_IMPORTED_MODULE_0__.businessItemsData.find((item)=>item.id === itemId);\n            if (!itemData || !itemData.dependencies || itemData.dependencies.length === 0) {\n                return true;\n            }\n            return itemData.dependencies.every((depId)=>{\n                const depItem = _data_businessItemsData__WEBPACK_IMPORTED_MODULE_0__.businessItemsData.find((dep)=>dep.id === depId);\n                return (depItem === null || depItem === void 0 ? void 0 : depItem.status) === \"confirmed\";\n            });\n        },\n        // Get related items (dependencies and dependents)\n        getRelatedItems: (itemId)=>{\n            var _itemData_dependencies;\n            const itemData = _data_businessItemsData__WEBPACK_IMPORTED_MODULE_0__.businessItemsData.find((item)=>item.id === itemId);\n            // Get dependencies\n            const dependencies = (itemData === null || itemData === void 0 ? void 0 : (_itemData_dependencies = itemData.dependencies) === null || _itemData_dependencies === void 0 ? void 0 : _itemData_dependencies.map((depId)=>_data_businessItemsData__WEBPACK_IMPORTED_MODULE_0__.businessItemsData.find((item)=>item.id === depId)).filter(Boolean)) || [];\n            // Get dependents (items that depend on this one)\n            const dependents = _data_businessItemsData__WEBPACK_IMPORTED_MODULE_0__.businessItemsData.filter((item)=>{\n                var _item_dependencies;\n                return (_item_dependencies = item.dependencies) === null || _item_dependencies === void 0 ? void 0 : _item_dependencies.includes(itemId);\n            });\n            return {\n                dependencies,\n                dependents\n            };\n        }\n    }), {\n    name: \"business-item-store-enhanced\"\n}));\n// Helper functions for easy access\nconst getItemData = (itemId)=>{\n    return _data_businessItemsData__WEBPACK_IMPORTED_MODULE_0__.businessItemsData.find((item)=>item.id === itemId);\n};\nconst checkItemDependencies = (itemId)=>{\n    const itemData = _data_businessItemsData__WEBPACK_IMPORTED_MODULE_0__.businessItemsData.find((item)=>item.id === itemId);\n    if (!itemData || !itemData.dependencies || itemData.dependencies.length === 0) {\n        return true;\n    }\n    return itemData.dependencies.every((depId)=>{\n        const depItem = _data_businessItemsData__WEBPACK_IMPORTED_MODULE_0__.businessItemsData.find((dep)=>dep.id === depId);\n        return (depItem === null || depItem === void 0 ? void 0 : depItem.status) === \"confirmed\";\n    });\n};\nconst getRelatedItems = (itemId)=>{\n    var _itemData_dependencies;\n    const itemData = _data_businessItemsData__WEBPACK_IMPORTED_MODULE_0__.businessItemsData.find((item)=>item.id === itemId);\n    const dependencies = (itemData === null || itemData === void 0 ? void 0 : (_itemData_dependencies = itemData.dependencies) === null || _itemData_dependencies === void 0 ? void 0 : _itemData_dependencies.map((depId)=>_data_businessItemsData__WEBPACK_IMPORTED_MODULE_0__.businessItemsData.find((item)=>item.id === depId)).filter(Boolean)) || [];\n    const dependents = _data_businessItemsData__WEBPACK_IMPORTED_MODULE_0__.businessItemsData.filter((item)=>{\n        var _item_dependencies;\n        return (_item_dependencies = item.dependencies) === null || _item_dependencies === void 0 ? void 0 : _item_dependencies.includes(itemId);\n    });\n    return {\n        dependencies,\n        dependents\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9zdG9yZXMvYnVzaW5lc3NJdGVtU3RvcmVFbmhhbmNlZC50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUFBLDBFQUEwRTtBQUNiO0FBQ1E7QUFLcEM7QUFDYTtBQWtDdkMsTUFBTUksK0JBQStCRiwrQ0FBTUEsR0FDaERDLDREQUFRQSxDQUNOLENBQUNFLEtBQUtDLE1BQVM7UUFDYkMsY0FBYztRQUNkQyxhQUFhLEVBQUU7UUFDZkMsV0FBVztRQUNYQyxPQUFPO1FBRVBDLGlCQUFpQixDQUFDQyxPQUFTUCxJQUFJO2dCQUFFRSxjQUFjSztZQUFLO1FBRXBEQyxnQkFBZ0IsQ0FBQ0MsVUFBWVQsSUFBSTtnQkFBRUcsYUFBYU07WUFBUTtRQUV4REMsWUFBWSxDQUFDTixZQUFjSixJQUFJO2dCQUFFSTtZQUFVO1FBRTNDTyxVQUFVLENBQUNOLFFBQVVMLElBQUk7Z0JBQUVLO1lBQU07UUFFakMsZ0VBQWdFO1FBQ2hFTyxrQkFBa0IsZUFDaEJDO2dCQUNBQyw2RUFBb0I7WUFFcEIsSUFBSTtvQkE2REVDO2dCQTVESmYsSUFBSTtvQkFBRUksV0FBVztvQkFBTUMsT0FBTztnQkFBSztnQkFFbkMsd0RBQXdEO2dCQUN4RCxNQUFNVyxnQkFBZ0JDLHNCQUFzQkMsY0FBYyxDQUN4REwsUUFDQUM7Z0JBR0YsbUVBQW1FO2dCQUNuRSxrQ0FBa0M7Z0JBQ2xDLHVDQUF1QztnQkFDdkMsb0VBQW9FO2dCQUNwRSwyREFBMkQ7Z0JBQzNELFlBQVk7Z0JBQ1osSUFBSTtnQkFFSkssUUFBUUMsR0FBRyxDQUNULHNEQUNBUDtnQkFHRix1RUFBdUU7Z0JBQ3ZFLE1BQU1FLFdBQVdwQixzRUFBaUJBLENBQUMwQixJQUFJLENBQUMsQ0FBQ2QsT0FBU0EsS0FBS2UsRUFBRSxLQUFLVDtnQkFFOUQsSUFBSSxDQUFDRSxVQUFVO29CQUNiLE1BQU0sSUFBSVEsTUFBTSxnQkFBdUIsT0FBUFYsUUFBTztnQkFDekM7Z0JBRUEsaURBQWlEO2dCQUNqRCxNQUFNVyxpQkFBdUMsRUFBRTtnQkFFL0MsMkRBQTJEO2dCQUMzRCxJQUFJVCxTQUFTVSxNQUFNLElBQUlWLFNBQVNVLE1BQU0sQ0FBQ0MsTUFBTSxHQUFHLEdBQUc7b0JBQ2pEWCxTQUFTVSxNQUFNLENBQUNFLE9BQU8sQ0FBQyxDQUFDQyxPQUFPQzs0QkFRMUJkO3dCQVBKUyxlQUFlTSxJQUFJLENBQUM7NEJBQ2xCUixJQUFJLEdBQXdCTyxPQUFyQmQsU0FBU08sRUFBRSxFQUFDLFdBQWUsT0FBTk87NEJBQzVCRSxPQUFPLFlBQXNCLE9BQVZGLFFBQVE7NEJBQzNCRyxRQUFROzRCQUNSQyxTQUFTOzRCQUNUQyxRQUFRTjs0QkFDUk8sYUFBYSx3QkFLWixPQUpDcEIsRUFBQUEscUJBQUFBLFNBQVNxQixRQUFRLGNBQWpCckIseUNBQUFBLG1CQUFtQnNCLE9BQU8sQ0FDeEIsa0JBQ0Esb0JBQ0d0QixTQUFTZ0IsS0FBSzs0QkFFckJPLFdBQVcsSUFBSUMsT0FBT0MsV0FBVzs0QkFDakNDLFdBQVcsSUFBSUYsT0FBT0MsV0FBVzt3QkFDbkM7b0JBQ0Y7Z0JBQ0Y7Z0JBRUEsbUNBQW1DO2dCQUNuQ2hCLGVBQWVNLElBQUksQ0FBQztvQkFDbEJSLElBQUksR0FBZSxPQUFaUCxTQUFTTyxFQUFFLEVBQUM7b0JBQ25CUyxPQUFPO29CQUNQQyxRQUFRO29CQUNSQyxTQUFTO29CQUNUQyxRQUFRO29CQUNSQyxhQUFhLHNCQUdaLE9BRkNwQixFQUFBQSxxQkFBQUEsU0FBU3FCLFFBQVEsY0FBakJyQix5Q0FBQUEsbUJBQW1Cc0IsT0FBTyxDQUFDLGtCQUFrQixvQkFDN0N0QixTQUFTZ0IsS0FBSztvQkFFaEJPLFdBQVcsSUFBSUMsT0FBT0MsV0FBVztvQkFDakNDLFdBQVcsSUFBSUYsT0FBT0MsV0FBVztnQkFDbkM7Z0JBRUEsMENBQTBDO2dCQUMxQ3ZCLHNCQUFzQnlCLGVBQWUsQ0FDbkM3QixRQUNBVyxnQkFDQVY7Z0JBR0ZLLFFBQVFDLEdBQUcsQ0FBQywrQkFBK0JQLFFBQVFXO2dCQUNuRHhCLElBQUk7b0JBQUVHLGFBQWFxQjtvQkFBZ0JwQixXQUFXO2dCQUFNO1lBQ3RELEVBQUUsT0FBT0MsT0FBTztnQkFDZGMsUUFBUWQsS0FBSyxDQUFDLGdDQUFnQ0E7Z0JBQzlDTCxJQUFJO29CQUNGSyxPQUNFQSxpQkFBaUJrQixRQUNibEIsTUFBTXNDLE9BQU8sR0FDYjtvQkFDTnZDLFdBQVc7Z0JBQ2I7WUFDRjtRQUNGO1FBRUEsZ0RBQWdEO1FBQ2hEd0Msa0JBQWtCLGVBQ2hCL0IsUUFDQW1CO2dCQUNBbEIsNkVBQW9CO1lBRXBCLElBQUk7Z0JBQ0ZkLElBQUk7b0JBQUVJLFdBQVc7b0JBQU1DLE9BQU87Z0JBQUs7Z0JBRW5DLGtFQUFrRTtnQkFDbEUsSUFBSTJCLFdBQVcsYUFBYTtvQkFDMUIsTUFBTWEsY0FBYzVDLE1BQU02QyxpQkFBaUIsQ0FBQ2pDO29CQUM1QyxJQUFJLENBQUNnQyxhQUFhO3dCQUNoQixNQUFNLElBQUl0QixNQUNSO29CQUVKO2dCQUNGO2dCQUVBLGlDQUFpQztnQkFDakMsTUFBTTNCLDhFQUFtQkEsQ0FBQ21ELFVBQVUsQ0FBQ2pDLFdBQVdELFFBQVE7b0JBQUVtQjtnQkFBTztnQkFFakUsa0RBQWtEO2dCQUNsRCxNQUFNLEVBQUU5QixZQUFZLEVBQUUsR0FBR0Q7Z0JBQ3pCLElBQUlDLGdCQUFnQkEsYUFBYW9CLEVBQUUsS0FBS1QsUUFBUTtvQkFDOUNiLElBQUk7d0JBQUVFLGNBQWM7NEJBQUUsR0FBR0EsWUFBWTs0QkFBRThCO3dCQUFPO29CQUFFO2dCQUNsRDtnQkFFQWhDLElBQUk7b0JBQUVJLFdBQVc7Z0JBQU07WUFDekIsRUFBRSxPQUFPQyxPQUFPO2dCQUNkYyxRQUFRZCxLQUFLLENBQUMsK0JBQStCQTtnQkFDN0NMLElBQUk7b0JBQ0ZLLE9BQ0VBLGlCQUFpQmtCLFFBQ2JsQixNQUFNc0MsT0FBTyxHQUNiO29CQUNOdkMsV0FBVztnQkFDYjtZQUNGO1FBQ0Y7UUFFQTRDLGVBQWUsQ0FBQ0M7WUFDZCxNQUFNLEVBQUUvQyxZQUFZLEVBQUUsR0FBR0Q7WUFDekIsSUFBSSxDQUFDQyxjQUFjO1lBRW5CLE1BQU1nRCxpQkFBaUI7bUJBQUlqRCxNQUFNRSxXQUFXO2dCQUFFOEM7YUFBTztZQUNyRGpELElBQUk7Z0JBQUVHLGFBQWErQztZQUFlO1lBRWxDLDBCQUEwQjtZQUMxQmpDLHNCQUFzQnlCLGVBQWUsQ0FDbkN4QyxhQUFhb0IsRUFBRSxFQUNmNEIsZ0JBQ0E7UUFFSjtRQUVBQyxrQkFBa0IsQ0FBQzdCLElBQUk4QjtZQUNyQixNQUFNLEVBQUVsRCxZQUFZLEVBQUUsR0FBR0Q7WUFDekIsSUFBSSxDQUFDQyxjQUFjO1lBRW5CLE1BQU1nRCxpQkFBaUJqRCxNQUFNRSxXQUFXLENBQUNrRCxHQUFHLENBQUMsQ0FBQzlDLE9BQzVDQSxLQUFLZSxFQUFFLEtBQUtBLEtBQ1I7b0JBQUUsR0FBR2YsSUFBSTtvQkFBRSxHQUFHNkMsT0FBTztvQkFBRVgsV0FBVyxJQUFJRixPQUFPQyxXQUFXO2dCQUFHLElBQzNEakM7WUFHTlAsSUFBSTtnQkFBRUcsYUFBYStDO1lBQWU7WUFFbEMsMEJBQTBCO1lBQzFCakMsc0JBQXNCeUIsZUFBZSxDQUNuQ3hDLGFBQWFvQixFQUFFLEVBQ2Y0QixnQkFDQTtRQUVKO1FBRUFJLGtCQUFrQixDQUFDaEM7WUFDakIsTUFBTSxFQUFFcEIsWUFBWSxFQUFFLEdBQUdEO1lBQ3pCLElBQUksQ0FBQ0MsY0FBYztZQUVuQixNQUFNZ0QsaUJBQWlCakQsTUFBTUUsV0FBVyxDQUFDb0QsTUFBTSxDQUM3QyxDQUFDaEQsT0FBU0EsS0FBS2UsRUFBRSxLQUFLQTtZQUV4QnRCLElBQUk7Z0JBQUVHLGFBQWErQztZQUFlO1lBRWxDLDBCQUEwQjtZQUMxQmpDLHNCQUFzQnlCLGVBQWUsQ0FDbkN4QyxhQUFhb0IsRUFBRSxFQUNmNEIsZ0JBQ0E7UUFFSjtRQUVBLDhCQUE4QjtRQUM5Qk0sYUFBYSxDQUFDM0M7WUFDWixPQUFPbEIsc0VBQWlCQSxDQUFDMEIsSUFBSSxDQUFDLENBQUNkLE9BQVNBLEtBQUtlLEVBQUUsS0FBS1Q7UUFDdEQ7UUFFQSwyQ0FBMkM7UUFDM0NpQyxtQkFBbUIsQ0FBQ2pDO1lBQ2xCLE1BQU1FLFdBQVdwQixzRUFBaUJBLENBQUMwQixJQUFJLENBQUMsQ0FBQ2QsT0FBU0EsS0FBS2UsRUFBRSxLQUFLVDtZQUM5RCxJQUNFLENBQUNFLFlBQ0QsQ0FBQ0EsU0FBUzBDLFlBQVksSUFDdEIxQyxTQUFTMEMsWUFBWSxDQUFDL0IsTUFBTSxLQUFLLEdBQ2pDO2dCQUNBLE9BQU87WUFDVDtZQUVBLE9BQU9YLFNBQVMwQyxZQUFZLENBQUNDLEtBQUssQ0FBQyxDQUFDQztnQkFDbEMsTUFBTUMsVUFBVWpFLHNFQUFpQkEsQ0FBQzBCLElBQUksQ0FBQyxDQUFDd0MsTUFBUUEsSUFBSXZDLEVBQUUsS0FBS3FDO2dCQUMzRCxPQUFPQyxDQUFBQSxvQkFBQUEsOEJBQUFBLFFBQVM1QixNQUFNLE1BQUs7WUFDN0I7UUFDRjtRQUVBLGtEQUFrRDtRQUNsRDhCLGlCQUFpQixDQUFDakQ7Z0JBS2RFO1lBSkYsTUFBTUEsV0FBV3BCLHNFQUFpQkEsQ0FBQzBCLElBQUksQ0FBQyxDQUFDZCxPQUFTQSxLQUFLZSxFQUFFLEtBQUtUO1lBRTlELG1CQUFtQjtZQUNuQixNQUFNNEMsZUFDSjFDLENBQUFBLHFCQUFBQSxnQ0FBQUEseUJBQUFBLFNBQVUwQyxZQUFZLGNBQXRCMUMsNkNBQUFBLHVCQUNJc0MsR0FBRyxDQUFDLENBQUNNLFFBQ0xoRSxzRUFBaUJBLENBQUMwQixJQUFJLENBQUMsQ0FBQ2QsT0FBU0EsS0FBS2UsRUFBRSxLQUFLcUMsUUFFOUNKLE1BQU0sQ0FBQ1EsYUFBWSxFQUFFO1lBRTFCLGlEQUFpRDtZQUNqRCxNQUFNQyxhQUFhckUsc0VBQWlCQSxDQUFDNEQsTUFBTSxDQUFDLENBQUNoRDtvQkFDM0NBO3dCQUFBQSxxQkFBQUEsS0FBS2tELFlBQVksY0FBakJsRCx5Q0FBQUEsbUJBQW1CMEQsUUFBUSxDQUFDcEQ7O1lBRzlCLE9BQU87Z0JBQUU0QztnQkFBY087WUFBVztRQUNwQztJQUNGLElBQ0E7SUFDRUUsTUFBTTtBQUNSLElBRUY7QUFFRixtQ0FBbUM7QUFDNUIsTUFBTVYsY0FBYyxDQUFDM0M7SUFDMUIsT0FBT2xCLHNFQUFpQkEsQ0FBQzBCLElBQUksQ0FBQyxDQUFDZCxPQUFTQSxLQUFLZSxFQUFFLEtBQUtUO0FBQ3RELEVBQUU7QUFFSyxNQUFNc0Qsd0JBQXdCLENBQUN0RDtJQUNwQyxNQUFNRSxXQUFXcEIsc0VBQWlCQSxDQUFDMEIsSUFBSSxDQUFDLENBQUNkLE9BQVNBLEtBQUtlLEVBQUUsS0FBS1Q7SUFDOUQsSUFDRSxDQUFDRSxZQUNELENBQUNBLFNBQVMwQyxZQUFZLElBQ3RCMUMsU0FBUzBDLFlBQVksQ0FBQy9CLE1BQU0sS0FBSyxHQUNqQztRQUNBLE9BQU87SUFDVDtJQUVBLE9BQU9YLFNBQVMwQyxZQUFZLENBQUNDLEtBQUssQ0FBQyxDQUFDQztRQUNsQyxNQUFNQyxVQUFVakUsc0VBQWlCQSxDQUFDMEIsSUFBSSxDQUFDLENBQUN3QyxNQUFRQSxJQUFJdkMsRUFBRSxLQUFLcUM7UUFDM0QsT0FBT0MsQ0FBQUEsb0JBQUFBLDhCQUFBQSxRQUFTNUIsTUFBTSxNQUFLO0lBQzdCO0FBQ0YsRUFBRTtBQUVLLE1BQU04QixrQkFBa0IsQ0FBQ2pEO1FBSTVCRTtJQUhGLE1BQU1BLFdBQVdwQixzRUFBaUJBLENBQUMwQixJQUFJLENBQUMsQ0FBQ2QsT0FBU0EsS0FBS2UsRUFBRSxLQUFLVDtJQUU5RCxNQUFNNEMsZUFDSjFDLENBQUFBLHFCQUFBQSxnQ0FBQUEseUJBQUFBLFNBQVUwQyxZQUFZLGNBQXRCMUMsNkNBQUFBLHVCQUNJc0MsR0FBRyxDQUFDLENBQUNNLFFBQVVoRSxzRUFBaUJBLENBQUMwQixJQUFJLENBQUMsQ0FBQ2QsT0FBU0EsS0FBS2UsRUFBRSxLQUFLcUMsUUFDN0RKLE1BQU0sQ0FBQ1EsYUFBWSxFQUFFO0lBRTFCLE1BQU1DLGFBQWFyRSxzRUFBaUJBLENBQUM0RCxNQUFNLENBQUMsQ0FBQ2hEO1lBQzNDQTtnQkFBQUEscUJBQUFBLEtBQUtrRCxZQUFZLGNBQWpCbEQseUNBQUFBLG1CQUFtQjBELFFBQVEsQ0FBQ3BEOztJQUc5QixPQUFPO1FBQUU0QztRQUFjTztJQUFXO0FBQ3BDLEVBQUUiLCJzb3VyY2VzIjpbIi9Vc2Vycy9hcmVmL0RhdGEvbmV3IGVyYS9zaWlmdC1uZXh0L3NyYy9zdG9yZXMvYnVzaW5lc3NJdGVtU3RvcmVFbmhhbmNlZC50cyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBFbmhhbmNlZCBidXNpbmVzcyBpdGVtIHN0b3JlIHVzaW5nIHRoZSBuZXcgY29tcHJlaGVuc2l2ZSBkYXRhIHN0cnVjdHVyZVxuaW1wb3J0IHsgYnVzaW5lc3NJdGVtc0RhdGEgfSBmcm9tIFwiQC9kYXRhL2J1c2luZXNzSXRlbXNEYXRhXCI7XG5pbXBvcnQgeyBCdXNpbmVzc1NlY3Rpb25zQXBpIH0gZnJvbSBcIkAvc2VydmljZXMvYnVzaW5lc3NTZWN0aW9uc0FwaVwiO1xuaW1wb3J0IHR5cGUge1xuICBCdXNpbmVzc0l0ZW0sXG4gIEJ1c2luZXNzSXRlbURldGFpbCxcbn0gZnJvbSBcIkAvdHlwZXMvQnVzaW5lc3NTZWN0aW9uLnR5cGVzXCI7XG5pbXBvcnQgeyBjcmVhdGUgfSBmcm9tIFwienVzdGFuZFwiO1xuaW1wb3J0IHsgZGV2dG9vbHMgfSBmcm9tIFwienVzdGFuZC9taWRkbGV3YXJlXCI7XG5cbmV4cG9ydCBpbnRlcmZhY2UgQnVzaW5lc3NJdGVtU3RvcmVFbmhhbmNlZCB7XG4gIHNlbGVjdGVkSXRlbTogQnVzaW5lc3NJdGVtIHwgbnVsbDtcbiAgaXRlbURldGFpbHM6IEJ1c2luZXNzSXRlbURldGFpbFtdO1xuICBpc0xvYWRpbmc6IGJvb2xlYW47XG4gIGVycm9yOiBzdHJpbmcgfCBudWxsO1xuXG4gIC8vIEFjdGlvbnNcbiAgc2V0U2VsZWN0ZWRJdGVtOiAoaXRlbTogQnVzaW5lc3NJdGVtIHwgbnVsbCkgPT4gdm9pZDtcbiAgc2V0SXRlbURldGFpbHM6IChkZXRhaWxzOiBCdXNpbmVzc0l0ZW1EZXRhaWxbXSkgPT4gdm9pZDtcbiAgc2V0TG9hZGluZzogKGxvYWRpbmc6IGJvb2xlYW4pID0+IHZvaWQ7XG4gIHNldEVycm9yOiAoZXJyb3I6IHN0cmluZyB8IG51bGwpID0+IHZvaWQ7XG5cbiAgLy8gRW5oYW5jZWQgYWN0aW9uc1xuICBmZXRjaEl0ZW1EZXRhaWxzOiAoaXRlbUlkOiBzdHJpbmcsIHByb2plY3RJZD86IHN0cmluZykgPT4gUHJvbWlzZTx2b2lkPjtcbiAgdXBkYXRlSXRlbVN0YXR1czogKFxuICAgIGl0ZW1JZDogc3RyaW5nLFxuICAgIHN0YXR1czogQnVzaW5lc3NJdGVtW1wic3RhdHVzXCJdLFxuICAgIHByb2plY3RJZD86IHN0cmluZ1xuICApID0+IFByb21pc2U8dm9pZD47XG4gIGFkZEl0ZW1EZXRhaWw6IChkZXRhaWw6IEJ1c2luZXNzSXRlbURldGFpbCkgPT4gdm9pZDtcbiAgdXBkYXRlSXRlbURldGFpbDogKGlkOiBzdHJpbmcsIHVwZGF0ZXM6IFBhcnRpYWw8QnVzaW5lc3NJdGVtRGV0YWlsPikgPT4gdm9pZDtcbiAgcmVtb3ZlSXRlbURldGFpbDogKGlkOiBzdHJpbmcpID0+IHZvaWQ7XG5cbiAgLy8gVXRpbGl0eSBhY3Rpb25zXG4gIGdldEl0ZW1EYXRhOiAoaXRlbUlkOiBzdHJpbmcpID0+IGFueTtcbiAgY2hlY2tEZXBlbmRlbmNpZXM6IChpdGVtSWQ6IHN0cmluZykgPT4gYm9vbGVhbjtcbiAgZ2V0UmVsYXRlZEl0ZW1zOiAoaXRlbUlkOiBzdHJpbmcpID0+IHtcbiAgICBkZXBlbmRlbmNpZXM6IGFueVtdO1xuICAgIGRlcGVuZGVudHM6IGFueVtdO1xuICB9O1xufVxuXG5leHBvcnQgY29uc3QgdXNlQnVzaW5lc3NJdGVtU3RvcmVFbmhhbmNlZCA9IGNyZWF0ZTxCdXNpbmVzc0l0ZW1TdG9yZUVuaGFuY2VkPigpKFxuICBkZXZ0b29scyhcbiAgICAoc2V0LCBnZXQpID0+ICh7XG4gICAgICBzZWxlY3RlZEl0ZW06IG51bGwsXG4gICAgICBpdGVtRGV0YWlsczogW10sXG4gICAgICBpc0xvYWRpbmc6IGZhbHNlLFxuICAgICAgZXJyb3I6IG51bGwsXG5cbiAgICAgIHNldFNlbGVjdGVkSXRlbTogKGl0ZW0pID0+IHNldCh7IHNlbGVjdGVkSXRlbTogaXRlbSB9KSxcblxuICAgICAgc2V0SXRlbURldGFpbHM6IChkZXRhaWxzKSA9PiBzZXQoeyBpdGVtRGV0YWlsczogZGV0YWlscyB9KSxcblxuICAgICAgc2V0TG9hZGluZzogKGlzTG9hZGluZykgPT4gc2V0KHsgaXNMb2FkaW5nIH0pLFxuXG4gICAgICBzZXRFcnJvcjogKGVycm9yKSA9PiBzZXQoeyBlcnJvciB9KSxcblxuICAgICAgLy8gRmV0Y2ggY29tcHJlaGVuc2l2ZSBpdGVtIGRldGFpbHMgdXNpbmcgdGhlIG5ldyBkYXRhIHN0cnVjdHVyZVxuICAgICAgZmV0Y2hJdGVtRGV0YWlsczogYXN5bmMgKFxuICAgICAgICBpdGVtSWQ6IHN0cmluZyxcbiAgICAgICAgcHJvamVjdElkOiBzdHJpbmcgPSBcImRlZmF1bHRcIlxuICAgICAgKSA9PiB7XG4gICAgICAgIHRyeSB7XG4gICAgICAgICAgc2V0KHsgaXNMb2FkaW5nOiB0cnVlLCBlcnJvcjogbnVsbCB9KTtcblxuICAgICAgICAgIC8vIEZpcnN0LCB0cnkgdG8gZ2V0IHN0b3JlZCBkZXRhaWxzIGZyb20gYnJvd3NlciBzdG9yYWdlXG4gICAgICAgICAgY29uc3Qgc3RvcmVkRGV0YWlscyA9IGJyb3dzZXJTdG9yYWdlU2VydmljZS5nZXRJdGVtRGV0YWlscyhcbiAgICAgICAgICAgIGl0ZW1JZCxcbiAgICAgICAgICAgIHByb2plY3RJZFxuICAgICAgICAgICk7XG5cbiAgICAgICAgICAvLyBGb3Igbm93LCBhbHdheXMgcmVnZW5lcmF0ZSB0byBzZWUgZnJlc2ggZGF0YSAocmVtb3ZlIHRoaXMgbGF0ZXIpXG4gICAgICAgICAgLy8gaWYgKHN0b3JlZERldGFpbHMubGVuZ3RoID4gMCkge1xuICAgICAgICAgIC8vICAgLy8gVXNlIHN0b3JlZCBkZXRhaWxzIGlmIGF2YWlsYWJsZVxuICAgICAgICAgIC8vICAgY29uc29sZS5sb2coXCJVc2luZyBzdG9yZWQgZGV0YWlscyBmb3JcIiwgaXRlbUlkLCBzdG9yZWREZXRhaWxzKTtcbiAgICAgICAgICAvLyAgIHNldCh7IGl0ZW1EZXRhaWxzOiBzdG9yZWREZXRhaWxzLCBpc0xvYWRpbmc6IGZhbHNlIH0pO1xuICAgICAgICAgIC8vICAgcmV0dXJuO1xuICAgICAgICAgIC8vIH1cblxuICAgICAgICAgIGNvbnNvbGUubG9nKFxuICAgICAgICAgICAgXCJObyBzdG9yZWQgZGV0YWlscyBmb3VuZCwgY3JlYXRpbmcgaW5pdGlhbCBkYXRhIGZvclwiLFxuICAgICAgICAgICAgaXRlbUlkXG4gICAgICAgICAgKTtcblxuICAgICAgICAgIC8vIElmIG5vIHN0b3JlZCBkZXRhaWxzLCBjcmVhdGUgaW5pdGlhbCBkZXRhaWxzIGZyb20gY29tcHJlaGVuc2l2ZSBkYXRhXG4gICAgICAgICAgY29uc3QgaXRlbURhdGEgPSBidXNpbmVzc0l0ZW1zRGF0YS5maW5kKChpdGVtKSA9PiBpdGVtLmlkID09PSBpdGVtSWQpO1xuXG4gICAgICAgICAgaWYgKCFpdGVtRGF0YSkge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKGBJdGVtIHdpdGggSUQgJHtpdGVtSWR9IG5vdCBmb3VuZGApO1xuICAgICAgICAgIH1cblxuICAgICAgICAgIC8vIENyZWF0ZSBpbml0aWFsIGRldGFpbCBpdGVtcyBmcm9tIHNhbXBsZSB2YWx1ZXNcbiAgICAgICAgICBjb25zdCBpbml0aWFsRGV0YWlsczogQnVzaW5lc3NJdGVtRGV0YWlsW10gPSBbXTtcblxuICAgICAgICAgIC8vIEFkZCBzYW1wbGUgdmFsdWVzIGFzIGVkaXRhYmxlIGRldGFpbCBpdGVtcyBmb3IgdGhlIHRhYmxlXG4gICAgICAgICAgaWYgKGl0ZW1EYXRhLnZhbHVlcyAmJiBpdGVtRGF0YS52YWx1ZXMubGVuZ3RoID4gMCkge1xuICAgICAgICAgICAgaXRlbURhdGEudmFsdWVzLmZvckVhY2goKHZhbHVlLCBpbmRleCkgPT4ge1xuICAgICAgICAgICAgICBpbml0aWFsRGV0YWlscy5wdXNoKHtcbiAgICAgICAgICAgICAgICBpZDogYCR7aXRlbURhdGEuaWR9LXZhbHVlLSR7aW5kZXh9YCxcbiAgICAgICAgICAgICAgICB0aXRsZTogYFJlc3BvbnNlICR7aW5kZXggKyAxfWAsXG4gICAgICAgICAgICAgICAgc3RhdHVzOiBcImlkZWFcIixcbiAgICAgICAgICAgICAgICBhY3Rpb25zOiBcIlNhbXBsZSByZXNwb25zZSAtIGVkaXQgdG8gY3VzdG9taXplXCIsXG4gICAgICAgICAgICAgICAgcmVzdWx0OiB2YWx1ZSxcbiAgICAgICAgICAgICAgICBkZXNjcmlwdGlvbjogYFNhbXBsZSByZXNwb25zZSBmb3I6ICR7XG4gICAgICAgICAgICAgICAgICBpdGVtRGF0YS5xdWVzdGlvbj8ucmVwbGFjZShcbiAgICAgICAgICAgICAgICAgICAgXCJ7UFJPSkVDVCBOQU1FfVwiLFxuICAgICAgICAgICAgICAgICAgICBcInlvdXIgcHJvamVjdFwiXG4gICAgICAgICAgICAgICAgICApIHx8IGl0ZW1EYXRhLnRpdGxlXG4gICAgICAgICAgICAgICAgfWAsXG4gICAgICAgICAgICAgICAgY3JlYXRlZEF0OiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCksXG4gICAgICAgICAgICAgICAgdXBkYXRlZEF0OiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCksXG4gICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgfSk7XG4gICAgICAgICAgfVxuXG4gICAgICAgICAgLy8gQWRkIGEgYmxhbmsgaXRlbSBmb3IgbmV3IGVudHJpZXNcbiAgICAgICAgICBpbml0aWFsRGV0YWlscy5wdXNoKHtcbiAgICAgICAgICAgIGlkOiBgJHtpdGVtRGF0YS5pZH0tbmV3LWVudHJ5YCxcbiAgICAgICAgICAgIHRpdGxlOiBcIkFkZCB5b3VyIHJlc3BvbnNlXCIsXG4gICAgICAgICAgICBzdGF0dXM6IFwidW5wcm92ZW5cIixcbiAgICAgICAgICAgIGFjdGlvbnM6IFwiQ2xpY2sgdG8gZWRpdCBhbmQgYWRkIHlvdXIgb3duIGFuYWx5c2lzXCIsXG4gICAgICAgICAgICByZXN1bHQ6IFwiRW50ZXIgeW91ciBmaW5kaW5ncyBoZXJlLi4uXCIsXG4gICAgICAgICAgICBkZXNjcmlwdGlvbjogYFlvdXIgYW5hbHlzaXMgZm9yOiAke1xuICAgICAgICAgICAgICBpdGVtRGF0YS5xdWVzdGlvbj8ucmVwbGFjZShcIntQUk9KRUNUIE5BTUV9XCIsIFwieW91ciBwcm9qZWN0XCIpIHx8XG4gICAgICAgICAgICAgIGl0ZW1EYXRhLnRpdGxlXG4gICAgICAgICAgICB9YCxcbiAgICAgICAgICAgIGNyZWF0ZWRBdDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpLFxuICAgICAgICAgICAgdXBkYXRlZEF0OiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCksXG4gICAgICAgICAgfSk7XG5cbiAgICAgICAgICAvLyBTYXZlIGluaXRpYWwgZGV0YWlscyB0byBicm93c2VyIHN0b3JhZ2VcbiAgICAgICAgICBicm93c2VyU3RvcmFnZVNlcnZpY2Uuc2F2ZUl0ZW1EZXRhaWxzKFxuICAgICAgICAgICAgaXRlbUlkLFxuICAgICAgICAgICAgaW5pdGlhbERldGFpbHMsXG4gICAgICAgICAgICBwcm9qZWN0SWRcbiAgICAgICAgICApO1xuXG4gICAgICAgICAgY29uc29sZS5sb2coXCJDcmVhdGVkIGluaXRpYWwgZGV0YWlscyBmb3JcIiwgaXRlbUlkLCBpbml0aWFsRGV0YWlscyk7XG4gICAgICAgICAgc2V0KHsgaXRlbURldGFpbHM6IGluaXRpYWxEZXRhaWxzLCBpc0xvYWRpbmc6IGZhbHNlIH0pO1xuICAgICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICAgIGNvbnNvbGUuZXJyb3IoXCJFcnJvciBmZXRjaGluZyBpdGVtIGRldGFpbHM6XCIsIGVycm9yKTtcbiAgICAgICAgICBzZXQoe1xuICAgICAgICAgICAgZXJyb3I6XG4gICAgICAgICAgICAgIGVycm9yIGluc3RhbmNlb2YgRXJyb3JcbiAgICAgICAgICAgICAgICA/IGVycm9yLm1lc3NhZ2VcbiAgICAgICAgICAgICAgICA6IFwiRmFpbGVkIHRvIGZldGNoIGl0ZW0gZGV0YWlsc1wiLFxuICAgICAgICAgICAgaXNMb2FkaW5nOiBmYWxzZSxcbiAgICAgICAgICB9KTtcbiAgICAgICAgfVxuICAgICAgfSxcblxuICAgICAgLy8gVXBkYXRlIGl0ZW0gc3RhdHVzIHdpdGggZGVwZW5kZW5jeSB2YWxpZGF0aW9uXG4gICAgICB1cGRhdGVJdGVtU3RhdHVzOiBhc3luYyAoXG4gICAgICAgIGl0ZW1JZDogc3RyaW5nLFxuICAgICAgICBzdGF0dXM6IEJ1c2luZXNzSXRlbVtcInN0YXR1c1wiXSxcbiAgICAgICAgcHJvamVjdElkOiBzdHJpbmcgPSBcImRlZmF1bHRcIlxuICAgICAgKSA9PiB7XG4gICAgICAgIHRyeSB7XG4gICAgICAgICAgc2V0KHsgaXNMb2FkaW5nOiB0cnVlLCBlcnJvcjogbnVsbCB9KTtcblxuICAgICAgICAgIC8vIENoZWNrIGRlcGVuZGVuY2llcyBiZWZvcmUgYWxsb3dpbmcgc3RhdHVzIGNoYW5nZSB0byBcImNvbmZpcm1lZFwiXG4gICAgICAgICAgaWYgKHN0YXR1cyA9PT0gXCJjb25maXJtZWRcIikge1xuICAgICAgICAgICAgY29uc3QgY2FuUHJvZ3Jlc3MgPSBnZXQoKS5jaGVja0RlcGVuZGVuY2llcyhpdGVtSWQpO1xuICAgICAgICAgICAgaWYgKCFjYW5Qcm9ncmVzcykge1xuICAgICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoXG4gICAgICAgICAgICAgICAgXCJDYW5ub3QgbWFyayBhcyBjb25maXJtZWQ6IGRlcGVuZGVuY2llcyBub3Qgc2F0aXNmaWVkXCJcbiAgICAgICAgICAgICAgKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICB9XG5cbiAgICAgICAgICAvLyBVc2UgdGhlIEFQSSB0byB1cGRhdGUgdGhlIGl0ZW1cbiAgICAgICAgICBhd2FpdCBCdXNpbmVzc1NlY3Rpb25zQXBpLnVwZGF0ZUl0ZW0ocHJvamVjdElkLCBpdGVtSWQsIHsgc3RhdHVzIH0pO1xuXG4gICAgICAgICAgLy8gVXBkYXRlIGxvY2FsIHN0YXRlIGlmIHdlIGhhdmUgdGhlIGl0ZW0gc2VsZWN0ZWRcbiAgICAgICAgICBjb25zdCB7IHNlbGVjdGVkSXRlbSB9ID0gZ2V0KCk7XG4gICAgICAgICAgaWYgKHNlbGVjdGVkSXRlbSAmJiBzZWxlY3RlZEl0ZW0uaWQgPT09IGl0ZW1JZCkge1xuICAgICAgICAgICAgc2V0KHsgc2VsZWN0ZWRJdGVtOiB7IC4uLnNlbGVjdGVkSXRlbSwgc3RhdHVzIH0gfSk7XG4gICAgICAgICAgfVxuXG4gICAgICAgICAgc2V0KHsgaXNMb2FkaW5nOiBmYWxzZSB9KTtcbiAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgICBjb25zb2xlLmVycm9yKFwiRXJyb3IgdXBkYXRpbmcgaXRlbSBzdGF0dXM6XCIsIGVycm9yKTtcbiAgICAgICAgICBzZXQoe1xuICAgICAgICAgICAgZXJyb3I6XG4gICAgICAgICAgICAgIGVycm9yIGluc3RhbmNlb2YgRXJyb3JcbiAgICAgICAgICAgICAgICA/IGVycm9yLm1lc3NhZ2VcbiAgICAgICAgICAgICAgICA6IFwiRmFpbGVkIHRvIHVwZGF0ZSBpdGVtIHN0YXR1c1wiLFxuICAgICAgICAgICAgaXNMb2FkaW5nOiBmYWxzZSxcbiAgICAgICAgICB9KTtcbiAgICAgICAgfVxuICAgICAgfSxcblxuICAgICAgYWRkSXRlbURldGFpbDogKGRldGFpbCkgPT4ge1xuICAgICAgICBjb25zdCB7IHNlbGVjdGVkSXRlbSB9ID0gZ2V0KCk7XG4gICAgICAgIGlmICghc2VsZWN0ZWRJdGVtKSByZXR1cm47XG5cbiAgICAgICAgY29uc3QgdXBkYXRlZERldGFpbHMgPSBbLi4uZ2V0KCkuaXRlbURldGFpbHMsIGRldGFpbF07XG4gICAgICAgIHNldCh7IGl0ZW1EZXRhaWxzOiB1cGRhdGVkRGV0YWlscyB9KTtcblxuICAgICAgICAvLyBTYXZlIHRvIGJyb3dzZXIgc3RvcmFnZVxuICAgICAgICBicm93c2VyU3RvcmFnZVNlcnZpY2Uuc2F2ZUl0ZW1EZXRhaWxzKFxuICAgICAgICAgIHNlbGVjdGVkSXRlbS5pZCxcbiAgICAgICAgICB1cGRhdGVkRGV0YWlscyxcbiAgICAgICAgICBcImRlZmF1bHRcIlxuICAgICAgICApO1xuICAgICAgfSxcblxuICAgICAgdXBkYXRlSXRlbURldGFpbDogKGlkLCB1cGRhdGVzKSA9PiB7XG4gICAgICAgIGNvbnN0IHsgc2VsZWN0ZWRJdGVtIH0gPSBnZXQoKTtcbiAgICAgICAgaWYgKCFzZWxlY3RlZEl0ZW0pIHJldHVybjtcblxuICAgICAgICBjb25zdCB1cGRhdGVkRGV0YWlscyA9IGdldCgpLml0ZW1EZXRhaWxzLm1hcCgoaXRlbSkgPT5cbiAgICAgICAgICBpdGVtLmlkID09PSBpZFxuICAgICAgICAgICAgPyB7IC4uLml0ZW0sIC4uLnVwZGF0ZXMsIHVwZGF0ZWRBdDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpIH1cbiAgICAgICAgICAgIDogaXRlbVxuICAgICAgICApO1xuXG4gICAgICAgIHNldCh7IGl0ZW1EZXRhaWxzOiB1cGRhdGVkRGV0YWlscyB9KTtcblxuICAgICAgICAvLyBTYXZlIHRvIGJyb3dzZXIgc3RvcmFnZVxuICAgICAgICBicm93c2VyU3RvcmFnZVNlcnZpY2Uuc2F2ZUl0ZW1EZXRhaWxzKFxuICAgICAgICAgIHNlbGVjdGVkSXRlbS5pZCxcbiAgICAgICAgICB1cGRhdGVkRGV0YWlscyxcbiAgICAgICAgICBcImRlZmF1bHRcIlxuICAgICAgICApO1xuICAgICAgfSxcblxuICAgICAgcmVtb3ZlSXRlbURldGFpbDogKGlkKSA9PiB7XG4gICAgICAgIGNvbnN0IHsgc2VsZWN0ZWRJdGVtIH0gPSBnZXQoKTtcbiAgICAgICAgaWYgKCFzZWxlY3RlZEl0ZW0pIHJldHVybjtcblxuICAgICAgICBjb25zdCB1cGRhdGVkRGV0YWlscyA9IGdldCgpLml0ZW1EZXRhaWxzLmZpbHRlcihcbiAgICAgICAgICAoaXRlbSkgPT4gaXRlbS5pZCAhPT0gaWRcbiAgICAgICAgKTtcbiAgICAgICAgc2V0KHsgaXRlbURldGFpbHM6IHVwZGF0ZWREZXRhaWxzIH0pO1xuXG4gICAgICAgIC8vIFNhdmUgdG8gYnJvd3NlciBzdG9yYWdlXG4gICAgICAgIGJyb3dzZXJTdG9yYWdlU2VydmljZS5zYXZlSXRlbURldGFpbHMoXG4gICAgICAgICAgc2VsZWN0ZWRJdGVtLmlkLFxuICAgICAgICAgIHVwZGF0ZWREZXRhaWxzLFxuICAgICAgICAgIFwiZGVmYXVsdFwiXG4gICAgICAgICk7XG4gICAgICB9LFxuXG4gICAgICAvLyBHZXQgY29tcHJlaGVuc2l2ZSBpdGVtIGRhdGFcbiAgICAgIGdldEl0ZW1EYXRhOiAoaXRlbUlkOiBzdHJpbmcpID0+IHtcbiAgICAgICAgcmV0dXJuIGJ1c2luZXNzSXRlbXNEYXRhLmZpbmQoKGl0ZW0pID0+IGl0ZW0uaWQgPT09IGl0ZW1JZCk7XG4gICAgICB9LFxuXG4gICAgICAvLyBDaGVjayBpZiBpdGVtIGRlcGVuZGVuY2llcyBhcmUgc2F0aXNmaWVkXG4gICAgICBjaGVja0RlcGVuZGVuY2llczogKGl0ZW1JZDogc3RyaW5nKSA9PiB7XG4gICAgICAgIGNvbnN0IGl0ZW1EYXRhID0gYnVzaW5lc3NJdGVtc0RhdGEuZmluZCgoaXRlbSkgPT4gaXRlbS5pZCA9PT0gaXRlbUlkKTtcbiAgICAgICAgaWYgKFxuICAgICAgICAgICFpdGVtRGF0YSB8fFxuICAgICAgICAgICFpdGVtRGF0YS5kZXBlbmRlbmNpZXMgfHxcbiAgICAgICAgICBpdGVtRGF0YS5kZXBlbmRlbmNpZXMubGVuZ3RoID09PSAwXG4gICAgICAgICkge1xuICAgICAgICAgIHJldHVybiB0cnVlO1xuICAgICAgICB9XG5cbiAgICAgICAgcmV0dXJuIGl0ZW1EYXRhLmRlcGVuZGVuY2llcy5ldmVyeSgoZGVwSWQpID0+IHtcbiAgICAgICAgICBjb25zdCBkZXBJdGVtID0gYnVzaW5lc3NJdGVtc0RhdGEuZmluZCgoZGVwKSA9PiBkZXAuaWQgPT09IGRlcElkKTtcbiAgICAgICAgICByZXR1cm4gZGVwSXRlbT8uc3RhdHVzID09PSBcImNvbmZpcm1lZFwiO1xuICAgICAgICB9KTtcbiAgICAgIH0sXG5cbiAgICAgIC8vIEdldCByZWxhdGVkIGl0ZW1zIChkZXBlbmRlbmNpZXMgYW5kIGRlcGVuZGVudHMpXG4gICAgICBnZXRSZWxhdGVkSXRlbXM6IChpdGVtSWQ6IHN0cmluZykgPT4ge1xuICAgICAgICBjb25zdCBpdGVtRGF0YSA9IGJ1c2luZXNzSXRlbXNEYXRhLmZpbmQoKGl0ZW0pID0+IGl0ZW0uaWQgPT09IGl0ZW1JZCk7XG5cbiAgICAgICAgLy8gR2V0IGRlcGVuZGVuY2llc1xuICAgICAgICBjb25zdCBkZXBlbmRlbmNpZXMgPVxuICAgICAgICAgIGl0ZW1EYXRhPy5kZXBlbmRlbmNpZXNcbiAgICAgICAgICAgID8ubWFwKChkZXBJZCkgPT5cbiAgICAgICAgICAgICAgYnVzaW5lc3NJdGVtc0RhdGEuZmluZCgoaXRlbSkgPT4gaXRlbS5pZCA9PT0gZGVwSWQpXG4gICAgICAgICAgICApXG4gICAgICAgICAgICAuZmlsdGVyKEJvb2xlYW4pIHx8IFtdO1xuXG4gICAgICAgIC8vIEdldCBkZXBlbmRlbnRzIChpdGVtcyB0aGF0IGRlcGVuZCBvbiB0aGlzIG9uZSlcbiAgICAgICAgY29uc3QgZGVwZW5kZW50cyA9IGJ1c2luZXNzSXRlbXNEYXRhLmZpbHRlcigoaXRlbSkgPT5cbiAgICAgICAgICBpdGVtLmRlcGVuZGVuY2llcz8uaW5jbHVkZXMoaXRlbUlkKVxuICAgICAgICApO1xuXG4gICAgICAgIHJldHVybiB7IGRlcGVuZGVuY2llcywgZGVwZW5kZW50cyB9O1xuICAgICAgfSxcbiAgICB9KSxcbiAgICB7XG4gICAgICBuYW1lOiBcImJ1c2luZXNzLWl0ZW0tc3RvcmUtZW5oYW5jZWRcIixcbiAgICB9XG4gIClcbik7XG5cbi8vIEhlbHBlciBmdW5jdGlvbnMgZm9yIGVhc3kgYWNjZXNzXG5leHBvcnQgY29uc3QgZ2V0SXRlbURhdGEgPSAoaXRlbUlkOiBzdHJpbmcpID0+IHtcbiAgcmV0dXJuIGJ1c2luZXNzSXRlbXNEYXRhLmZpbmQoKGl0ZW0pID0+IGl0ZW0uaWQgPT09IGl0ZW1JZCk7XG59O1xuXG5leHBvcnQgY29uc3QgY2hlY2tJdGVtRGVwZW5kZW5jaWVzID0gKGl0ZW1JZDogc3RyaW5nKSA9PiB7XG4gIGNvbnN0IGl0ZW1EYXRhID0gYnVzaW5lc3NJdGVtc0RhdGEuZmluZCgoaXRlbSkgPT4gaXRlbS5pZCA9PT0gaXRlbUlkKTtcbiAgaWYgKFxuICAgICFpdGVtRGF0YSB8fFxuICAgICFpdGVtRGF0YS5kZXBlbmRlbmNpZXMgfHxcbiAgICBpdGVtRGF0YS5kZXBlbmRlbmNpZXMubGVuZ3RoID09PSAwXG4gICkge1xuICAgIHJldHVybiB0cnVlO1xuICB9XG5cbiAgcmV0dXJuIGl0ZW1EYXRhLmRlcGVuZGVuY2llcy5ldmVyeSgoZGVwSWQpID0+IHtcbiAgICBjb25zdCBkZXBJdGVtID0gYnVzaW5lc3NJdGVtc0RhdGEuZmluZCgoZGVwKSA9PiBkZXAuaWQgPT09IGRlcElkKTtcbiAgICByZXR1cm4gZGVwSXRlbT8uc3RhdHVzID09PSBcImNvbmZpcm1lZFwiO1xuICB9KTtcbn07XG5cbmV4cG9ydCBjb25zdCBnZXRSZWxhdGVkSXRlbXMgPSAoaXRlbUlkOiBzdHJpbmcpID0+IHtcbiAgY29uc3QgaXRlbURhdGEgPSBidXNpbmVzc0l0ZW1zRGF0YS5maW5kKChpdGVtKSA9PiBpdGVtLmlkID09PSBpdGVtSWQpO1xuXG4gIGNvbnN0IGRlcGVuZGVuY2llcyA9XG4gICAgaXRlbURhdGE/LmRlcGVuZGVuY2llc1xuICAgICAgPy5tYXAoKGRlcElkKSA9PiBidXNpbmVzc0l0ZW1zRGF0YS5maW5kKChpdGVtKSA9PiBpdGVtLmlkID09PSBkZXBJZCkpXG4gICAgICAuZmlsdGVyKEJvb2xlYW4pIHx8IFtdO1xuXG4gIGNvbnN0IGRlcGVuZGVudHMgPSBidXNpbmVzc0l0ZW1zRGF0YS5maWx0ZXIoKGl0ZW0pID0+XG4gICAgaXRlbS5kZXBlbmRlbmNpZXM/LmluY2x1ZGVzKGl0ZW1JZClcbiAgKTtcblxuICByZXR1cm4geyBkZXBlbmRlbmNpZXMsIGRlcGVuZGVudHMgfTtcbn07XG4iXSwibmFtZXMiOlsiYnVzaW5lc3NJdGVtc0RhdGEiLCJCdXNpbmVzc1NlY3Rpb25zQXBpIiwiY3JlYXRlIiwiZGV2dG9vbHMiLCJ1c2VCdXNpbmVzc0l0ZW1TdG9yZUVuaGFuY2VkIiwic2V0IiwiZ2V0Iiwic2VsZWN0ZWRJdGVtIiwiaXRlbURldGFpbHMiLCJpc0xvYWRpbmciLCJlcnJvciIsInNldFNlbGVjdGVkSXRlbSIsIml0ZW0iLCJzZXRJdGVtRGV0YWlscyIsImRldGFpbHMiLCJzZXRMb2FkaW5nIiwic2V0RXJyb3IiLCJmZXRjaEl0ZW1EZXRhaWxzIiwiaXRlbUlkIiwicHJvamVjdElkIiwiaXRlbURhdGEiLCJzdG9yZWREZXRhaWxzIiwiYnJvd3NlclN0b3JhZ2VTZXJ2aWNlIiwiZ2V0SXRlbURldGFpbHMiLCJjb25zb2xlIiwibG9nIiwiZmluZCIsImlkIiwiRXJyb3IiLCJpbml0aWFsRGV0YWlscyIsInZhbHVlcyIsImxlbmd0aCIsImZvckVhY2giLCJ2YWx1ZSIsImluZGV4IiwicHVzaCIsInRpdGxlIiwic3RhdHVzIiwiYWN0aW9ucyIsInJlc3VsdCIsImRlc2NyaXB0aW9uIiwicXVlc3Rpb24iLCJyZXBsYWNlIiwiY3JlYXRlZEF0IiwiRGF0ZSIsInRvSVNPU3RyaW5nIiwidXBkYXRlZEF0Iiwic2F2ZUl0ZW1EZXRhaWxzIiwibWVzc2FnZSIsInVwZGF0ZUl0ZW1TdGF0dXMiLCJjYW5Qcm9ncmVzcyIsImNoZWNrRGVwZW5kZW5jaWVzIiwidXBkYXRlSXRlbSIsImFkZEl0ZW1EZXRhaWwiLCJkZXRhaWwiLCJ1cGRhdGVkRGV0YWlscyIsInVwZGF0ZUl0ZW1EZXRhaWwiLCJ1cGRhdGVzIiwibWFwIiwicmVtb3ZlSXRlbURldGFpbCIsImZpbHRlciIsImdldEl0ZW1EYXRhIiwiZGVwZW5kZW5jaWVzIiwiZXZlcnkiLCJkZXBJZCIsImRlcEl0ZW0iLCJkZXAiLCJnZXRSZWxhdGVkSXRlbXMiLCJCb29sZWFuIiwiZGVwZW5kZW50cyIsImluY2x1ZGVzIiwibmFtZSIsImNoZWNrSXRlbURlcGVuZGVuY2llcyJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/stores/businessItemStoreEnhanced.ts\n"));

/***/ })

});