{"c": ["app/layout", "app/projects/[id]/page", "webpack"], "r": [], "m": ["(app-pages-browser)/./node_modules/@babel/runtime/helpers/esm/extends.js", "(app-pages-browser)/./node_modules/@hello-pangea/dnd/dist/dnd.esm.js", "(app-pages-browser)/./node_modules/@radix-ui/number/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-select/dist/index.mjs", "(app-pages-browser)/./node_modules/css-box-model/dist/css-box-model.esm.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/grip-vertical.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js", "(app-pages-browser)/./node_modules/raf-schd/dist/raf-schd.esm.js", "(app-pages-browser)/./node_modules/react-redux/dist/react-redux.mjs", "(app-pages-browser)/./node_modules/redux/dist/redux.mjs", "(app-pages-browser)/./node_modules/tiny-invariant/dist/esm/tiny-invariant.js", "(app-pages-browser)/./node_modules/use-sync-external-store/cjs/use-sync-external-store-with-selector.development.js", "(app-pages-browser)/./node_modules/use-sync-external-store/with-selector.js", "(app-pages-browser)/./src/components/EditableCell.tsx", "(app-pages-browser)/./src/components/business-item-table.tsx", "(app-pages-browser)/./src/components/ui/select.tsx", "(app-pages-browser)/./src/components/ui/table.tsx"]}