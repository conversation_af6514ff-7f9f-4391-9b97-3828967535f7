"use client";

import { BusinessSectionsDemo } from "@/components/business-sections-demo";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { ArrowLeft } from "lucide-react";
import Link from "next/link";

export default function BusinessSectionsDemoPage() {
  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <div className="border-b bg-white dark:bg-gray-800">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center gap-4">
            <Link 
              href="/user-dashboard" 
              className="flex items-center gap-2 text-muted-foreground hover:text-foreground transition-colors"
            >
              <ArrowLeft className="h-4 w-4" />
              Back to Dashboard
            </Link>
            <div className="flex items-center gap-3">
              <h1 className="text-2xl font-bold">Business Sections Demo</h1>
              <Badge variant="secondary">New Enhanced Version</Badge>
            </div>
          </div>
          <p className="text-muted-foreground mt-2">
            Comprehensive business planning with 23 items, dependency management, and smart recommendations
          </p>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-4 py-8">
        {/* Feature Highlights */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm">Total Items</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">23</div>
              <p className="text-xs text-muted-foreground">
                Across 4 categories
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm">Dependency System</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">✓</div>
              <p className="text-xs text-muted-foreground">
                Smart progression logic
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm">Status Tracking</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">4</div>
              <p className="text-xs text-muted-foreground">
                Status levels with opacity
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm">API Ready</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">✓</div>
              <p className="text-xs text-muted-foreground">
                Easy backend integration
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Demo Component */}
        <BusinessSectionsDemo />

        {/* Implementation Notes */}
        <Card className="mt-8">
          <CardHeader>
            <CardTitle>Implementation Notes</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <h4 className="font-medium mb-2">Key Features Implemented:</h4>
              <ul className="text-sm text-muted-foreground space-y-1 list-disc list-inside">
                <li>23 comprehensive business items with realistic data</li>
                <li>Dependency management system preventing invalid progressions</li>
                <li>Status-based card opacity (10%, 20%, 50%, 100%)</li>
                <li>Smart recommendations based on dependency satisfaction</li>
                <li>Enhanced UI with tooltips and progress indicators</li>
                <li>API-ready service layer for easy backend integration</li>
                <li>Comprehensive analytics and progress tracking</li>
              </ul>
            </div>
            
            <div>
              <h4 className="font-medium mb-2">Data Structure:</h4>
              <ul className="text-sm text-muted-foreground space-y-1 list-disc list-inside">
                <li><strong>Market:</strong> Problem, UVP, Audience, Alternatives, Market Size, Trends</li>
                <li><strong>Solution:</strong> Why, Advantages, Product, Tech, Packages</li>
                <li><strong>Sales & Marketing:</strong> Positioning, Channels, Messaging, Brand, Assets, Sales Motion, Metrics</li>
                <li><strong>Company:</strong> Risks, Business Model, Revenue, Cost, Team</li>
              </ul>
            </div>
            
            <div>
              <h4 className="font-medium mb-2">Integration:</h4>
              <p className="text-sm text-muted-foreground">
                The new system is now active in your project pages. Visit any project to see the enhanced 
                business sections with dependency management and improved UI. The old system has been 
                completely replaced with the new comprehensive version.
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
