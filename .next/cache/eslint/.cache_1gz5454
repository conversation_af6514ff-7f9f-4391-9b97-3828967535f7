[{"/Users/<USER>/Data/new era/siift-next/src/app/admin/activity/page.tsx": "1", "/Users/<USER>/Data/new era/siift-next/src/app/admin/agent/calls/page.tsx": "2", "/Users/<USER>/Data/new era/siift-next/src/app/admin/agent/token-trends/page.tsx": "3", "/Users/<USER>/Data/new era/siift-next/src/app/admin/agent/usage-stats/page.tsx": "4", "/Users/<USER>/Data/new era/siift-next/src/app/admin/analytics/activity-metrics/page.tsx": "5", "/Users/<USER>/Data/new era/siift-next/src/app/admin/analytics/activity-trends/page.tsx": "6", "/Users/<USER>/Data/new era/siift-next/src/app/admin/analytics/feedbacks/page.tsx": "7", "/Users/<USER>/Data/new era/siift-next/src/app/admin/analytics/summary/page.tsx": "8", "/Users/<USER>/Data/new era/siift-next/src/app/admin/analytics/users/page.tsx": "9", "/Users/<USER>/Data/new era/siift-next/src/app/admin/api-test/page.tsx": "10", "/Users/<USER>/Data/new era/siift-next/src/app/admin/health/page.tsx": "11", "/Users/<USER>/Data/new era/siift-next/src/app/admin/notifications/page.tsx": "12", "/Users/<USER>/Data/new era/siift-next/src/app/admin/page.tsx": "13", "/Users/<USER>/Data/new era/siift-next/src/app/admin/profile/page.tsx": "14", "/Users/<USER>/Data/new era/siift-next/src/app/admin/projects/all/page.tsx": "15", "/Users/<USER>/Data/new era/siift-next/src/app/admin/projects/create/page.tsx": "16", "/Users/<USER>/Data/new era/siift-next/src/app/admin/projects/page.tsx": "17", "/Users/<USER>/Data/new era/siift-next/src/app/admin/recent/page.tsx": "18", "/Users/<USER>/Data/new era/siift-next/src/app/admin/settings/page.tsx": "19", "/Users/<USER>/Data/new era/siift-next/src/app/admin/settings-tab/page.tsx": "20", "/Users/<USER>/Data/new era/siift-next/src/app/admin/system/health/page.tsx": "21", "/Users/<USER>/Data/new era/siift-next/src/app/admin/trends/page.tsx": "22", "/Users/<USER>/Data/new era/siift-next/src/app/admin/users/page.tsx": "23", "/Users/<USER>/Data/new era/siift-next/src/app/api/auth/me/route.ts": "24", "/Users/<USER>/Data/new era/siift-next/src/app/api/auth/signin/route.ts": "25", "/Users/<USER>/Data/new era/siift-next/src/app/api/health/route.ts": "26", "/Users/<USER>/Data/new era/siift-next/src/app/api/projects/[id]/route.ts": "27", "/Users/<USER>/Data/new era/siift-next/src/app/api/projects/route.ts": "28", "/Users/<USER>/Data/new era/siift-next/src/app/api/webhooks/clerk/route.ts": "29", "/Users/<USER>/Data/new era/siift-next/src/app/auth/forgot-password/page.tsx": "30", "/Users/<USER>/Data/new era/siift-next/src/app/auth/layout.tsx": "31", "/Users/<USER>/Data/new era/siift-next/src/app/auth/login/page.tsx": "32", "/Users/<USER>/Data/new era/siift-next/src/app/auth/login/sso-callback/page.tsx": "33", "/Users/<USER>/Data/new era/siift-next/src/app/auth/register/page.tsx": "34", "/Users/<USER>/Data/new era/siift-next/src/app/auth/register/sso-callback/page.tsx": "35", "/Users/<USER>/Data/new era/siift-next/src/app/auth/reset-password/page.tsx": "36", "/Users/<USER>/Data/new era/siift-next/src/app/auth/verify-email/page.tsx": "37", "/Users/<USER>/Data/new era/siift-next/src/app/layout.tsx": "38", "/Users/<USER>/Data/new era/siift-next/src/app/page.tsx": "39", "/Users/<USER>/Data/new era/siift-next/src/app/profile/page.tsx": "40", "/Users/<USER>/Data/new era/siift-next/src/app/projects/[id]/edit/page.tsx": "41", "/Users/<USER>/Data/new era/siift-next/src/app/projects/[id]/page.tsx": "42", "/Users/<USER>/Data/new era/siift-next/src/app/projects/create/page.tsx": "43", "/Users/<USER>/Data/new era/siift-next/src/app/projects/new/page.tsx": "44", "/Users/<USER>/Data/new era/siift-next/src/app/projects/page.tsx": "45", "/Users/<USER>/Data/new era/siift-next/src/app/settings/page.tsx": "46", "/Users/<USER>/Data/new era/siift-next/src/app/sso-callback/page.tsx": "47", "/Users/<USER>/Data/new era/siift-next/src/app/user-dashboard/page.tsx": "48", "/Users/<USER>/Data/new era/siift-next/src/components/admin-sidebar.tsx": "49", "/Users/<USER>/Data/new era/siift-next/src/components/admin-tabbed-content.tsx": "50", "/Users/<USER>/Data/new era/siift-next/src/components/auth/admin-route.tsx": "51", "/Users/<USER>/Data/new era/siift-next/src/components/auth/auth-card.tsx": "52", "/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGrid.tsx": "53", "/Users/<USER>/Data/new era/siift-next/src/components/dashboard/DashboardHeader.tsx": "54", "/Users/<USER>/Data/new era/siift-next/src/components/dashboard/dashboard-hero-section.tsx": "55", "/Users/<USER>/Data/new era/siift-next/src/components/dashboard/dashboard-projects-section.tsx": "56", "/Users/<USER>/Data/new era/siift-next/src/components/dashboard/dashboard-stats-cards.tsx": "57", "/Users/<USER>/Data/new era/siift-next/src/components/landing/feature-grid.tsx": "58", "/Users/<USER>/Data/new era/siift-next/src/components/landing/hero-section.tsx": "59", "/Users/<USER>/Data/new era/siift-next/src/components/landing/landing-page.tsx": "60", "/Users/<USER>/Data/new era/siift-next/src/components/layout/admin-layout.tsx": "61", "/Users/<USER>/Data/new era/siift-next/src/components/layout/dashboard-layout.tsx": "62", "/Users/<USER>/Data/new era/siift-next/src/components/layout/footer.tsx": "63", "/Users/<USER>/Data/new era/siift-next/src/components/layout/header.tsx": "64", "/Users/<USER>/Data/new era/siift-next/src/components/layout/main-layout.tsx": "65", "/Users/<USER>/Data/new era/siift-next/src/components/nav-main.tsx": "66", "/Users/<USER>/Data/new era/siift-next/src/components/nav-user.tsx": "67", "/Users/<USER>/Data/new era/siift-next/src/components/navigation/main-nav.tsx": "68", "/Users/<USER>/Data/new era/siift-next/src/components/navigation/mobile-nav.tsx": "69", "/Users/<USER>/Data/new era/siift-next/src/components/navigation/user-menu.tsx": "70", "/Users/<USER>/Data/new era/siift-next/src/components/project-chat-sidebar.tsx": "71", "/Users/<USER>/Data/new era/siift-next/src/components/project-creation/ProjectCreationAnimation.tsx": "72", "/Users/<USER>/Data/new era/siift-next/src/components/project-sidebar.tsx": "73", "/Users/<USER>/Data/new era/siift-next/src/components/providers/ClerkSessionProvider.tsx": "74", "/Users/<USER>/Data/new era/siift-next/src/components/providers/QueryProvider.tsx": "75", "/Users/<USER>/Data/new era/siift-next/src/components/providers/SessionProvider.tsx": "76", "/Users/<USER>/Data/new era/siift-next/src/components/team-switcher.tsx": "77", "/Users/<USER>/Data/new era/siift-next/src/components/theme-provider.tsx": "78", "/Users/<USER>/Data/new era/siift-next/src/components/theme-toggle.tsx": "79", "/Users/<USER>/Data/new era/siift-next/src/components/ui/app-loading.tsx": "80", "/Users/<USER>/Data/new era/siift-next/src/components/ui/avatar.tsx": "81", "/Users/<USER>/Data/new era/siift-next/src/components/ui/background-beams.tsx": "82", "/Users/<USER>/Data/new era/siift-next/src/components/ui/badge.tsx": "83", "/Users/<USER>/Data/new era/siift-next/src/components/ui/breadcrumb.tsx": "84", "/Users/<USER>/Data/new era/siift-next/src/components/ui/button.tsx": "85", "/Users/<USER>/Data/new era/siift-next/src/components/ui/card.tsx": "86", "/Users/<USER>/Data/new era/siift-next/src/components/ui/chat-bubble.tsx": "87", "/Users/<USER>/Data/new era/siift-next/src/components/ui/chat-message-list.tsx": "88", "/Users/<USER>/Data/new era/siift-next/src/components/ui/checkbox.tsx": "89", "/Users/<USER>/Data/new era/siift-next/src/components/ui/collapsible.tsx": "90", "/Users/<USER>/Data/new era/siift-next/src/components/ui/dropdown-menu.tsx": "91", "/Users/<USER>/Data/new era/siift-next/src/components/ui/form-input.tsx": "92", "/Users/<USER>/Data/new era/siift-next/src/components/ui/form.tsx": "93", "/Users/<USER>/Data/new era/siift-next/src/components/ui/glow.tsx": "94", "/Users/<USER>/Data/new era/siift-next/src/components/ui/icons.tsx": "95", "/Users/<USER>/Data/new era/siift-next/src/components/ui/input.tsx": "96", "/Users/<USER>/Data/new era/siift-next/src/components/ui/label.tsx": "97", "/Users/<USER>/Data/new era/siift-next/src/components/ui/logo.tsx": "98", "/Users/<USER>/Data/new era/siift-next/src/components/ui/message-loading.tsx": "99", "/Users/<USER>/Data/new era/siift-next/src/components/ui/mockup.tsx": "100", "/Users/<USER>/Data/new era/siift-next/src/components/ui/navigation-menu.tsx": "101", "/Users/<USER>/Data/new era/siift-next/src/components/ui/progress.tsx": "102", "/Users/<USER>/Data/new era/siift-next/src/components/ui/scroll-area.tsx": "103", "/Users/<USER>/Data/new era/siift-next/src/components/ui/select.tsx": "104", "/Users/<USER>/Data/new era/siift-next/src/components/ui/separator.tsx": "105", "/Users/<USER>/Data/new era/siift-next/src/components/ui/sheet.tsx": "106", "/Users/<USER>/Data/new era/siift-next/src/components/ui/sidebar-button.tsx": "107", "/Users/<USER>/Data/new era/siift-next/src/components/ui/sidebar.tsx": "108", "/Users/<USER>/Data/new era/siift-next/src/components/ui/skeleton.tsx": "109", "/Users/<USER>/Data/new era/siift-next/src/components/ui/sonner.tsx": "110", "/Users/<USER>/Data/new era/siift-next/src/components/ui/switch.tsx": "111", "/Users/<USER>/Data/new era/siift-next/src/components/ui/tabs.tsx": "112", "/Users/<USER>/Data/new era/siift-next/src/components/ui/testimonials-columns-1.tsx": "113", "/Users/<USER>/Data/new era/siift-next/src/components/ui/testimonials.tsx": "114", "/Users/<USER>/Data/new era/siift-next/src/components/ui/textarea.tsx": "115", "/Users/<USER>/Data/new era/siift-next/src/components/ui/tooltip.tsx": "116", "/Users/<USER>/Data/new era/siift-next/src/components/ui/waitlist-section.tsx": "117", "/Users/<USER>/Data/new era/siift-next/src/components/user-tabbed-content.tsx": "118", "/Users/<USER>/Data/new era/siift-next/src/contexts/auth-context.tsx": "119", "/Users/<USER>/Data/new era/siift-next/src/contexts/background-context.tsx": "120", "/Users/<USER>/Data/new era/siift-next/src/hooks/mutations/useUserMutations.ts": "121", "/Users/<USER>/Data/new era/siift-next/src/hooks/queries/useUser.ts": "122", "/Users/<USER>/Data/new era/siift-next/src/hooks/use-auto-scroll.ts": "123", "/Users/<USER>/Data/new era/siift-next/src/hooks/use-mobile.ts": "124", "/Users/<USER>/Data/new era/siift-next/src/hooks/useAuth.ts": "125", "/Users/<USER>/Data/new era/siift-next/src/hooks/useClerkAuth.ts": "126", "/Users/<USER>/Data/new era/siift-next/src/hooks/useResizable.ts": "127", "/Users/<USER>/Data/new era/siift-next/src/hooks/useToast.ts": "128", "/Users/<USER>/Data/new era/siift-next/src/hooks/useUserSync.ts": "129", "/Users/<USER>/Data/new era/siift-next/src/lib/admin-api.ts": "130", "/Users/<USER>/Data/new era/siift-next/src/lib/api.ts": "131", "/Users/<USER>/Data/new era/siift-next/src/lib/apiClient.ts": "132", "/Users/<USER>/Data/new era/siift-next/src/lib/businessSectionsData.ts": "133", "/Users/<USER>/Data/new era/siift-next/src/lib/clerk-api.ts": "134", "/Users/<USER>/Data/new era/siift-next/src/lib/constants.ts": "135", "/Users/<USER>/Data/new era/siift-next/src/lib/fonts.ts": "136", "/Users/<USER>/Data/new era/siift-next/src/lib/jwt.ts": "137", "/Users/<USER>/Data/new era/siift-next/src/lib/mock-auth-api.ts": "138", "/Users/<USER>/Data/new era/siift-next/src/lib/mock-email-service.ts": "139", "/Users/<USER>/Data/new era/siift-next/src/lib/mockEventStream.ts": "140", "/Users/<USER>/Data/new era/siift-next/src/lib/projectCreationConfig.ts": "141", "/Users/<USER>/Data/new era/siift-next/src/lib/queryClient.ts": "142", "/Users/<USER>/Data/new era/siift-next/src/lib/realEventStream.ts": "143", "/Users/<USER>/Data/new era/siift-next/src/lib/session.ts": "144", "/Users/<USER>/Data/new era/siift-next/src/lib/tokenStorage.ts": "145", "/Users/<USER>/Data/new era/siift-next/src/lib/types.ts": "146", "/Users/<USER>/Data/new era/siift-next/src/lib/utils.ts": "147", "/Users/<USER>/Data/new era/siift-next/src/middleware.ts": "148", "/Users/<USER>/Data/new era/siift-next/src/stores/businessSectionStore.ts": "149", "/Users/<USER>/Data/new era/siift-next/src/stores/projectCreationStore.ts": "150", "/Users/<USER>/Data/new era/siift-next/src/stores/sessionStore.ts": "151", "/Users/<USER>/Data/new era/siift-next/src/types/BusinessSection.types.ts": "152", "/Users/<USER>/Data/new era/siift-next/src/types/Session.types.ts": "153", "/Users/<USER>/Data/new era/siift-next/src/types/email.types.ts": "154", "/Users/<USER>/Data/new era/siift-next/src/hooks/useClerkApi.ts": "155", "/Users/<USER>/Data/new era/siift-next/src/app/robots.ts": "156", "/Users/<USER>/Data/new era/siift-next/src/app/sitemap.ts": "157", "/Users/<USER>/Data/new era/siift-next/src/components/analytics/PostHogProvider.tsx": "158", "/Users/<USER>/Data/new era/siift-next/src/components/seo/SEOHead.tsx": "159", "/Users/<USER>/Data/new era/siift-next/src/components/seo/StructuredData.tsx": "160", "/Users/<USER>/Data/new era/siift-next/src/hooks/useAnalytics.ts": "161", "/Users/<USER>/Data/new era/siift-next/src/app/admin/layout.tsx": "162", "/Users/<USER>/Data/new era/siift-next/src/app/debug/tokens/page.tsx": "163", "/Users/<USER>/Data/new era/siift-next/src/app/error.tsx": "164", "/Users/<USER>/Data/new era/siift-next/src/app/loading.tsx": "165", "/Users/<USER>/Data/new era/siift-next/src/app/not-found.tsx": "166", "/Users/<USER>/Data/new era/siift-next/src/components/EditableCell.tsx": "167", "/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx": "168", "/Users/<USER>/Data/new era/siift-next/src/components/debug/ClerkTokenDebug.tsx": "169", "/Users/<USER>/Data/new era/siift-next/src/components/ui/table.tsx": "170", "/Users/<USER>/Data/new era/siift-next/src/hooks/useClerkToken.ts": "171", "/Users/<USER>/Data/new era/siift-next/src/stores/businessItemStore.ts": "172", "/Users/<USER>/Data/new era/siift-next/src/components/ui/resize-handle.tsx": "173", "/Users/<USER>/Data/new era/siift-next/src/app/debug/page.tsx": "174", "/Users/<USER>/Data/new era/siift-next/src/components/debug/PostHogDebug.tsx": "175", "/Users/<USER>/Data/new era/siift-next/src/components/debug/ClerkTokenExample.tsx": "176", "/Users/<USER>/Data/new era/siift-next/src/app/about/page.tsx": "177", "/Users/<USER>/Data/new era/siift-next/src/app/blog/[slug]/not-found.tsx": "178", "/Users/<USER>/Data/new era/siift-next/src/app/blog/[slug]/page.tsx": "179", "/Users/<USER>/Data/new era/siift-next/src/app/blog/page.tsx": "180", "/Users/<USER>/Data/new era/siift-next/src/app/test-ai-input/page.tsx": "181", "/Users/<USER>/Data/new era/siift-next/src/components/blog/blog-card.tsx": "182", "/Users/<USER>/Data/new era/siift-next/src/components/blog/blog-section.tsx": "183", "/Users/<USER>/Data/new era/siift-next/src/components/ui/ai-prompt-demo.tsx": "184", "/Users/<USER>/Data/new era/siift-next/src/components/ui/animated-ai-input.tsx": "185", "/Users/<USER>/Data/new era/siift-next/src/data/blog-posts.ts": "186", "/Users/<USER>/Data/new era/siift-next/src/types/blog.ts": "187", "/Users/<USER>/Data/new era/siift-next/src/components/project/ContentSections.tsx": "188", "/Users/<USER>/Data/new era/siift-next/src/components/project/PrioritiesDropdown.tsx": "189", "/Users/<USER>/Data/new era/siift-next/src/components/project/ProgressBar.tsx": "190", "/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx": "191", "/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectHeader.tsx": "192", "/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectMainContent.tsx": "193", "/Users/<USER>/Data/new era/siift-next/src/components/project/index.ts": "194", "/Users/<USER>/Data/new era/siift-next/src/components/shared/ProjectInputSection.tsx": "195", "/Users/<USER>/Data/new era/siift-next/src/components/ui/dotted-background.tsx": "196", "/Users/<USER>/Data/new era/siift-next/src/components/ui/popover.tsx": "197", "/Users/<USER>/Data/new era/siift-next/src/lib/design-tokens.ts": "198", "/Users/<USER>/Data/new era/siift-next/src/mockdata/businessItemQuestions.ts": "199"}, {"size": 14293, "mtime": 1753675505450, "results": "200", "hashOfConfig": "201"}, {"size": 17842, "mtime": 1753675505489, "results": "202", "hashOfConfig": "201"}, {"size": 15868, "mtime": 1753675505503, "results": "203", "hashOfConfig": "201"}, {"size": 13664, "mtime": 1753675505496, "results": "204", "hashOfConfig": "201"}, {"size": 9942, "mtime": 1753675505565, "results": "205", "hashOfConfig": "201"}, {"size": 11912, "mtime": 1753675505594, "results": "206", "hashOfConfig": "201"}, {"size": 19398, "mtime": 1753675505584, "results": "207", "hashOfConfig": "201"}, {"size": 8154, "mtime": 1753675505601, "results": "208", "hashOfConfig": "201"}, {"size": 12828, "mtime": 1753675505575, "results": "209", "hashOfConfig": "201"}, {"size": 27409, "mtime": 1754123165734, "results": "210", "hashOfConfig": "201"}, {"size": 13221, "mtime": 1753675505478, "results": "211", "hashOfConfig": "201"}, {"size": 663, "mtime": 1753675505559, "results": "212", "hashOfConfig": "201"}, {"size": 761, "mtime": 1753675505552, "results": "213", "hashOfConfig": "201"}, {"size": 644, "mtime": 1753675505518, "results": "214", "hashOfConfig": "201"}, {"size": 645, "mtime": 1753675505456, "results": "215", "hashOfConfig": "201"}, {"size": 3723, "mtime": 1753675505471, "results": "216", "hashOfConfig": "201"}, {"size": 640, "mtime": 1753675505464, "results": "217", "hashOfConfig": "201"}, {"size": 648, "mtime": 1753675505608, "results": "218", "hashOfConfig": "201"}, {"size": 11761, "mtime": 1753675505443, "results": "219", "hashOfConfig": "201"}, {"size": 645, "mtime": 1753675505546, "results": "220", "hashOfConfig": "201"}, {"size": 13284, "mtime": 1753675505511, "results": "221", "hashOfConfig": "201"}, {"size": 12981, "mtime": 1753675505530, "results": "222", "hashOfConfig": "201"}, {"size": 13219, "mtime": 1753675505538, "results": "223", "hashOfConfig": "201"}, {"size": 1097, "mtime": 1753595146912, "results": "224", "hashOfConfig": "201"}, {"size": 1919, "mtime": 1753595158639, "results": "225", "hashOfConfig": "201"}, {"size": 992, "mtime": 1752468771519, "results": "226", "hashOfConfig": "201"}, {"size": 3891, "mtime": 1753672539953, "results": "227", "hashOfConfig": "201"}, {"size": 2881, "mtime": 1753595118765, "results": "228", "hashOfConfig": "201"}, {"size": 5436, "mtime": 1753672571172, "results": "229", "hashOfConfig": "201"}, {"size": 3893, "mtime": 1753675518355, "results": "230", "hashOfConfig": "201"}, {"size": 1303, "mtime": 1753675630418, "results": "231", "hashOfConfig": "201"}, {"size": 10224, "mtime": 1753675518375, "results": "232", "hashOfConfig": "201"}, {"size": 259, "mtime": 1753675518368, "results": "233", "hashOfConfig": "201"}, {"size": 13670, "mtime": 1753675518348, "results": "234", "hashOfConfig": "201"}, {"size": 259, "mtime": 1753675518342, "results": "235", "hashOfConfig": "201"}, {"size": 12893, "mtime": 1753675518361, "results": "236", "hashOfConfig": "201"}, {"size": 9905, "mtime": 1753675518335, "results": "237", "hashOfConfig": "201"}, {"size": 2963, "mtime": 1753673250076, "results": "238", "hashOfConfig": "201"}, {"size": 1172, "mtime": 1753673054748, "results": "239", "hashOfConfig": "201"}, {"size": 12835, "mtime": 1753675745805, "results": "240", "hashOfConfig": "201"}, {"size": 8822, "mtime": 1753675760836, "results": "241", "hashOfConfig": "201"}, {"size": 6566, "mtime": 1754334672916, "results": "242", "hashOfConfig": "201"}, {"size": 766, "mtime": 1753158462887, "results": "243", "hashOfConfig": "201"}, {"size": 5085, "mtime": 1753675869095, "results": "244", "hashOfConfig": "201"}, {"size": 9642, "mtime": 1753595833363, "results": "245", "hashOfConfig": "201"}, {"size": 19859, "mtime": 1753675760823, "results": "246", "hashOfConfig": "201"}, {"size": 613, "mtime": 1753665982933, "results": "247", "hashOfConfig": "201"}, {"size": 3899, "mtime": 1754334736954, "results": "248", "hashOfConfig": "201"}, {"size": 5728, "mtime": 1752982830884, "results": "249", "hashOfConfig": "201"}, {"size": 17129, "mtime": 1753596187151, "results": "250", "hashOfConfig": "201"}, {"size": 2425, "mtime": 1752998545730, "results": "251", "hashOfConfig": "201"}, {"size": 1390, "mtime": 1753853005228, "results": "252", "hashOfConfig": "201"}, {"size": 5378, "mtime": 1754331668380, "results": "253", "hashOfConfig": "201"}, {"size": 4728, "mtime": 1753852870110, "results": "254", "hashOfConfig": "201"}, {"size": 622, "mtime": 1754334754119, "results": "255", "hashOfConfig": "201"}, {"size": 6039, "mtime": 1753662236525, "results": "256", "hashOfConfig": "201"}, {"size": 4211, "mtime": 1753662236981, "results": "257", "hashOfConfig": "201"}, {"size": 3966, "mtime": 1753595529167, "results": "258", "hashOfConfig": "201"}, {"size": 12305, "mtime": 1754336700487, "results": "259", "hashOfConfig": "201"}, {"size": 1211, "mtime": 1754328801788, "results": "260", "hashOfConfig": "201"}, {"size": 3642, "mtime": 1752981130099, "results": "261", "hashOfConfig": "201"}, {"size": 392, "mtime": 1754336730216, "results": "262", "hashOfConfig": "201"}, {"size": 5758, "mtime": 1753853710936, "results": "263", "hashOfConfig": "201"}, {"size": 1254, "mtime": 1753852918837, "results": "264", "hashOfConfig": "201"}, {"size": 682, "mtime": 1753593043509, "results": "265", "hashOfConfig": "201"}, {"size": 3421, "mtime": 1752982524772, "results": "266", "hashOfConfig": "201"}, {"size": 5318, "mtime": 1753663916166, "results": "267", "hashOfConfig": "201"}, {"size": 2078, "mtime": 1754336685180, "results": "268", "hashOfConfig": "201"}, {"size": 2778, "mtime": 1754336697245, "results": "269", "hashOfConfig": "201"}, {"size": 2901, "mtime": 1753663572932, "results": "270", "hashOfConfig": "201"}, {"size": 5963, "mtime": 1754121922086, "results": "271", "hashOfConfig": "201"}, {"size": 8468, "mtime": 1754331173299, "results": "272", "hashOfConfig": "201"}, {"size": 11280, "mtime": 1754334972793, "results": "273", "hashOfConfig": "201"}, {"size": 5191, "mtime": 1753686888552, "results": "274", "hashOfConfig": "201"}, {"size": 652, "mtime": 1753664896734, "results": "275", "hashOfConfig": "201"}, {"size": 2110, "mtime": 1752972043397, "results": "276", "hashOfConfig": "201"}, {"size": 3296, "mtime": 1753597668044, "results": "277", "hashOfConfig": "201"}, {"size": 457, "mtime": 1752998625992, "results": "278", "hashOfConfig": "201"}, {"size": 2056, "mtime": 1753662232836, "results": "279", "hashOfConfig": "201"}, {"size": 2136, "mtime": 1752968997388, "results": "280", "hashOfConfig": "201"}, {"size": 1097, "mtime": 1752024546459, "results": "281", "hashOfConfig": "201"}, {"size": 9832, "mtime": 1752999819120, "results": "282", "hashOfConfig": "201"}, {"size": 1631, "mtime": 1752025897263, "results": "283", "hashOfConfig": "201"}, {"size": 2357, "mtime": 1752980497582, "results": "284", "hashOfConfig": "201"}, {"size": 4159, "mtime": 1753857540369, "results": "285", "hashOfConfig": "201"}, {"size": 1976, "mtime": 1753599324700, "results": "286", "hashOfConfig": "201"}, {"size": 2342, "mtime": 1754025864774, "results": "287", "hashOfConfig": "201"}, {"size": 1511, "mtime": 1753594097252, "results": "288", "hashOfConfig": "201"}, {"size": 1236, "mtime": 1753597378483, "results": "289", "hashOfConfig": "201"}, {"size": 800, "mtime": 1752980497583, "results": "290", "hashOfConfig": "201"}, {"size": 8305, "mtime": 1753662234042, "results": "291", "hashOfConfig": "201"}, {"size": 1446, "mtime": 1752712464418, "results": "292", "hashOfConfig": "201"}, {"size": 3759, "mtime": 1752024533987, "results": "293", "hashOfConfig": "201"}, {"size": 1339, "mtime": 1753000764590, "results": "294", "hashOfConfig": "201"}, {"size": 12774, "mtime": 1752994552413, "results": "295", "hashOfConfig": "201"}, {"size": 967, "mtime": 1752980497575, "results": "296", "hashOfConfig": "201"}, {"size": 611, "mtime": 1752024533969, "results": "297", "hashOfConfig": "201"}, {"size": 4133, "mtime": 1753941998467, "results": "298", "hashOfConfig": "201"}, {"size": 1189, "mtime": 1752998409681, "results": "299", "hashOfConfig": "201"}, {"size": 1491, "mtime": 1752994420851, "results": "300", "hashOfConfig": "201"}, {"size": 6664, "mtime": 1752024546470, "results": "301", "hashOfConfig": "201"}, {"size": 1260, "mtime": 1754332261781, "results": "302", "hashOfConfig": "201"}, {"size": 1645, "mtime": 1752984133492, "results": "303", "hashOfConfig": "201"}, {"size": 6253, "mtime": 1753677432274, "results": "304", "hashOfConfig": "201"}, {"size": 699, "mtime": 1752980495473, "results": "305", "hashOfConfig": "201"}, {"size": 4090, "mtime": 1752980496414, "results": "306", "hashOfConfig": "201"}, {"size": 5606, "mtime": 1754335485078, "results": "307", "hashOfConfig": "201"}, {"size": 21633, "mtime": 1753682799163, "results": "308", "hashOfConfig": "201"}, {"size": 276, "mtime": 1752980497578, "results": "309", "hashOfConfig": "201"}, {"size": 1813, "mtime": 1752973899761, "results": "310", "hashOfConfig": "201"}, {"size": 1173, "mtime": 1752458895628, "results": "311", "hashOfConfig": "201"}, {"size": 1969, "mtime": 1752465420982, "results": "312", "hashOfConfig": "201"}, {"size": 2582, "mtime": 1752999564859, "results": "313", "hashOfConfig": "201"}, {"size": 3732, "mtime": 1752999587832, "results": "314", "hashOfConfig": "201"}, {"size": 778, "mtime": 1752305269639, "results": "315", "hashOfConfig": "201"}, {"size": 1891, "mtime": 1752980496419, "results": "316", "hashOfConfig": "201"}, {"size": 4459, "mtime": 1753854489718, "results": "317", "hashOfConfig": "201"}, {"size": 21638, "mtime": 1754332270735, "results": "318", "hashOfConfig": "201"}, {"size": 8062, "mtime": 1752908182083, "results": "319", "hashOfConfig": "201"}, {"size": 1810, "mtime": 1752973774511, "results": "320", "hashOfConfig": "201"}, {"size": 9145, "mtime": 1753672623962, "results": "321", "hashOfConfig": "201"}, {"size": 4951, "mtime": 1753672386205, "results": "322", "hashOfConfig": "201"}, {"size": 3596, "mtime": 1752998402592, "results": "323", "hashOfConfig": "201"}, {"size": 565, "mtime": 1752980497577, "results": "324", "hashOfConfig": "201"}, {"size": 3836, "mtime": 1752972841718, "results": "325", "hashOfConfig": "201"}, {"size": 1451, "mtime": 1753661383143, "results": "326", "hashOfConfig": "201"}, {"size": 2522, "mtime": 1753846474177, "results": "327", "hashOfConfig": "201"}, {"size": 2422, "mtime": 1752973002784, "results": "328", "hashOfConfig": "201"}, {"size": 2489, "mtime": 1753672396206, "results": "329", "hashOfConfig": "201"}, {"size": 9688, "mtime": 1752470232696, "results": "330", "hashOfConfig": "201"}, {"size": 3441, "mtime": 1752460198072, "results": "331", "hashOfConfig": "201"}, {"size": 6817, "mtime": 1752998709744, "results": "332", "hashOfConfig": "201"}, {"size": 5052, "mtime": 1753686854520, "results": "333", "hashOfConfig": "201"}, {"size": 4246, "mtime": 1753672708155, "results": "334", "hashOfConfig": "201"}, {"size": 1636, "mtime": 1753578806610, "results": "335", "hashOfConfig": "201"}, {"size": 1473, "mtime": 1752995579929, "results": "336", "hashOfConfig": "201"}, {"size": 1389, "mtime": 1752026410415, "results": "337", "hashOfConfig": "201"}, {"size": 11277, "mtime": 1752908709717, "results": "338", "hashOfConfig": "201"}, {"size": 11316, "mtime": 1752998830455, "results": "339", "hashOfConfig": "201"}, {"size": 6736, "mtime": 1753664413321, "results": "340", "hashOfConfig": "201"}, {"size": 1928, "mtime": 1753158560369, "results": "341", "hashOfConfig": "201"}, {"size": 2770, "mtime": 1753664857036, "results": "342", "hashOfConfig": "201"}, {"size": 7620, "mtime": 1753158547289, "results": "343", "hashOfConfig": "201"}, {"size": 3088, "mtime": 1752468935955, "results": "344", "hashOfConfig": "201"}, {"size": 5556, "mtime": 1752969250904, "results": "345", "hashOfConfig": "201"}, {"size": 6661, "mtime": 1752998737150, "results": "346", "hashOfConfig": "201"}, {"size": 166, "mtime": 1752024521758, "results": "347", "hashOfConfig": "201"}, {"size": 435, "mtime": 1753663942644, "results": "348", "hashOfConfig": "201"}, {"size": 1659, "mtime": 1753054451476, "results": "349", "hashOfConfig": "201"}, {"size": 3836, "mtime": 1753160247716, "results": "350", "hashOfConfig": "201"}, {"size": 12521, "mtime": 1753686854521, "results": "351", "hashOfConfig": "201"}, {"size": 1713, "mtime": 1754116688313, "results": "352", "hashOfConfig": "201"}, {"size": 2250, "mtime": 1752908751521, "results": "353", "hashOfConfig": "201"}, {"size": 1498, "mtime": 1752908458736, "results": "354", "hashOfConfig": "201"}, {"size": 3282, "mtime": 1753672276773, "results": "355", "hashOfConfig": "201"}, {"size": 873, "mtime": 1753851542088, "results": "356", "hashOfConfig": "201"}, {"size": 1866, "mtime": 1753851520182, "results": "357", "hashOfConfig": "201"}, {"size": 1397, "mtime": 1753686764431, "results": "358", "hashOfConfig": "201"}, {"size": 4443, "mtime": 1753673483383, "results": "359", "hashOfConfig": "201"}, {"size": 5440, "mtime": 1753851497146, "results": "360", "hashOfConfig": "201"}, {"size": 3641, "mtime": 1753673769790, "results": "361", "hashOfConfig": "201"}, {"size": 234, "mtime": 1753675616016, "results": "362", "hashOfConfig": "201"}, {"size": 744, "mtime": 1753676382520, "results": "363", "hashOfConfig": "201"}, {"size": 3545, "mtime": 1753676234917, "results": "364", "hashOfConfig": "201"}, {"size": 2270, "mtime": 1753922304275, "results": "365", "hashOfConfig": "201"}, {"size": 2859, "mtime": 1753676221357, "results": "366", "hashOfConfig": "201"}, {"size": 3103, "mtime": 1754335877606, "results": "367", "hashOfConfig": "201"}, {"size": 17925, "mtime": 1754335832917, "results": "368", "hashOfConfig": "201"}, {"size": 7426, "mtime": 1753683916153, "results": "369", "hashOfConfig": "201"}, {"size": 2531, "mtime": 1754335792221, "results": "370", "hashOfConfig": "201"}, {"size": 2974, "mtime": 1753676418523, "results": "371", "hashOfConfig": "201"}, {"size": 4802, "mtime": 1754116729378, "results": "372", "hashOfConfig": "201"}, {"size": 889, "mtime": 1753846474177, "results": "373", "hashOfConfig": "201"}, {"size": 915, "mtime": 1753842497889, "results": "374", "hashOfConfig": "201"}, {"size": 5415, "mtime": 1753686818939, "results": "375", "hashOfConfig": "201"}, {"size": 9551, "mtime": 1753760500434, "results": "376", "hashOfConfig": "201"}, {"size": 9160, "mtime": 1753853976551, "results": "377", "hashOfConfig": "201"}, {"size": 1679, "mtime": 1753851587474, "results": "378", "hashOfConfig": "201"}, {"size": 5641, "mtime": 1753857588035, "results": "379", "hashOfConfig": "201"}, {"size": 2941, "mtime": 1753852097456, "results": "380", "hashOfConfig": "201"}, {"size": 394, "mtime": 1753857600394, "results": "381", "hashOfConfig": "201"}, {"size": 2360, "mtime": 1754333369468, "results": "382", "hashOfConfig": "201"}, {"size": 1491, "mtime": 1754335159581, "results": "383", "hashOfConfig": "201"}, {"size": 125, "mtime": 1753854785290, "results": "384", "hashOfConfig": "201"}, {"size": 12188, "mtime": 1754118723720, "results": "385", "hashOfConfig": "201"}, {"size": 20916, "mtime": 1753852016709, "results": "386", "hashOfConfig": "201"}, {"size": 420, "mtime": 1753851797609, "results": "387", "hashOfConfig": "201"}, {"size": 5476, "mtime": 1754336945838, "results": "388", "hashOfConfig": "201"}, {"size": 3691, "mtime": 1754332398090, "results": "389", "hashOfConfig": "201"}, {"size": 2628, "mtime": 1754336280674, "results": "390", "hashOfConfig": "201"}, {"size": 5698, "mtime": 1754336402059, "results": "391", "hashOfConfig": "201"}, {"size": 4126, "mtime": 1754332703906, "results": "392", "hashOfConfig": "201"}, {"size": 4385, "mtime": 1754332463228, "results": "393", "hashOfConfig": "201"}, {"size": 326, "mtime": 1754336821666, "results": "394", "hashOfConfig": "201"}, {"size": 11844, "mtime": 1754334209728, "results": "395", "hashOfConfig": "201"}, {"size": 4757, "mtime": 1754329229301, "results": "396", "hashOfConfig": "201"}, {"size": 2313, "mtime": 1754117329649, "results": "397", "hashOfConfig": "201"}, {"size": 12304, "mtime": 1754335563354, "results": "398", "hashOfConfig": "201"}, {"size": 7351, "mtime": 1754336232644, "results": "399", "hashOfConfig": "201"}, {"filePath": "400", "messages": "401", "suppressedMessages": "402", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "7ir3zp", {"filePath": "403", "messages": "404", "suppressedMessages": "405", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "406", "messages": "407", "suppressedMessages": "408", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "409", "messages": "410", "suppressedMessages": "411", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "412", "messages": "413", "suppressedMessages": "414", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "415", "messages": "416", "suppressedMessages": "417", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "418", "messages": "419", "suppressedMessages": "420", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "421", "messages": "422", "suppressedMessages": "423", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "424", "messages": "425", "suppressedMessages": "426", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "427", "messages": "428", "suppressedMessages": "429", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "430", "messages": "431", "suppressedMessages": "432", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "433", "messages": "434", "suppressedMessages": "435", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "436", "messages": "437", "suppressedMessages": "438", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "439", "messages": "440", "suppressedMessages": "441", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "442", "messages": "443", "suppressedMessages": "444", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "445", "messages": "446", "suppressedMessages": "447", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "448", "messages": "449", "suppressedMessages": "450", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "451", "messages": "452", "suppressedMessages": "453", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "454", "messages": "455", "suppressedMessages": "456", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "457", "messages": "458", "suppressedMessages": "459", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "460", "messages": "461", "suppressedMessages": "462", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "463", "messages": "464", "suppressedMessages": "465", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "466", "messages": "467", "suppressedMessages": "468", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "469", "messages": "470", "suppressedMessages": "471", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "472", "messages": "473", "suppressedMessages": "474", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "475", "messages": "476", "suppressedMessages": "477", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "478", "messages": "479", "suppressedMessages": "480", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "481", "messages": "482", "suppressedMessages": "483", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "484", "messages": "485", "suppressedMessages": "486", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "487", "messages": "488", "suppressedMessages": "489", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "490", "messages": "491", "suppressedMessages": "492", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "493", "messages": "494", "suppressedMessages": "495", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "496", "messages": "497", "suppressedMessages": "498", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "499", "messages": "500", "suppressedMessages": "501", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "502", "messages": "503", "suppressedMessages": "504", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "505", "messages": "506", "suppressedMessages": "507", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "508", "messages": "509", "suppressedMessages": "510", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "511", "messages": "512", "suppressedMessages": "513", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "514", "messages": "515", "suppressedMessages": "516", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "517", "messages": "518", "suppressedMessages": "519", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "520", "messages": "521", "suppressedMessages": "522", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "523", "messages": "524", "suppressedMessages": "525", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "526", "messages": "527", "suppressedMessages": "528", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "529", "messages": "530", "suppressedMessages": "531", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "532", "messages": "533", "suppressedMessages": "534", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "535", "messages": "536", "suppressedMessages": "537", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "538", "messages": "539", "suppressedMessages": "540", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "541", "messages": "542", "suppressedMessages": "543", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "544", "messages": "545", "suppressedMessages": "546", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "547", "messages": "548", "suppressedMessages": "549", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "550", "messages": "551", "suppressedMessages": "552", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "553", "messages": "554", "suppressedMessages": "555", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "556", "messages": "557", "suppressedMessages": "558", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 23, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "559", "messages": "560", "suppressedMessages": "561", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "562", "messages": "563", "suppressedMessages": "564", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "565", "messages": "566", "suppressedMessages": "567", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "568", "messages": "569", "suppressedMessages": "570", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "571", "messages": "572", "suppressedMessages": "573", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "574", "messages": "575", "suppressedMessages": "576", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "577", "messages": "578", "suppressedMessages": "579", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "580", "messages": "581", "suppressedMessages": "582", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "583", "messages": "584", "suppressedMessages": "585", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "586", "messages": "587", "suppressedMessages": "588", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "589", "messages": "590", "suppressedMessages": "591", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "592", "messages": "593", "suppressedMessages": "594", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "595", "messages": "596", "suppressedMessages": "597", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "598", "messages": "599", "suppressedMessages": "600", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "601", "messages": "602", "suppressedMessages": "603", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "604", "messages": "605", "suppressedMessages": "606", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "607", "messages": "608", "suppressedMessages": "609", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "610", "messages": "611", "suppressedMessages": "612", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 21, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "613", "messages": "614", "suppressedMessages": "615", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "616", "messages": "617", "suppressedMessages": "618", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "619", "messages": "620", "suppressedMessages": "621", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "622", "messages": "623", "suppressedMessages": "624", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "625", "messages": "626", "suppressedMessages": "627", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "628", "messages": "629", "suppressedMessages": "630", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "631", "messages": "632", "suppressedMessages": "633", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "634", "messages": "635", "suppressedMessages": "636", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "637", "messages": "638", "suppressedMessages": "639", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "640", "messages": "641", "suppressedMessages": "642", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "643", "messages": "644", "suppressedMessages": "645", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "646", "messages": "647", "suppressedMessages": "648", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "649", "messages": "650", "suppressedMessages": "651", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "652", "messages": "653", "suppressedMessages": "654", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "655", "messages": "656", "suppressedMessages": "657", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "658", "messages": "659", "suppressedMessages": "660", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "661", "messages": "662", "suppressedMessages": "663", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "664", "messages": "665", "suppressedMessages": "666", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "667", "messages": "668", "suppressedMessages": "669", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "670", "messages": "671", "suppressedMessages": "672", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "673", "messages": "674", "suppressedMessages": "675", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "676", "messages": "677", "suppressedMessages": "678", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "679", "messages": "680", "suppressedMessages": "681", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "682", "messages": "683", "suppressedMessages": "684", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "685", "messages": "686", "suppressedMessages": "687", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "688", "messages": "689", "suppressedMessages": "690", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "691", "messages": "692", "suppressedMessages": "693", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "694", "messages": "695", "suppressedMessages": "696", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "697", "messages": "698", "suppressedMessages": "699", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "700", "messages": "701", "suppressedMessages": "702", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "703", "messages": "704", "suppressedMessages": "705", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "706", "messages": "707", "suppressedMessages": "708", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "709", "messages": "710", "suppressedMessages": "711", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "712", "messages": "713", "suppressedMessages": "714", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "715", "messages": "716", "suppressedMessages": "717", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "718", "messages": "719", "suppressedMessages": "720", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "721", "messages": "722", "suppressedMessages": "723", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "724", "messages": "725", "suppressedMessages": "726", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "727", "messages": "728", "suppressedMessages": "729", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "730", "messages": "731", "suppressedMessages": "732", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "733", "messages": "734", "suppressedMessages": "735", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "736", "messages": "737", "suppressedMessages": "738", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "739", "messages": "740", "suppressedMessages": "741", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "742", "messages": "743", "suppressedMessages": "744", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "745", "messages": "746", "suppressedMessages": "747", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "748", "messages": "749", "suppressedMessages": "750", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "751", "messages": "752", "suppressedMessages": "753", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "754", "messages": "755", "suppressedMessages": "756", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "757", "messages": "758", "suppressedMessages": "759", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "760", "messages": "761", "suppressedMessages": "762", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "763", "messages": "764", "suppressedMessages": "765", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "766", "messages": "767", "suppressedMessages": "768", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "769", "messages": "770", "suppressedMessages": "771", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "772", "messages": "773", "suppressedMessages": "774", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "775", "messages": "776", "suppressedMessages": "777", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "778", "messages": "779", "suppressedMessages": "780", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "781", "messages": "782", "suppressedMessages": "783", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "784", "messages": "785", "suppressedMessages": "786", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "787", "messages": "788", "suppressedMessages": "789", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "790", "messages": "791", "suppressedMessages": "792", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "793", "messages": "794", "suppressedMessages": "795", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 17, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "796", "messages": "797", "suppressedMessages": "798", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "799", "messages": "800", "suppressedMessages": "801", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "802", "messages": "803", "suppressedMessages": "804", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "805", "messages": "806", "suppressedMessages": "807", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "808", "messages": "809", "suppressedMessages": "810", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "811", "messages": "812", "suppressedMessages": "813", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "814", "messages": "815", "suppressedMessages": "816", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "817", "messages": "818", "suppressedMessages": "819", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "820", "messages": "821", "suppressedMessages": "822", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "823", "messages": "824", "suppressedMessages": "825", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "826", "messages": "827", "suppressedMessages": "828", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "829", "messages": "830", "suppressedMessages": "831", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "832", "messages": "833", "suppressedMessages": "834", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "835", "messages": "836", "suppressedMessages": "837", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "838", "messages": "839", "suppressedMessages": "840", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "841", "messages": "842", "suppressedMessages": "843", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "844", "messages": "845", "suppressedMessages": "846", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "847", "messages": "848", "suppressedMessages": "849", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "850", "messages": "851", "suppressedMessages": "852", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "853", "messages": "854", "suppressedMessages": "855", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "856", "messages": "857", "suppressedMessages": "858", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "859", "messages": "860", "suppressedMessages": "861", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "862", "messages": "863", "suppressedMessages": "864", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "865", "messages": "866", "suppressedMessages": "867", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "868", "messages": "869", "suppressedMessages": "870", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "871", "messages": "872", "suppressedMessages": "873", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "874", "messages": "875", "suppressedMessages": "876", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "877", "messages": "878", "suppressedMessages": "879", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "880", "messages": "881", "suppressedMessages": "882", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "883", "messages": "884", "suppressedMessages": "885", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "886", "messages": "887", "suppressedMessages": "888", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "889", "messages": "890", "suppressedMessages": "891", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "892", "messages": "893", "suppressedMessages": "894", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "895", "messages": "896", "suppressedMessages": "897", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "898", "messages": "899", "suppressedMessages": "900", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "901", "messages": "902", "suppressedMessages": "903", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "904", "messages": "905", "suppressedMessages": "906", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "907", "messages": "908", "suppressedMessages": "909", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "910", "messages": "911", "suppressedMessages": "912", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "913", "messages": "914", "suppressedMessages": "915", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "916", "messages": "917", "suppressedMessages": "918", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "919", "messages": "920", "suppressedMessages": "921", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "922", "messages": "923", "suppressedMessages": "924", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "925", "messages": "926", "suppressedMessages": "927", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "928", "messages": "929", "suppressedMessages": "930", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "931", "messages": "932", "suppressedMessages": "933", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "934", "messages": "935", "suppressedMessages": "936", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "937", "messages": "938", "suppressedMessages": "939", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "940", "messages": "941", "suppressedMessages": "942", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "943", "messages": "944", "suppressedMessages": "945", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "946", "messages": "947", "suppressedMessages": "948", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "949", "messages": "950", "suppressedMessages": "951", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "952", "messages": "953", "suppressedMessages": "954", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 15, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "955", "messages": "956", "suppressedMessages": "957", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "958", "messages": "959", "suppressedMessages": "960", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "961", "messages": "962", "suppressedMessages": "963", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "964", "messages": "965", "suppressedMessages": "966", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "967", "messages": "968", "suppressedMessages": "969", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "970", "messages": "971", "suppressedMessages": "972", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "973", "messages": "974", "suppressedMessages": "975", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "976", "messages": "977", "suppressedMessages": "978", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "979", "messages": "980", "suppressedMessages": "981", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "982", "messages": "983", "suppressedMessages": "984", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "985", "messages": "986", "suppressedMessages": "987", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "988", "messages": "989", "suppressedMessages": "990", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "991", "messages": "992", "suppressedMessages": "993", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "994", "messages": "995", "suppressedMessages": "996", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "/Users/<USER>/Data/new era/siift-next/src/app/admin/activity/page.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/app/admin/agent/calls/page.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/app/admin/agent/token-trends/page.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/app/admin/agent/usage-stats/page.tsx", ["997", "998", "999", "1000", "1001", "1002"], [], "/Users/<USER>/Data/new era/siift-next/src/app/admin/analytics/activity-metrics/page.tsx", ["1003", "1004"], [], "/Users/<USER>/Data/new era/siift-next/src/app/admin/analytics/activity-trends/page.tsx", ["1005", "1006", "1007", "1008", "1009", "1010", "1011"], [], "/Users/<USER>/Data/new era/siift-next/src/app/admin/analytics/feedbacks/page.tsx", ["1012", "1013", "1014", "1015", "1016", "1017", "1018", "1019", "1020"], [], "/Users/<USER>/Data/new era/siift-next/src/app/admin/analytics/summary/page.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/app/admin/analytics/users/page.tsx", ["1021", "1022", "1023", "1024", "1025", "1026", "1027"], [], "/Users/<USER>/Data/new era/siift-next/src/app/admin/api-test/page.tsx", ["1028", "1029", "1030", "1031", "1032", "1033", "1034", "1035", "1036"], [], "/Users/<USER>/Data/new era/siift-next/src/app/admin/health/page.tsx", ["1037", "1038", "1039", "1040"], [], "/Users/<USER>/Data/new era/siift-next/src/app/admin/notifications/page.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/app/admin/page.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/app/admin/profile/page.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/app/admin/projects/all/page.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/app/admin/projects/create/page.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/app/admin/projects/page.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/app/admin/recent/page.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/app/admin/settings/page.tsx", ["1041"], [], "/Users/<USER>/Data/new era/siift-next/src/app/admin/settings-tab/page.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/app/admin/system/health/page.tsx", ["1042", "1043", "1044", "1045", "1046"], [], "/Users/<USER>/Data/new era/siift-next/src/app/admin/trends/page.tsx", ["1047", "1048", "1049"], [], "/Users/<USER>/Data/new era/siift-next/src/app/admin/users/page.tsx", ["1050", "1051"], [], "/Users/<USER>/Data/new era/siift-next/src/app/api/auth/me/route.ts", [], [], "/Users/<USER>/Data/new era/siift-next/src/app/api/auth/signin/route.ts", ["1052"], [], "/Users/<USER>/Data/new era/siift-next/src/app/api/health/route.ts", [], [], "/Users/<USER>/Data/new era/siift-next/src/app/api/projects/[id]/route.ts", [], [], "/Users/<USER>/Data/new era/siift-next/src/app/api/projects/route.ts", ["1053"], [], "/Users/<USER>/Data/new era/siift-next/src/app/api/webhooks/clerk/route.ts", ["1054", "1055"], [], "/Users/<USER>/Data/new era/siift-next/src/app/auth/forgot-password/page.tsx", ["1056"], [], "/Users/<USER>/Data/new era/siift-next/src/app/auth/layout.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/app/auth/login/page.tsx", ["1057", "1058", "1059", "1060"], [], "/Users/<USER>/Data/new era/siift-next/src/app/auth/login/sso-callback/page.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/app/auth/register/page.tsx", ["1061", "1062", "1063", "1064"], [], "/Users/<USER>/Data/new era/siift-next/src/app/auth/register/sso-callback/page.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/app/auth/reset-password/page.tsx", ["1065", "1066"], [], "/Users/<USER>/Data/new era/siift-next/src/app/auth/verify-email/page.tsx", ["1067", "1068"], [], "/Users/<USER>/Data/new era/siift-next/src/app/layout.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/app/page.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/app/profile/page.tsx", ["1069", "1070", "1071", "1072", "1073"], [], "/Users/<USER>/Data/new era/siift-next/src/app/projects/[id]/edit/page.tsx", ["1074", "1075", "1076", "1077"], [], "/Users/<USER>/Data/new era/siift-next/src/app/projects/[id]/page.tsx", ["1078", "1079"], [], "/Users/<USER>/Data/new era/siift-next/src/app/projects/create/page.tsx", ["1080"], [], "/Users/<USER>/Data/new era/siift-next/src/app/projects/new/page.tsx", ["1081"], [], "/Users/<USER>/Data/new era/siift-next/src/app/projects/page.tsx", ["1082", "1083"], [], "/Users/<USER>/Data/new era/siift-next/src/app/settings/page.tsx", ["1084", "1085", "1086", "1087", "1088"], [], "/Users/<USER>/Data/new era/siift-next/src/app/sso-callback/page.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/app/user-dashboard/page.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/admin-sidebar.tsx", ["1089", "1090", "1091"], [], "/Users/<USER>/Data/new era/siift-next/src/components/admin-tabbed-content.tsx", ["1092", "1093", "1094", "1095", "1096", "1097"], [], "/Users/<USER>/Data/new era/siift-next/src/components/auth/admin-route.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/auth/auth-card.tsx", ["1098"], [], "/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGrid.tsx", ["1099", "1100", "1101", "1102", "1103", "1104", "1105", "1106", "1107", "1108", "1109", "1110", "1111", "1112", "1113", "1114", "1115", "1116", "1117", "1118", "1119", "1120", "1121"], [], "/Users/<USER>/Data/new era/siift-next/src/components/dashboard/DashboardHeader.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/dashboard/dashboard-hero-section.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/dashboard/dashboard-projects-section.tsx", ["1122", "1123", "1124"], [], "/Users/<USER>/Data/new era/siift-next/src/components/dashboard/dashboard-stats-cards.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/landing/feature-grid.tsx", ["1125"], [], "/Users/<USER>/Data/new era/siift-next/src/components/landing/hero-section.tsx", ["1126"], [], "/Users/<USER>/Data/new era/siift-next/src/components/landing/landing-page.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/layout/admin-layout.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/layout/dashboard-layout.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/layout/footer.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/layout/header.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/layout/main-layout.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/nav-main.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/nav-user.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/navigation/main-nav.tsx", ["1127"], [], "/Users/<USER>/Data/new era/siift-next/src/components/navigation/mobile-nav.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/navigation/user-menu.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/project-chat-sidebar.tsx", ["1128", "1129", "1130", "1131", "1132", "1133", "1134", "1135", "1136", "1137", "1138", "1139", "1140", "1141", "1142", "1143", "1144", "1145", "1146", "1147", "1148"], [], "/Users/<USER>/Data/new era/siift-next/src/components/project-creation/ProjectCreationAnimation.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/project-sidebar.tsx", ["1149"], [], "/Users/<USER>/Data/new era/siift-next/src/components/providers/ClerkSessionProvider.tsx", ["1150", "1151", "1152", "1153", "1154"], [], "/Users/<USER>/Data/new era/siift-next/src/components/providers/QueryProvider.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/providers/SessionProvider.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/team-switcher.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/theme-provider.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/theme-toggle.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/ui/app-loading.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/ui/avatar.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/ui/background-beams.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/ui/badge.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/ui/breadcrumb.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/ui/button.tsx", ["1155"], [], "/Users/<USER>/Data/new era/siift-next/src/components/ui/card.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/ui/chat-bubble.tsx", ["1156", "1157"], [], "/Users/<USER>/Data/new era/siift-next/src/components/ui/chat-message-list.tsx", ["1158", "1159"], [], "/Users/<USER>/Data/new era/siift-next/src/components/ui/checkbox.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/ui/collapsible.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/ui/dropdown-menu.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/ui/form-input.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/ui/form.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/ui/glow.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/ui/icons.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/ui/input.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/ui/label.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/ui/logo.tsx", ["1160"], [], "/Users/<USER>/Data/new era/siift-next/src/components/ui/message-loading.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/ui/mockup.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/ui/navigation-menu.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/ui/progress.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/ui/scroll-area.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/ui/select.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/ui/separator.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/ui/sheet.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/ui/sidebar-button.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/ui/sidebar.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/ui/skeleton.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/ui/sonner.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/ui/switch.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/ui/tabs.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/ui/testimonials-columns-1.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/ui/testimonials.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/ui/textarea.tsx", ["1161"], [], "/Users/<USER>/Data/new era/siift-next/src/components/ui/tooltip.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/ui/waitlist-section.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/user-tabbed-content.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/contexts/auth-context.tsx", ["1162", "1163", "1164"], [], "/Users/<USER>/Data/new era/siift-next/src/contexts/background-context.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/hooks/mutations/useUserMutations.ts", ["1165", "1166", "1167", "1168", "1169", "1170", "1171"], [], "/Users/<USER>/Data/new era/siift-next/src/hooks/queries/useUser.ts", ["1172"], [], "/Users/<USER>/Data/new era/siift-next/src/hooks/use-auto-scroll.ts", [], [], "/Users/<USER>/Data/new era/siift-next/src/hooks/use-mobile.ts", [], [], "/Users/<USER>/Data/new era/siift-next/src/hooks/useAuth.ts", [], [], "/Users/<USER>/Data/new era/siift-next/src/hooks/useClerkAuth.ts", [], [], "/Users/<USER>/Data/new era/siift-next/src/hooks/useResizable.ts", [], [], "/Users/<USER>/Data/new era/siift-next/src/hooks/useToast.ts", ["1173"], [], "/Users/<USER>/Data/new era/siift-next/src/hooks/useUserSync.ts", ["1174", "1175", "1176"], [], "/Users/<USER>/Data/new era/siift-next/src/lib/admin-api.ts", ["1177", "1178", "1179", "1180"], [], "/Users/<USER>/Data/new era/siift-next/src/lib/api.ts", ["1181", "1182"], [], "/Users/<USER>/Data/new era/siift-next/src/lib/apiClient.ts", ["1183", "1184", "1185", "1186", "1187", "1188", "1189", "1190", "1191", "1192", "1193", "1194", "1195", "1196", "1197", "1198", "1199"], [], "/Users/<USER>/Data/new era/siift-next/src/lib/businessSectionsData.ts", ["1200"], [], "/Users/<USER>/Data/new era/siift-next/src/lib/clerk-api.ts", ["1201", "1202", "1203"], [], "/Users/<USER>/Data/new era/siift-next/src/lib/constants.ts", [], [], "/Users/<USER>/Data/new era/siift-next/src/lib/fonts.ts", [], [], "/Users/<USER>/Data/new era/siift-next/src/lib/jwt.ts", [], [], "/Users/<USER>/Data/new era/siift-next/src/lib/mock-auth-api.ts", ["1204", "1205", "1206"], [], "/Users/<USER>/Data/new era/siift-next/src/lib/mock-email-service.ts", ["1207"], [], "/Users/<USER>/Data/new era/siift-next/src/lib/mockEventStream.ts", ["1208", "1209", "1210"], [], "/Users/<USER>/Data/new era/siift-next/src/lib/projectCreationConfig.ts", [], [], "/Users/<USER>/Data/new era/siift-next/src/lib/queryClient.ts", ["1211", "1212", "1213", "1214"], [], "/Users/<USER>/Data/new era/siift-next/src/lib/realEventStream.ts", [], [], "/Users/<USER>/Data/new era/siift-next/src/lib/session.ts", [], [], "/Users/<USER>/Data/new era/siift-next/src/lib/tokenStorage.ts", ["1215", "1216"], [], "/Users/<USER>/Data/new era/siift-next/src/lib/types.ts", ["1217", "1218"], [], "/Users/<USER>/Data/new era/siift-next/src/lib/utils.ts", [], [], "/Users/<USER>/Data/new era/siift-next/src/middleware.ts", [], [], "/Users/<USER>/Data/new era/siift-next/src/stores/businessSectionStore.ts", ["1219", "1220", "1221"], [], "/Users/<USER>/Data/new era/siift-next/src/stores/projectCreationStore.ts", ["1222"], [], "/Users/<USER>/Data/new era/siift-next/src/stores/sessionStore.ts", ["1223", "1224"], [], "/Users/<USER>/Data/new era/siift-next/src/types/BusinessSection.types.ts", [], [], "/Users/<USER>/Data/new era/siift-next/src/types/Session.types.ts", [], [], "/Users/<USER>/Data/new era/siift-next/src/types/email.types.ts", ["1225"], [], "/Users/<USER>/Data/new era/siift-next/src/hooks/useClerkApi.ts", ["1226", "1227", "1228"], [], "/Users/<USER>/Data/new era/siift-next/src/app/robots.ts", [], [], "/Users/<USER>/Data/new era/siift-next/src/app/sitemap.ts", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/analytics/PostHogProvider.tsx", ["1229"], [], "/Users/<USER>/Data/new era/siift-next/src/components/seo/SEOHead.tsx", ["1230"], [], "/Users/<USER>/Data/new era/siift-next/src/components/seo/StructuredData.tsx", ["1231", "1232"], [], "/Users/<USER>/Data/new era/siift-next/src/hooks/useAnalytics.ts", ["1233", "1234", "1235", "1236", "1237"], [], "/Users/<USER>/Data/new era/siift-next/src/app/admin/layout.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/app/debug/tokens/page.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/app/error.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/app/loading.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/app/not-found.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/EditableCell.tsx", ["1238"], [], "/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx", ["1239", "1240", "1241", "1242", "1243", "1244", "1245"], [], "/Users/<USER>/Data/new era/siift-next/src/components/debug/ClerkTokenDebug.tsx", ["1246", "1247"], [], "/Users/<USER>/Data/new era/siift-next/src/components/ui/table.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/hooks/useClerkToken.ts", ["1248"], [], "/Users/<USER>/Data/new era/siift-next/src/stores/businessItemStore.ts", ["1249", "1250"], [], "/Users/<USER>/Data/new era/siift-next/src/components/ui/resize-handle.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/app/debug/page.tsx", ["1251", "1252", "1253", "1254", "1255", "1256", "1257", "1258"], [], "/Users/<USER>/Data/new era/siift-next/src/components/debug/PostHogDebug.tsx", ["1259", "1260", "1261", "1262", "1263"], [], "/Users/<USER>/Data/new era/siift-next/src/components/debug/ClerkTokenExample.tsx", ["1264", "1265", "1266", "1267", "1268", "1269"], [], "/Users/<USER>/Data/new era/siift-next/src/app/about/page.tsx", ["1270", "1271", "1272", "1273"], [], "/Users/<USER>/Data/new era/siift-next/src/app/blog/[slug]/not-found.tsx", ["1274", "1275"], [], "/Users/<USER>/Data/new era/siift-next/src/app/blog/[slug]/page.tsx", ["1276"], [], "/Users/<USER>/Data/new era/siift-next/src/app/blog/page.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/app/test-ai-input/page.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/blog/blog-card.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/blog/blog-section.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/ui/ai-prompt-demo.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/ui/animated-ai-input.tsx", ["1277", "1278", "1279", "1280", "1281", "1282", "1283", "1284", "1285", "1286", "1287", "1288", "1289", "1290", "1291"], [], "/Users/<USER>/Data/new era/siift-next/src/data/blog-posts.ts", [], [], "/Users/<USER>/Data/new era/siift-next/src/types/blog.ts", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/project/ContentSections.tsx", ["1292", "1293"], [], "/Users/<USER>/Data/new era/siift-next/src/components/project/PrioritiesDropdown.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/project/ProgressBar.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx", ["1294"], [], "/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectHeader.tsx", ["1295", "1296", "1297"], [], "/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectMainContent.tsx", ["1298", "1299", "1300", "1301", "1302", "1303"], [], "/Users/<USER>/Data/new era/siift-next/src/components/project/index.ts", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/shared/ProjectInputSection.tsx", ["1304"], [], "/Users/<USER>/Data/new era/siift-next/src/components/ui/dotted-background.tsx", ["1305"], [], "/Users/<USER>/Data/new era/siift-next/src/components/ui/popover.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/lib/design-tokens.ts", [], [], "/Users/<USER>/Data/new era/siift-next/src/mockdata/businessItemQuestions.ts", ["1306", "1307", "1308"], [], {"ruleId": "1309", "severity": 1, "message": "1310", "line": 35, "column": 36, "nodeType": "1311", "messageId": "1312", "endLine": 35, "endColumn": 39, "suggestions": "1313"}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 58, "column": 19, "nodeType": "1311", "messageId": "1312", "endLine": 58, "endColumn": 22, "suggestions": "1314"}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 267, "column": 46, "nodeType": "1311", "messageId": "1312", "endLine": 267, "endColumn": 49, "suggestions": "1315"}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 293, "column": 42, "nodeType": "1311", "messageId": "1312", "endLine": 293, "endColumn": 45, "suggestions": "1316"}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 310, "column": 46, "nodeType": "1311", "messageId": "1312", "endLine": 310, "endColumn": 49, "suggestions": "1317"}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 335, "column": 44, "nodeType": "1311", "messageId": "1312", "endLine": 335, "endColumn": 47, "suggestions": "1318"}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 24, "column": 36, "nodeType": "1311", "messageId": "1312", "endLine": 24, "endColumn": 39, "suggestions": "1319"}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 47, "column": 19, "nodeType": "1311", "messageId": "1312", "endLine": 47, "endColumn": 22, "suggestions": "1320"}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 34, "column": 36, "nodeType": "1311", "messageId": "1312", "endLine": 34, "endColumn": 39, "suggestions": "1321"}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 57, "column": 19, "nodeType": "1311", "messageId": "1312", "endLine": 57, "endColumn": 22, "suggestions": "1322"}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 116, "column": 28, "nodeType": "1311", "messageId": "1312", "endLine": 116, "endColumn": 31, "suggestions": "1323"}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 207, "column": 44, "nodeType": "1311", "messageId": "1312", "endLine": 207, "endColumn": 47, "suggestions": "1324"}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 246, "column": 46, "nodeType": "1311", "messageId": "1312", "endLine": 246, "endColumn": 49, "suggestions": "1325"}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 270, "column": 46, "nodeType": "1311", "messageId": "1312", "endLine": 270, "endColumn": 49, "suggestions": "1326"}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 309, "column": 37, "nodeType": "1311", "messageId": "1312", "endLine": 309, "endColumn": 40, "suggestions": "1327"}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 39, "column": 36, "nodeType": "1311", "messageId": "1312", "endLine": 39, "endColumn": 39, "suggestions": "1328"}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 70, "column": 27, "nodeType": "1311", "messageId": "1312", "endLine": 70, "endColumn": 30, "suggestions": "1329"}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 90, "column": 19, "nodeType": "1311", "messageId": "1312", "endLine": 90, "endColumn": 22, "suggestions": "1330"}, {"ruleId": "1331", "severity": 1, "message": "1332", "line": 99, "column": 6, "nodeType": "1333", "endLine": 99, "endColumn": 15, "suggestions": "1334"}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 112, "column": 24, "nodeType": "1311", "messageId": "1312", "endLine": 112, "endColumn": 27, "suggestions": "1335"}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 199, "column": 50, "nodeType": "1311", "messageId": "1312", "endLine": 199, "endColumn": 53, "suggestions": "1336"}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 215, "column": 55, "nodeType": "1311", "messageId": "1312", "endLine": 215, "endColumn": 58, "suggestions": "1337"}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 234, "column": 48, "nodeType": "1311", "messageId": "1312", "endLine": 234, "endColumn": 51, "suggestions": "1338"}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 414, "column": 52, "nodeType": "1311", "messageId": "1312", "endLine": 414, "endColumn": 55, "suggestions": "1339"}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 32, "column": 36, "nodeType": "1311", "messageId": "1312", "endLine": 32, "endColumn": 39, "suggestions": "1340"}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 59, "column": 27, "nodeType": "1311", "messageId": "1312", "endLine": 59, "endColumn": 30, "suggestions": "1341"}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 78, "column": 19, "nodeType": "1311", "messageId": "1312", "endLine": 78, "endColumn": 22, "suggestions": "1342"}, {"ruleId": "1331", "severity": 1, "message": "1332", "line": 87, "column": 6, "nodeType": "1333", "endLine": 87, "endColumn": 15, "suggestions": "1343"}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 247, "column": 42, "nodeType": "1311", "messageId": "1312", "endLine": 247, "endColumn": 45, "suggestions": "1344"}, {"ruleId": "1345", "severity": 1, "message": "1346", "line": 299, "column": 33, "nodeType": "1347", "messageId": "1348", "suggestions": "1349"}, {"ruleId": "1345", "severity": 1, "message": "1346", "line": 299, "column": 56, "nodeType": "1347", "messageId": "1348", "suggestions": "1350"}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 45, "column": 10, "nodeType": "1311", "messageId": "1312", "endLine": 45, "endColumn": 13, "suggestions": "1351"}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 179, "column": 23, "nodeType": "1311", "messageId": "1312", "endLine": 179, "endColumn": 26, "suggestions": "1352"}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 266, "column": 41, "nodeType": "1311", "messageId": "1312", "endLine": 266, "endColumn": 44, "suggestions": "1353"}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 317, "column": 38, "nodeType": "1311", "messageId": "1312", "endLine": 317, "endColumn": 41, "suggestions": "1354"}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 323, "column": 45, "nodeType": "1311", "messageId": "1312", "endLine": 323, "endColumn": 48, "suggestions": "1355"}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 365, "column": 40, "nodeType": "1311", "messageId": "1312", "endLine": 365, "endColumn": 43, "suggestions": "1356"}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 424, "column": 40, "nodeType": "1311", "messageId": "1312", "endLine": 424, "endColumn": 43, "suggestions": "1357"}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 495, "column": 35, "nodeType": "1311", "messageId": "1312", "endLine": 495, "endColumn": 38, "suggestions": "1358"}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 500, "column": 43, "nodeType": "1311", "messageId": "1312", "endLine": 500, "endColumn": 46, "suggestions": "1359"}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 61, "column": 28, "nodeType": "1311", "messageId": "1312", "endLine": 61, "endColumn": 31, "suggestions": "1360"}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 71, "column": 19, "nodeType": "1311", "messageId": "1312", "endLine": 71, "endColumn": 22, "suggestions": "1361"}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 140, "column": 19, "nodeType": "1311", "messageId": "1312", "endLine": 140, "endColumn": 22, "suggestions": "1362"}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 154, "column": 19, "nodeType": "1311", "messageId": "1312", "endLine": 154, "endColumn": 22, "suggestions": "1363"}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 58, "column": 50, "nodeType": "1311", "messageId": "1312", "endLine": 58, "endColumn": 53, "suggestions": "1364"}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 26, "column": 36, "nodeType": "1311", "messageId": "1312", "endLine": 26, "endColumn": 39, "suggestions": "1365"}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 49, "column": 19, "nodeType": "1311", "messageId": "1312", "endLine": 49, "endColumn": 22, "suggestions": "1366"}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 107, "column": 61, "nodeType": "1311", "messageId": "1312", "endLine": 107, "endColumn": 64, "suggestions": "1367"}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 112, "column": 63, "nodeType": "1311", "messageId": "1312", "endLine": 112, "endColumn": 66, "suggestions": "1368"}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 317, "column": 104, "nodeType": "1311", "messageId": "1312", "endLine": 317, "endColumn": 107, "suggestions": "1369"}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 142, "column": 21, "nodeType": "1311", "messageId": "1312", "endLine": 142, "endColumn": 24, "suggestions": "1370"}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 199, "column": 36, "nodeType": "1311", "messageId": "1312", "endLine": 199, "endColumn": 39, "suggestions": "1371"}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 351, "column": 42, "nodeType": "1311", "messageId": "1312", "endLine": 351, "endColumn": 45, "suggestions": "1372"}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 74, "column": 30, "nodeType": "1311", "messageId": "1312", "endLine": 74, "endColumn": 33, "suggestions": "1373"}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 84, "column": 21, "nodeType": "1311", "messageId": "1312", "endLine": 84, "endColumn": 24, "suggestions": "1374"}, {"ruleId": "1375", "severity": 1, "message": "1376", "line": 59, "column": 23, "nodeType": null, "messageId": "1377", "endLine": 59, "endColumn": 24}, {"ruleId": "1375", "severity": 1, "message": "1378", "line": 52, "column": 27, "nodeType": null, "messageId": "1377", "endLine": 52, "endColumn": 34}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 32, "column": 12, "nodeType": "1311", "messageId": "1312", "endLine": 32, "endColumn": 15, "suggestions": "1379"}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 40, "column": 11, "nodeType": "1311", "messageId": "1312", "endLine": 40, "endColumn": 14, "suggestions": "1380"}, {"ruleId": "1375", "severity": 1, "message": "1381", "line": 36, "column": 5, "nodeType": null, "messageId": "1377", "endLine": 36, "endColumn": 14}, {"ruleId": "1375", "severity": 1, "message": "1382", "line": 8, "column": 10, "nodeType": null, "messageId": "1377", "endLine": 8, "endColumn": 19}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 75, "column": 19, "nodeType": "1311", "messageId": "1312", "endLine": 75, "endColumn": 22, "suggestions": "1383"}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 123, "column": 19, "nodeType": "1311", "messageId": "1312", "endLine": 123, "endColumn": 22, "suggestions": "1384"}, {"ruleId": "1345", "severity": 1, "message": "1385", "line": 159, "column": 16, "nodeType": "1347", "messageId": "1348", "suggestions": "1386"}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 74, "column": 29, "nodeType": "1311", "messageId": "1312", "endLine": 74, "endColumn": 32, "suggestions": "1387"}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 91, "column": 29, "nodeType": "1311", "messageId": "1312", "endLine": 91, "endColumn": 32, "suggestions": "1388"}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 124, "column": 19, "nodeType": "1311", "messageId": "1312", "endLine": 124, "endColumn": 22, "suggestions": "1389"}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 191, "column": 19, "nodeType": "1311", "messageId": "1312", "endLine": 191, "endColumn": 22, "suggestions": "1390"}, {"ruleId": "1375", "severity": 1, "message": "1391", "line": 44, "column": 19, "nodeType": null, "messageId": "1377", "endLine": 44, "endColumn": 29}, {"ruleId": "1345", "severity": 1, "message": "1385", "line": 174, "column": 17, "nodeType": "1347", "messageId": "1348", "suggestions": "1392"}, {"ruleId": "1375", "severity": 1, "message": "1393", "line": 50, "column": 5, "nodeType": null, "messageId": "1377", "endLine": 50, "endColumn": 26}, {"ruleId": "1345", "severity": 1, "message": "1385", "line": 175, "column": 17, "nodeType": "1347", "messageId": "1348", "suggestions": "1394"}, {"ruleId": "1375", "severity": 1, "message": "1395", "line": 50, "column": 17, "nodeType": null, "messageId": "1377", "endLine": 50, "endColumn": 28}, {"ruleId": "1375", "severity": 1, "message": "1396", "line": 50, "column": 41, "nodeType": null, "messageId": "1377", "endLine": 50, "endColumn": 54}, {"ruleId": "1375", "severity": 1, "message": "1397", "line": 70, "column": 5, "nodeType": null, "messageId": "1377", "endLine": 70, "endColumn": 13}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 111, "column": 21, "nodeType": "1311", "messageId": "1312", "endLine": 111, "endColumn": 24, "suggestions": "1398"}, {"ruleId": "1375", "severity": 1, "message": "1399", "line": 138, "column": 9, "nodeType": null, "messageId": "1377", "endLine": 138, "endColumn": 17}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 98, "column": 21, "nodeType": "1311", "messageId": "1312", "endLine": 98, "endColumn": 24, "suggestions": "1400"}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 117, "column": 19, "nodeType": "1311", "messageId": "1312", "endLine": 117, "endColumn": 22, "suggestions": "1401"}, {"ruleId": "1345", "severity": 1, "message": "1385", "line": 162, "column": 22, "nodeType": "1347", "messageId": "1348", "suggestions": "1402"}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 243, "column": 73, "nodeType": "1311", "messageId": "1312", "endLine": 243, "endColumn": 76, "suggestions": "1403"}, {"ruleId": "1331", "severity": 1, "message": "1404", "line": 85, "column": 6, "nodeType": "1333", "endLine": 85, "endColumn": 41, "suggestions": "1405"}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 88, "column": 42, "nodeType": "1311", "messageId": "1312", "endLine": 88, "endColumn": 45, "suggestions": "1406"}, {"ruleId": "1375", "severity": 1, "message": "1407", "line": 10, "column": 24, "nodeType": null, "messageId": "1377", "endLine": 10, "endColumn": 29}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 52, "column": 19, "nodeType": "1311", "messageId": "1312", "endLine": 52, "endColumn": 22, "suggestions": "1408"}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 50, "column": 21, "nodeType": "1311", "messageId": "1312", "endLine": 50, "endColumn": 24, "suggestions": "1409"}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 88, "column": 19, "nodeType": "1311", "messageId": "1312", "endLine": 88, "endColumn": 22, "suggestions": "1410"}, {"ruleId": "1375", "severity": 1, "message": "1411", "line": 64, "column": 12, "nodeType": null, "messageId": "1377", "endLine": 64, "endColumn": 17}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 123, "column": 21, "nodeType": "1311", "messageId": "1312", "endLine": 123, "endColumn": 24, "suggestions": "1412"}, {"ruleId": "1375", "severity": 1, "message": "1413", "line": 133, "column": 35, "nodeType": null, "messageId": "1377", "endLine": 133, "endColumn": 40}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 143, "column": 21, "nodeType": "1311", "messageId": "1312", "endLine": 143, "endColumn": 24, "suggestions": "1414"}, {"ruleId": "1345", "severity": 1, "message": "1385", "line": 234, "column": 51, "nodeType": "1347", "messageId": "1348", "suggestions": "1415"}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 37, "column": 32, "nodeType": "1311", "messageId": "1312", "endLine": 37, "endColumn": 35, "suggestions": "1416"}, {"ruleId": "1375", "severity": 1, "message": "1417", "line": 256, "column": 17, "nodeType": null, "messageId": "1377", "endLine": 256, "endColumn": 23}, {"ruleId": "1375", "severity": 1, "message": "1418", "line": 257, "column": 9, "nodeType": null, "messageId": "1377", "endLine": 257, "endColumn": 15}, {"ruleId": "1375", "severity": 1, "message": "1419", "line": 4, "column": 10, "nodeType": null, "messageId": "1377", "endLine": 4, "endColumn": 14}, {"ruleId": "1375", "severity": 1, "message": "1420", "line": 4, "column": 16, "nodeType": null, "messageId": "1377", "endLine": 4, "endColumn": 27}, {"ruleId": "1375", "severity": 1, "message": "1421", "line": 4, "column": 29, "nodeType": null, "messageId": "1377", "endLine": 4, "endColumn": 37}, {"ruleId": "1375", "severity": 1, "message": "1422", "line": 4, "column": 39, "nodeType": null, "messageId": "1377", "endLine": 4, "endColumn": 50}, {"ruleId": "1375", "severity": 1, "message": "1423", "line": 17, "column": 3, "nodeType": null, "messageId": "1377", "endLine": 17, "endColumn": 7}, {"ruleId": "1375", "severity": 1, "message": "1424", "line": 19, "column": 3, "nodeType": null, "messageId": "1377", "endLine": 19, "endColumn": 11}, {"ruleId": "1375", "severity": 1, "message": "1425", "line": 10, "column": 8, "nodeType": null, "messageId": "1377", "endLine": 10, "endColumn": 12}, {"ruleId": "1375", "severity": 1, "message": "1426", "line": 14, "column": 3, "nodeType": null, "messageId": "1377", "endLine": 14, "endColumn": 14}, {"ruleId": "1375", "severity": 1, "message": "1427", "line": 15, "column": 3, "nodeType": null, "messageId": "1377", "endLine": 15, "endColumn": 16}, {"ruleId": "1375", "severity": 1, "message": "1428", "line": 16, "column": 3, "nodeType": null, "messageId": "1377", "endLine": 16, "endColumn": 11}, {"ruleId": "1375", "severity": 1, "message": "1429", "line": 17, "column": 3, "nodeType": null, "messageId": "1377", "endLine": 17, "endColumn": 9}, {"ruleId": "1375", "severity": 1, "message": "1430", "line": 18, "column": 3, "nodeType": null, "messageId": "1377", "endLine": 18, "endColumn": 8}, {"ruleId": "1375", "severity": 1, "message": "1431", "line": 19, "column": 3, "nodeType": null, "messageId": "1377", "endLine": 19, "endColumn": 13}, {"ruleId": "1375", "severity": 1, "message": "1432", "line": 20, "column": 3, "nodeType": null, "messageId": "1377", "endLine": 20, "endColumn": 10}, {"ruleId": "1375", "severity": 1, "message": "1433", "line": 21, "column": 3, "nodeType": null, "messageId": "1377", "endLine": 21, "endColumn": 12}, {"ruleId": "1375", "severity": 1, "message": "1434", "line": 22, "column": 3, "nodeType": null, "messageId": "1377", "endLine": 22, "endColumn": 13}, {"ruleId": "1375", "severity": 1, "message": "1435", "line": 23, "column": 3, "nodeType": null, "messageId": "1377", "endLine": 23, "endColumn": 9}, {"ruleId": "1375", "severity": 1, "message": "1436", "line": 24, "column": 3, "nodeType": null, "messageId": "1377", "endLine": 24, "endColumn": 16}, {"ruleId": "1375", "severity": 1, "message": "1423", "line": 25, "column": 3, "nodeType": null, "messageId": "1377", "endLine": 25, "endColumn": 7}, {"ruleId": "1375", "severity": 1, "message": "1437", "line": 26, "column": 3, "nodeType": null, "messageId": "1377", "endLine": 26, "endColumn": 12}, {"ruleId": "1375", "severity": 1, "message": "1438", "line": 27, "column": 3, "nodeType": null, "messageId": "1377", "endLine": 27, "endColumn": 10}, {"ruleId": "1375", "severity": 1, "message": "1439", "line": 28, "column": 3, "nodeType": null, "messageId": "1377", "endLine": 28, "endColumn": 12}, {"ruleId": "1375", "severity": 1, "message": "1424", "line": 29, "column": 3, "nodeType": null, "messageId": "1377", "endLine": 29, "endColumn": 11}, {"ruleId": "1375", "severity": 1, "message": "1440", "line": 30, "column": 3, "nodeType": null, "messageId": "1377", "endLine": 30, "endColumn": 11}, {"ruleId": "1375", "severity": 1, "message": "1441", "line": 31, "column": 3, "nodeType": null, "messageId": "1377", "endLine": 31, "endColumn": 8}, {"ruleId": "1375", "severity": 1, "message": "1442", "line": 32, "column": 3, "nodeType": null, "messageId": "1377", "endLine": 32, "endColumn": 15}, {"ruleId": "1375", "severity": 1, "message": "1443", "line": 33, "column": 3, "nodeType": null, "messageId": "1377", "endLine": 33, "endColumn": 8}, {"ruleId": "1375", "severity": 1, "message": "1444", "line": 34, "column": 3, "nodeType": null, "messageId": "1377", "endLine": 34, "endColumn": 8}, {"ruleId": "1375", "severity": 1, "message": "1445", "line": 39, "column": 10, "nodeType": null, "messageId": "1377", "endLine": 39, "endColumn": 20}, {"ruleId": "1375", "severity": 1, "message": "1446", "line": 43, "column": 9, "nodeType": null, "messageId": "1377", "endLine": 43, "endColumn": 13}, {"ruleId": "1375", "severity": 1, "message": "1447", "line": 6, "column": 10, "nodeType": null, "messageId": "1377", "endLine": 6, "endColumn": 14}, {"ruleId": "1375", "severity": 1, "message": "1425", "line": 7, "column": 8, "nodeType": null, "messageId": "1377", "endLine": 7, "endColumn": 12}, {"ruleId": "1375", "severity": 1, "message": "1448", "line": 79, "column": 9, "nodeType": null, "messageId": "1377", "endLine": 79, "endColumn": 25}, {"ruleId": "1345", "severity": 1, "message": "1385", "line": 91, "column": 87, "nodeType": "1347", "messageId": "1348", "suggestions": "1449"}, {"ruleId": "1331", "severity": 1, "message": "1450", "line": 35, "column": 9, "nodeType": "1451", "endLine": 40, "endColumn": 4}, {"ruleId": "1375", "severity": 1, "message": "1452", "line": 11, "column": 11, "nodeType": null, "messageId": "1377", "endLine": 11, "endColumn": 15}, {"ruleId": "1375", "severity": 1, "message": "1453", "line": 3, "column": 10, "nodeType": null, "messageId": "1377", "endLine": 3, "endColumn": 16}, {"ruleId": "1375", "severity": 1, "message": "1454", "line": 5, "column": 3, "nodeType": null, "messageId": "1377", "endLine": 5, "endColumn": 10}, {"ruleId": "1375", "severity": 1, "message": "1455", "line": 6, "column": 3, "nodeType": null, "messageId": "1377", "endLine": 6, "endColumn": 17}, {"ruleId": "1375", "severity": 1, "message": "1456", "line": 7, "column": 3, "nodeType": null, "messageId": "1377", "endLine": 7, "endColumn": 17}, {"ruleId": "1375", "severity": 1, "message": "1427", "line": 9, "column": 10, "nodeType": null, "messageId": "1377", "endLine": 9, "endColumn": 23}, {"ruleId": "1375", "severity": 1, "message": "1457", "line": 9, "column": 25, "nodeType": null, "messageId": "1377", "endLine": 9, "endColumn": 33}, {"ruleId": "1375", "severity": 1, "message": "1458", "line": 9, "column": 35, "nodeType": null, "messageId": "1377", "endLine": 9, "endColumn": 43}, {"ruleId": "1375", "severity": 1, "message": "1445", "line": 18, "column": 10, "nodeType": null, "messageId": "1377", "endLine": 18, "endColumn": 20}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 30, "column": 26, "nodeType": "1311", "messageId": "1312", "endLine": 30, "endColumn": 29, "suggestions": "1459"}, {"ruleId": "1375", "severity": 1, "message": "1460", "line": 102, "column": 3, "nodeType": null, "messageId": "1377", "endLine": 102, "endColumn": 12}, {"ruleId": "1375", "severity": 1, "message": "1461", "line": 108, "column": 20, "nodeType": null, "messageId": "1377", "endLine": 108, "endColumn": 43}, {"ruleId": "1375", "severity": 1, "message": "1462", "line": 109, "column": 23, "nodeType": null, "messageId": "1377", "endLine": 109, "endColumn": 49}, {"ruleId": "1375", "severity": 1, "message": "1463", "line": 112, "column": 20, "nodeType": null, "messageId": "1377", "endLine": 112, "endColumn": 31}, {"ruleId": "1375", "severity": 1, "message": "1464", "line": 114, "column": 10, "nodeType": null, "messageId": "1377", "endLine": 114, "endColumn": 19}, {"ruleId": "1375", "severity": 1, "message": "1465", "line": 114, "column": 21, "nodeType": null, "messageId": "1377", "endLine": 114, "endColumn": 33}, {"ruleId": "1375", "severity": 1, "message": "1466", "line": 116, "column": 11, "nodeType": null, "messageId": "1377", "endLine": 116, "endColumn": 18}, {"ruleId": "1375", "severity": 1, "message": "1467", "line": 119, "column": 9, "nodeType": null, "messageId": "1377", "endLine": 119, "endColumn": 20}, {"ruleId": "1375", "severity": 1, "message": "1468", "line": 123, "column": 9, "nodeType": null, "messageId": "1377", "endLine": 123, "endColumn": 23}, {"ruleId": "1375", "severity": 1, "message": "1469", "line": 126, "column": 9, "nodeType": null, "messageId": "1377", "endLine": 126, "endColumn": 24}, {"ruleId": "1375", "severity": 1, "message": "1470", "line": 127, "column": 9, "nodeType": null, "messageId": "1377", "endLine": 127, "endColumn": 27}, {"ruleId": "1375", "severity": 1, "message": "1471", "line": 137, "column": 9, "nodeType": null, "messageId": "1377", "endLine": 137, "endColumn": 26}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 41, "column": 26, "nodeType": "1311", "messageId": "1312", "endLine": 41, "endColumn": 29, "suggestions": "1472"}, {"ruleId": "1375", "severity": 1, "message": "1473", "line": 7, "column": 10, "nodeType": null, "messageId": "1377", "endLine": 7, "endColumn": 21}, {"ruleId": "1375", "severity": 1, "message": "1474", "line": 18, "column": 25, "nodeType": null, "messageId": "1377", "endLine": 18, "endColumn": 42}, {"ruleId": "1375", "severity": 1, "message": "1418", "line": 19, "column": 9, "nodeType": null, "messageId": "1377", "endLine": 19, "endColumn": 15}, {"ruleId": "1375", "severity": 1, "message": "1475", "line": 46, "column": 24, "nodeType": null, "messageId": "1377", "endLine": 46, "endColumn": 25}, {"ruleId": "1331", "severity": 1, "message": "1476", "line": 118, "column": 6, "nodeType": "1333", "endLine": 118, "endColumn": 72, "suggestions": "1477"}, {"ruleId": "1375", "severity": 1, "message": "1445", "line": 6, "column": 10, "nodeType": null, "messageId": "1377", "endLine": 6, "endColumn": 20}, {"ruleId": "1375", "severity": 1, "message": "1478", "line": 18, "column": 3, "nodeType": null, "messageId": "1377", "endLine": 18, "endColumn": 9}, {"ruleId": "1375", "severity": 1, "message": "1479", "line": 45, "column": 3, "nodeType": null, "messageId": "1377", "endLine": 45, "endColumn": 12}, {"ruleId": "1375", "severity": 1, "message": "1480", "line": 11, "column": 55, "nodeType": null, "messageId": "1377", "endLine": 11, "endColumn": 59}, {"ruleId": "1375", "severity": 1, "message": "1481", "line": 15, "column": 7, "nodeType": null, "messageId": "1377", "endLine": 15, "endColumn": 24}, {"ruleId": "1375", "severity": 1, "message": "1482", "line": 46, "column": 9, "nodeType": null, "messageId": "1377", "endLine": 46, "endColumn": 25}, {"ruleId": "1483", "severity": 1, "message": "1484", "line": 5, "column": 18, "nodeType": "1485", "messageId": "1486", "endLine": 5, "endColumn": 31, "suggestions": "1487"}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 126, "column": 21, "nodeType": "1311", "messageId": "1312", "endLine": 126, "endColumn": 24, "suggestions": "1488"}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 193, "column": 21, "nodeType": "1311", "messageId": "1312", "endLine": 193, "endColumn": 24, "suggestions": "1489"}, {"ruleId": "1375", "severity": 1, "message": "1490", "line": 219, "column": 16, "nodeType": null, "messageId": "1377", "endLine": 219, "endColumn": 24}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 23, "column": 20, "nodeType": "1311", "messageId": "1312", "endLine": 23, "endColumn": 23, "suggestions": "1491"}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 40, "column": 20, "nodeType": "1311", "messageId": "1312", "endLine": 40, "endColumn": 23, "suggestions": "1492"}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 124, "column": 22, "nodeType": "1311", "messageId": "1312", "endLine": 124, "endColumn": 25, "suggestions": "1493"}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 199, "column": 22, "nodeType": "1311", "messageId": "1312", "endLine": 199, "endColumn": 25, "suggestions": "1494"}, {"ruleId": "1375", "severity": 1, "message": "1495", "line": 225, "column": 9, "nodeType": null, "messageId": "1377", "endLine": 225, "endColumn": 20}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 254, "column": 22, "nodeType": "1311", "messageId": "1312", "endLine": 254, "endColumn": 25, "suggestions": "1496"}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 295, "column": 22, "nodeType": "1311", "messageId": "1312", "endLine": 295, "endColumn": 25, "suggestions": "1497"}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 76, "column": 34, "nodeType": "1311", "messageId": "1312", "endLine": 76, "endColumn": 37, "suggestions": "1498"}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 54, "column": 34, "nodeType": "1311", "messageId": "1312", "endLine": 54, "endColumn": 37, "suggestions": "1499"}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 12, "column": 47, "nodeType": "1311", "messageId": "1312", "endLine": 12, "endColumn": 50, "suggestions": "1500"}, {"ruleId": "1375", "severity": 1, "message": "1411", "line": 46, "column": 16, "nodeType": null, "messageId": "1377", "endLine": 46, "endColumn": 21}, {"ruleId": "1331", "severity": 1, "message": "1501", "line": 80, "column": 6, "nodeType": "1333", "endLine": 80, "endColumn": 47, "suggestions": "1502"}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 295, "column": 34, "nodeType": "1311", "messageId": "1312", "endLine": 295, "endColumn": 37, "suggestions": "1503"}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 301, "column": 41, "nodeType": "1311", "messageId": "1312", "endLine": 301, "endColumn": 44, "suggestions": "1504"}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 318, "column": 15, "nodeType": "1311", "messageId": "1312", "endLine": 318, "endColumn": 18, "suggestions": "1505"}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 341, "column": 25, "nodeType": "1311", "messageId": "1312", "endLine": 341, "endColumn": 28, "suggestions": "1506"}, {"ruleId": "1375", "severity": 1, "message": "1507", "line": 5, "column": 3, "nodeType": null, "messageId": "1377", "endLine": 5, "endColumn": 13}, {"ruleId": "1375", "severity": 1, "message": "1508", "line": 9, "column": 3, "nodeType": null, "messageId": "1377", "endLine": 9, "endColumn": 14}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 15, "column": 19, "nodeType": "1311", "messageId": "1312", "endLine": 15, "endColumn": 22, "suggestions": "1509"}, {"ruleId": "1375", "severity": 1, "message": "1508", "line": 30, "column": 11, "nodeType": null, "messageId": "1377", "endLine": 30, "endColumn": 22}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 30, "column": 27, "nodeType": "1311", "messageId": "1312", "endLine": 30, "endColumn": 30, "suggestions": "1510"}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 48, "column": 21, "nodeType": "1311", "messageId": "1312", "endLine": 48, "endColumn": 24, "suggestions": "1511"}, {"ruleId": "1375", "severity": 1, "message": "1512", "line": 109, "column": 20, "nodeType": null, "messageId": "1377", "endLine": 109, "endColumn": 32}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 123, "column": 24, "nodeType": "1311", "messageId": "1312", "endLine": 123, "endColumn": 27, "suggestions": "1513"}, {"ruleId": "1375", "severity": 1, "message": "1475", "line": 126, "column": 18, "nodeType": null, "messageId": "1377", "endLine": 126, "endColumn": 19}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 169, "column": 17, "nodeType": "1311", "messageId": "1312", "endLine": 169, "endColumn": 20, "suggestions": "1514"}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 176, "column": 18, "nodeType": "1311", "messageId": "1312", "endLine": 176, "endColumn": 21, "suggestions": "1515"}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 178, "column": 12, "nodeType": "1311", "messageId": "1312", "endLine": 178, "endColumn": 15, "suggestions": "1516"}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 191, "column": 17, "nodeType": "1311", "messageId": "1312", "endLine": 191, "endColumn": 20, "suggestions": "1517"}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 193, "column": 12, "nodeType": "1311", "messageId": "1312", "endLine": 193, "endColumn": 15, "suggestions": "1518"}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 206, "column": 19, "nodeType": "1311", "messageId": "1312", "endLine": 206, "endColumn": 22, "suggestions": "1519"}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 208, "column": 12, "nodeType": "1311", "messageId": "1312", "endLine": 208, "endColumn": 15, "suggestions": "1520"}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 221, "column": 20, "nodeType": "1311", "messageId": "1312", "endLine": 221, "endColumn": 23, "suggestions": "1521"}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 228, "column": 20, "nodeType": "1311", "messageId": "1312", "endLine": 228, "endColumn": 23, "suggestions": "1522"}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 246, "column": 28, "nodeType": "1311", "messageId": "1312", "endLine": 246, "endColumn": 31, "suggestions": "1523"}, {"ruleId": "1375", "severity": 1, "message": "1460", "line": 257, "column": 45, "nodeType": null, "messageId": "1377", "endLine": 257, "endColumn": 54}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 79, "column": 17, "nodeType": "1311", "messageId": "1312", "endLine": 79, "endColumn": 20, "suggestions": "1524"}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 131, "column": 17, "nodeType": "1311", "messageId": "1312", "endLine": 131, "endColumn": 20, "suggestions": "1525"}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 158, "column": 17, "nodeType": "1311", "messageId": "1312", "endLine": 158, "endColumn": 20, "suggestions": "1526"}, {"ruleId": "1375", "severity": 1, "message": "1527", "line": 68, "column": 7, "nodeType": null, "messageId": "1377", "endLine": 68, "endColumn": 31}, {"ruleId": "1375", "severity": 1, "message": "1411", "line": 286, "column": 14, "nodeType": null, "messageId": "1377", "endLine": 286, "endColumn": 19}, {"ruleId": "1375", "severity": 1, "message": "1411", "line": 298, "column": 14, "nodeType": null, "messageId": "1377", "endLine": 298, "endColumn": 19}, {"ruleId": "1375", "severity": 1, "message": "1528", "line": 380, "column": 17, "nodeType": null, "messageId": "1377", "endLine": 380, "endColumn": 20}, {"ruleId": "1375", "severity": 1, "message": "1529", "line": 18, "column": 32, "nodeType": null, "messageId": "1377", "endLine": 18, "endColumn": 40}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 196, "column": 28, "nodeType": "1311", "messageId": "1312", "endLine": 196, "endColumn": 31, "suggestions": "1530"}, {"ruleId": "1375", "severity": 1, "message": "1531", "line": 198, "column": 15, "nodeType": null, "messageId": "1377", "endLine": 198, "endColumn": 18}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 8, "column": 36, "nodeType": "1311", "messageId": "1312", "endLine": 8, "endColumn": 39, "suggestions": "1532"}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 22, "column": 24, "nodeType": "1311", "messageId": "1312", "endLine": 22, "endColumn": 27, "suggestions": "1533"}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 45, "column": 23, "nodeType": "1311", "messageId": "1312", "endLine": 45, "endColumn": 26, "suggestions": "1534"}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 46, "column": 26, "nodeType": "1311", "messageId": "1312", "endLine": 46, "endColumn": 29, "suggestions": "1535"}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 160, "column": 24, "nodeType": "1311", "messageId": "1312", "endLine": 160, "endColumn": 27, "suggestions": "1536"}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 176, "column": 21, "nodeType": "1311", "messageId": "1312", "endLine": 176, "endColumn": 24, "suggestions": "1537"}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 41, "column": 18, "nodeType": "1311", "messageId": "1312", "endLine": 41, "endColumn": 21, "suggestions": "1538"}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 67, "column": 34, "nodeType": "1311", "messageId": "1312", "endLine": 67, "endColumn": 37, "suggestions": "1539"}, {"ruleId": "1375", "severity": 1, "message": "1540", "line": 3, "column": 37, "nodeType": null, "messageId": "1377", "endLine": 3, "endColumn": 52}, {"ruleId": "1375", "severity": 1, "message": "1541", "line": 3, "column": 54, "nodeType": null, "messageId": "1377", "endLine": 3, "endColumn": 66}, {"ruleId": "1375", "severity": 1, "message": "1542", "line": 7, "column": 11, "nodeType": null, "messageId": "1377", "endLine": 7, "endColumn": 14}, {"ruleId": "1375", "severity": 1, "message": "1542", "line": 85, "column": 11, "nodeType": null, "messageId": "1377", "endLine": 85, "endColumn": 14}, {"ruleId": "1375", "severity": 1, "message": "1543", "line": 274, "column": 23, "nodeType": null, "messageId": "1377", "endLine": 274, "endColumn": 31}, {"ruleId": "1375", "severity": 1, "message": "1543", "line": 337, "column": 23, "nodeType": null, "messageId": "1377", "endLine": 337, "endColumn": 31}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 69, "column": 13, "nodeType": "1311", "messageId": "1312", "endLine": 69, "endColumn": 16, "suggestions": "1544"}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 62, "column": 37, "nodeType": "1311", "messageId": "1312", "endLine": 62, "endColumn": 40, "suggestions": "1545"}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 73, "column": 37, "nodeType": "1311", "messageId": "1312", "endLine": 73, "endColumn": 40, "suggestions": "1546"}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 94, "column": 37, "nodeType": "1311", "messageId": "1312", "endLine": 94, "endColumn": 40, "suggestions": "1547"}, {"ruleId": "1375", "severity": 1, "message": "1548", "line": 14, "column": 14, "nodeType": null, "messageId": "1377", "endLine": 14, "endColumn": 21}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 79, "column": 21, "nodeType": "1311", "messageId": "1312", "endLine": 79, "endColumn": 24, "suggestions": "1549"}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 4, "column": 24, "nodeType": "1311", "messageId": "1312", "endLine": 4, "endColumn": 27, "suggestions": "1550"}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 4, "column": 46, "nodeType": "1311", "messageId": "1312", "endLine": 4, "endColumn": 49, "suggestions": "1551"}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 86, "column": 88, "nodeType": "1311", "messageId": "1312", "endLine": 86, "endColumn": 91, "suggestions": "1552"}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 91, "column": 81, "nodeType": "1311", "messageId": "1312", "endLine": 91, "endColumn": 84, "suggestions": "1553"}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 96, "column": 69, "nodeType": "1311", "messageId": "1312", "endLine": 96, "endColumn": 72, "suggestions": "1554"}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 119, "column": 34, "nodeType": "1311", "messageId": "1312", "endLine": 119, "endColumn": 37, "suggestions": "1555"}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 122, "column": 43, "nodeType": "1311", "messageId": "1312", "endLine": 122, "endColumn": 46, "suggestions": "1556"}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 17, "column": 16, "nodeType": "1311", "messageId": "1312", "endLine": 17, "endColumn": 19, "suggestions": "1557"}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 146, "column": 47, "nodeType": "1311", "messageId": "1312", "endLine": 146, "endColumn": 50, "suggestions": "1558"}, {"ruleId": "1375", "severity": 1, "message": "1559", "line": 350, "column": 3, "nodeType": null, "messageId": "1377", "endLine": 350, "endColumn": 23}, {"ruleId": "1375", "severity": 1, "message": "1560", "line": 351, "column": 3, "nodeType": null, "messageId": "1377", "endLine": 351, "endColumn": 16}, {"ruleId": "1375", "severity": 1, "message": "1561", "line": 362, "column": 10, "nodeType": null, "messageId": "1377", "endLine": 362, "endColumn": 26}, {"ruleId": "1375", "severity": 1, "message": "1562", "line": 362, "column": 28, "nodeType": null, "messageId": "1377", "endLine": 362, "endColumn": 47}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 404, "column": 61, "nodeType": "1311", "messageId": "1312", "endLine": 404, "endColumn": 64, "suggestions": "1563"}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 459, "column": 34, "nodeType": "1311", "messageId": "1312", "endLine": 459, "endColumn": 37, "suggestions": "1564"}, {"ruleId": "1375", "severity": 1, "message": "1475", "line": 60, "column": 18, "nodeType": null, "messageId": "1377", "endLine": 60, "endColumn": 19}, {"ruleId": "1331", "severity": 1, "message": "1565", "line": 79, "column": 6, "nodeType": "1333", "endLine": 79, "endColumn": 18, "suggestions": "1566"}, {"ruleId": "1375", "severity": 1, "message": "1475", "line": 36, "column": 18, "nodeType": null, "messageId": "1377", "endLine": 36, "endColumn": 19}, {"ruleId": "1375", "severity": 1, "message": "1567", "line": 99, "column": 40, "nodeType": null, "messageId": "1377", "endLine": 99, "endColumn": 54}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 108, "column": 62, "nodeType": "1311", "messageId": "1312", "endLine": 108, "endColumn": 65, "suggestions": "1568"}, {"ruleId": "1375", "severity": 1, "message": "1569", "line": 3, "column": 10, "nodeType": null, "messageId": "1377", "endLine": 3, "endColumn": 22}, {"ruleId": "1375", "severity": 1, "message": "1570", "line": 4, "column": 10, "nodeType": null, "messageId": "1377", "endLine": 4, "endColumn": 25}, {"ruleId": "1375", "severity": 1, "message": "1571", "line": 6, "column": 10, "nodeType": null, "messageId": "1377", "endLine": 6, "endColumn": 14}, {"ruleId": "1375", "severity": 1, "message": "1572", "line": 6, "column": 16, "nodeType": null, "messageId": "1377", "endLine": 6, "endColumn": 27}, {"ruleId": "1375", "severity": 1, "message": "1573", "line": 6, "column": 29, "nodeType": null, "messageId": "1377", "endLine": 6, "endColumn": 39}, {"ruleId": "1375", "severity": 1, "message": "1574", "line": 6, "column": 41, "nodeType": null, "messageId": "1377", "endLine": 6, "endColumn": 50}, {"ruleId": "1375", "severity": 1, "message": "1453", "line": 7, "column": 10, "nodeType": null, "messageId": "1377", "endLine": 7, "endColumn": 16}, {"ruleId": "1375", "severity": 1, "message": "1425", "line": 8, "column": 8, "nodeType": null, "messageId": "1377", "endLine": 8, "endColumn": 12}, {"ruleId": "1345", "severity": 1, "message": "1385", "line": 131, "column": 33, "nodeType": "1347", "messageId": "1348", "suggestions": "1575"}, {"ruleId": "1345", "severity": 1, "message": "1346", "line": 142, "column": 29, "nodeType": "1347", "messageId": "1348", "suggestions": "1576"}, {"ruleId": "1345", "severity": 1, "message": "1346", "line": 142, "column": 37, "nodeType": "1347", "messageId": "1348", "suggestions": "1577"}, {"ruleId": "1345", "severity": 1, "message": "1346", "line": 142, "column": 42, "nodeType": "1347", "messageId": "1348", "suggestions": "1578"}, {"ruleId": "1345", "severity": 1, "message": "1346", "line": 142, "column": 48, "nodeType": "1347", "messageId": "1348", "suggestions": "1579"}, {"ruleId": "1375", "severity": 1, "message": "1580", "line": 19, "column": 16, "nodeType": null, "messageId": "1377", "endLine": 19, "endColumn": 20}, {"ruleId": "1375", "severity": 1, "message": "1581", "line": 19, "column": 34, "nodeType": null, "messageId": "1377", "endLine": 19, "endColumn": 47}, {"ruleId": "1375", "severity": 1, "message": "1582", "line": 20, "column": 11, "nodeType": null, "messageId": "1377", "endLine": 20, "endColumn": 16}, {"ruleId": "1375", "severity": 1, "message": "1583", "line": 20, "column": 18, "nodeType": null, "messageId": "1377", "endLine": 20, "endColumn": 34}, {"ruleId": "1375", "severity": 1, "message": "1584", "line": 20, "column": 36, "nodeType": null, "messageId": "1377", "endLine": 20, "endColumn": 56}, {"ruleId": "1375", "severity": 1, "message": "1411", "line": 123, "column": 14, "nodeType": null, "messageId": "1377", "endLine": 123, "endColumn": 19}, {"ruleId": "1345", "severity": 1, "message": "1385", "line": 97, "column": 21, "nodeType": "1347", "messageId": "1348", "suggestions": "1585"}, {"ruleId": "1345", "severity": 1, "message": "1346", "line": 97, "column": 62, "nodeType": "1347", "messageId": "1348", "suggestions": "1586"}, {"ruleId": "1345", "severity": 1, "message": "1346", "line": 97, "column": 81, "nodeType": "1347", "messageId": "1348", "suggestions": "1587"}, {"ruleId": "1588", "severity": 1, "message": "1589", "line": 141, "column": 23, "nodeType": "1590", "endLine": 145, "endColumn": 25}, {"ruleId": "1345", "severity": 1, "message": "1385", "line": 23, "column": 32, "nodeType": "1347", "messageId": "1348", "suggestions": "1591"}, {"ruleId": "1345", "severity": 1, "message": "1385", "line": 23, "column": 53, "nodeType": "1347", "messageId": "1348", "suggestions": "1592"}, {"ruleId": "1375", "severity": 1, "message": "1593", "line": 9, "column": 38, "nodeType": null, "messageId": "1377", "endLine": 9, "endColumn": 44}, {"ruleId": "1375", "severity": 1, "message": "1594", "line": 3, "column": 22, "nodeType": null, "messageId": "1377", "endLine": 3, "endColumn": 25}, {"ruleId": "1375", "severity": 1, "message": "1595", "line": 3, "column": 27, "nodeType": null, "messageId": "1377", "endLine": 3, "endColumn": 32}, {"ruleId": "1375", "severity": 1, "message": "1596", "line": 3, "column": 34, "nodeType": null, "messageId": "1377", "endLine": 3, "endColumn": 45}, {"ruleId": "1375", "severity": 1, "message": "1597", "line": 3, "column": 47, "nodeType": null, "messageId": "1377", "endLine": 3, "endColumn": 56}, {"ruleId": "1375", "severity": 1, "message": "1453", "line": 7, "column": 10, "nodeType": null, "messageId": "1377", "endLine": 7, "endColumn": 16}, {"ruleId": "1375", "severity": 1, "message": "1598", "line": 10, "column": 5, "nodeType": null, "messageId": "1377", "endLine": 10, "endColumn": 17}, {"ruleId": "1375", "severity": 1, "message": "1599", "line": 11, "column": 5, "nodeType": null, "messageId": "1377", "endLine": 11, "endColumn": 24}, {"ruleId": "1375", "severity": 1, "message": "1600", "line": 12, "column": 5, "nodeType": null, "messageId": "1377", "endLine": 12, "endColumn": 21}, {"ruleId": "1375", "severity": 1, "message": "1601", "line": 13, "column": 5, "nodeType": null, "messageId": "1377", "endLine": 13, "endColumn": 24}, {"ruleId": "1375", "severity": 1, "message": "1602", "line": 15, "column": 10, "nodeType": null, "messageId": "1377", "endLine": 15, "endColumn": 16}, {"ruleId": "1375", "severity": 1, "message": "1603", "line": 15, "column": 18, "nodeType": null, "messageId": "1377", "endLine": 15, "endColumn": 33}, {"ruleId": "1375", "severity": 1, "message": "1604", "line": 105, "column": 12, "nodeType": null, "messageId": "1377", "endLine": 105, "endColumn": 25}, {"ruleId": "1375", "severity": 1, "message": "1605", "line": 105, "column": 27, "nodeType": null, "messageId": "1377", "endLine": 105, "endColumn": 43}, {"ruleId": "1375", "severity": 1, "message": "1606", "line": 107, "column": 11, "nodeType": null, "messageId": "1377", "endLine": 107, "endColumn": 20}, {"ruleId": "1375", "severity": 1, "message": "1607", "line": 115, "column": 11, "nodeType": null, "messageId": "1377", "endLine": 115, "endColumn": 22}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 10, "column": 19, "nodeType": "1311", "messageId": "1312", "endLine": 10, "endColumn": 22, "suggestions": "1608"}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 11, "column": 18, "nodeType": "1311", "messageId": "1312", "endLine": 11, "endColumn": 21, "suggestions": "1609"}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 17, "column": 25, "nodeType": "1311", "messageId": "1312", "endLine": 17, "endColumn": 28, "suggestions": "1610"}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 16, "column": 25, "nodeType": "1311", "messageId": "1312", "endLine": 16, "endColumn": 28, "suggestions": "1611"}, {"ruleId": "1375", "severity": 1, "message": "1612", "line": 21, "column": 3, "nodeType": null, "messageId": "1377", "endLine": 21, "endColumn": 16}, {"ruleId": "1375", "severity": 1, "message": "1613", "line": 28, "column": 11, "nodeType": null, "messageId": "1377", "endLine": 28, "endColumn": 16}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 13, "column": 19, "nodeType": "1311", "messageId": "1312", "endLine": 13, "endColumn": 22, "suggestions": "1614"}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 14, "column": 18, "nodeType": "1311", "messageId": "1312", "endLine": 14, "endColumn": 21, "suggestions": "1615"}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 15, "column": 17, "nodeType": "1311", "messageId": "1312", "endLine": 15, "endColumn": 20, "suggestions": "1616"}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 16, "column": 16, "nodeType": "1311", "messageId": "1312", "endLine": 16, "endColumn": 19, "suggestions": "1617"}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 17, "column": 13, "nodeType": "1311", "messageId": "1312", "endLine": 17, "endColumn": 16, "suggestions": "1618"}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 20, "column": 31, "nodeType": "1311", "messageId": "1312", "endLine": 20, "endColumn": 34, "suggestions": "1619"}, {"ruleId": "1331", "severity": 1, "message": "1620", "line": 58, "column": 9, "nodeType": "1451", "endLine": 63, "endColumn": 4}, {"ruleId": "1375", "severity": 1, "message": "1621", "line": 73, "column": 41, "nodeType": null, "messageId": "1377", "endLine": 73, "endColumn": 46}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 9, "column": 22, "nodeType": "1311", "messageId": "1312", "endLine": 9, "endColumn": 25, "suggestions": "1622"}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 127, "column": 41, "nodeType": "1311", "messageId": "1312", "endLine": 127, "endColumn": 44, "suggestions": "1623"}, {"ruleId": "1309", "severity": 1, "message": "1310", "line": 150, "column": 34, "nodeType": "1311", "messageId": "1312", "endLine": 150, "endColumn": 37, "suggestions": "1624"}, "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["1625", "1626"], ["1627", "1628"], ["1629", "1630"], ["1631", "1632"], ["1633", "1634"], ["1635", "1636"], ["1637", "1638"], ["1639", "1640"], ["1641", "1642"], ["1643", "1644"], ["1645", "1646"], ["1647", "1648"], ["1649", "1650"], ["1651", "1652"], ["1653", "1654"], ["1655", "1656"], ["1657", "1658"], ["1659", "1660"], "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchData'. Either include it or remove the dependency array.", "ArrayExpression", ["1661"], ["1662", "1663"], ["1664", "1665"], ["1666", "1667"], ["1668", "1669"], ["1670", "1671"], ["1672", "1673"], ["1674", "1675"], ["1676", "1677"], ["1678"], ["1679", "1680"], "react/no-unescaped-entities", "`\"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.", "JSXText", "unescapedEntityAlts", ["1681", "1682", "1683", "1684"], ["1685", "1686", "1687", "1688"], ["1689", "1690"], ["1691", "1692"], ["1693", "1694"], ["1695", "1696"], ["1697", "1698"], ["1699", "1700"], ["1701", "1702"], ["1703", "1704"], ["1705", "1706"], ["1707", "1708"], ["1709", "1710"], ["1711", "1712"], ["1713", "1714"], ["1715", "1716"], ["1717", "1718"], ["1719", "1720"], ["1721", "1722"], ["1723", "1724"], ["1725", "1726"], ["1727", "1728"], ["1729", "1730"], ["1731", "1732"], ["1733", "1734"], ["1735", "1736"], "@typescript-eslint/no-unused-vars", "'_' is assigned a value but never used.", "unusedVar", "'request' is defined but never used.", ["1737", "1738"], ["1739", "1740"], "'getValues' is assigned a value but never used.", "'ArrowLeft' is defined but never used.", ["1741", "1742"], ["1743", "1744"], "`'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.", ["1745", "1746", "1747", "1748"], ["1749", "1750"], ["1751", "1752"], ["1753", "1754"], ["1755", "1756"], "'setSuccess' is assigned a value but never used.", ["1757", "1758", "1759", "1760"], "'emailVerificationSent' is assigned a value but never used.", ["1761", "1762", "1763", "1764"], "'backendUser' is assigned a value but never used.", "'isUserLoading' is assigned a value but never used.", "'setValue' is assigned a value but never used.", ["1765", "1766"], "'initials' is assigned a value but never used.", ["1767", "1768"], ["1769", "1770"], ["1771", "1772", "1773", "1774"], ["1775", "1776"], "React Hook useEffect has a missing dependency: 'projectId'. Either include it or remove the dependency array.", ["1777"], ["1778", "1779"], "'reset' is assigned a value but never used.", ["1780", "1781"], ["1782", "1783"], ["1784", "1785"], "'error' is defined but never used.", ["1786", "1787"], "'_data' is defined but never used.", ["1788", "1789"], ["1790", "1791", "1792", "1793"], ["1794", "1795"], "'logout' is assigned a value but never used.", "'router' is assigned a value but never used.", "'Tabs' is defined but never used.", "'TabsContent' is defined but never used.", "'TabsList' is defined but never used.", "'TabsTrigger' is defined but never used.", "'Star' is defined but never used.", "'FileText' is defined but never used.", "'Link' is defined but never used.", "'CheckCircle' is defined but never used.", "'MessageCircle' is defined but never used.", "'Building' is defined but never used.", "'Target' is defined but never used.", "'Users' is defined but never used.", "'TrendingUp' is defined but never used.", "'Package' is defined but never used.", "'BarChart3' is defined but never used.", "'DollarSign' is defined but never used.", "'Shield' is defined but never used.", "'AlertTriangle' is defined but never used.", "'Briefcase' is defined but never used.", "'Palette' is defined but never used.", "'Megaphone' is defined but never used.", "'Settings' is defined but never used.", "'Globe' is defined but never used.", "'ShoppingCart' is defined but never used.", "'Heart' is defined but never used.", "'Clock' is defined but never used.", "'ICON_SIZES' is defined but never used.", "'Icon' is assigned a value but never used.", "'Plus' is defined but never used.", "'getProgressColor' is assigned a value but never used.", ["1796", "1797", "1798", "1799"], "The 'hints' array makes the dependencies of useEffect Hook (at line 82) change on every render. Move it inside the useEffect callback. Alternatively, wrap the initialization of 'hints' in its own useMemo() Hook.", "VariableDeclarator", "'user' is assigned a value but never used.", "'Button' is defined but never used.", "'Tooltip' is defined but never used.", "'TooltipContent' is defined but never used.", "'TooltipTrigger' is defined but never used.", "'Columns2' is defined but never used.", "'Columns3' is defined but never used.", ["1800", "1801"], "'projectId' is defined but never used.", "'externalIsChatCollapsed' is defined but never used.", "'externalSetIsChatCollapsed' is defined but never used.", "'setMessages' is assigned a value but never used.", "'isLoading' is assigned a value but never used.", "'setIsLoading' is assigned a value but never used.", "'setOpen' is assigned a value but never used.", "'isCollapsed' is assigned a value but never used.", "'setIsCollapsed' is assigned a value but never used.", "'isChatCollapsed' is assigned a value but never used.", "'setIsChatCollapsed' is assigned a value but never used.", "'handleWidthToggle' is assigned a value but never used.", ["1802", "1803"], "'useUserSync' is defined but never used.", "'setUserProperties' is assigned a value but never used.", "'e' is defined but never used.", "React Hook useEffect has a missing dependency: 'identifyUser'. Either include it or remove the dependency array.", ["1804"], "'layout' is assigned a value but never used.", "'className' is defined but never used.", "'_ref' is defined but never used.", "'autoScrollEnabled' is assigned a value but never used.", "'dotPulseDuration' is assigned a value but never used.", "@typescript-eslint/no-empty-object-type", "An interface declaring no members is equivalent to its supertype.", "Identifier", "noEmptyInterfaceWithSuper", ["1805"], ["1806", "1807"], ["1808", "1809"], "'apiError' is defined but never used.", ["1810", "1811"], ["1812", "1813"], ["1814", "1815"], ["1816", "1817"], "'queryClient' is assigned a value but never used.", ["1818", "1819"], ["1820", "1821"], ["1822", "1823"], ["1824", "1825"], ["1826", "1827"], "React Hook useEffect has a missing dependency: 'syncUserToBackend'. Either include it or remove the dependency array.", ["1828"], ["1829", "1830"], ["1831", "1832"], ["1833", "1834"], ["1835", "1836"], "'AuthTokens' is defined but never used.", "'ApiResponse' is defined but never used.", ["1837", "1838"], ["1839", "1840"], ["1841", "1842"], "'refreshError' is defined but never used.", ["1843", "1844"], ["1845", "1846"], ["1847", "1848"], ["1849", "1850"], ["1851", "1852"], ["1853", "1854"], ["1855", "1856"], ["1857", "1858"], ["1859", "1860"], ["1861", "1862"], ["1863", "1864"], ["1865", "1866"], ["1867", "1868"], ["1869", "1870"], "'generateVerificationCode' is assigned a value but never used.", "'key' is assigned a value but never used.", "'ideaText' is defined but never used.", ["1871", "1872"], "'url' is defined but never used.", ["1873", "1874"], ["1875", "1876"], ["1877", "1878"], ["1879", "1880"], ["1881", "1882"], ["1883", "1884"], ["1885", "1886"], ["1887", "1888"], "'BusinessSection' is defined but never used.", "'BusinessItem' is defined but never used.", "'get' is defined but never used.", "'response' is assigned a value but never used.", ["1889", "1890"], ["1891", "1892"], ["1893", "1894"], ["1895", "1896"], "'posthog' is defined but never used.", ["1897", "1898"], ["1899", "1900"], ["1901", "1902"], ["1903", "1904"], ["1905", "1906"], ["1907", "1908"], ["1909", "1910"], ["1911", "1912"], ["1913", "1914"], ["1915", "1916"], "'selectedBusinessItem' is defined but never used.", "'onBackToItems' is defined but never used.", "'isHowItWorksOpen' is assigned a value but never used.", "'setIsHowItWorksOpen' is assigned a value but never used.", ["1917", "1918"], ["1919", "1920"], "React Hook useEffect has a missing dependency: 'fetchTokens'. Either include it or remove the dependency array.", ["1921"], "'businessItemId' is defined but never used.", ["1922", "1923"], "'PostHogDebug' is defined but never used.", "'ClerkTokenDebug' is defined but never used.", "'Card' is defined but never used.", "'CardContent' is defined but never used.", "'CardHeader' is defined but never used.", "'CardTitle' is defined but never used.", ["1924", "1925", "1926", "1927"], ["1928", "1929", "1930", "1931"], ["1932", "1933", "1934", "1935"], ["1936", "1937", "1938", "1939"], ["1940", "1941", "1942", "1943"], "'post' is assigned a value but never used.", "'apiIsSignedIn' is assigned a value but never used.", "'token' is assigned a value but never used.", "'fetchAndLogToken' is assigned a value but never used.", "'copyTokenToClipboard' is assigned a value but never used.", ["1944", "1945", "1946", "1947"], ["1948", "1949", "1950", "1951"], ["1952", "1953", "1954", "1955"], "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "JSXOpeningElement", ["1956", "1957", "1958", "1959"], ["1960", "1961", "1962", "1963"], "'Share2' is defined but never used.", "'Bot' is defined but never used.", "'Check' is defined but never used.", "'ChevronDown' is defined but never used.", "'Paperclip' is defined but never used.", "'DropdownMenu' is defined but never used.", "'DropdownMenuContent' is defined but never used.", "'DropdownMenuItem' is defined but never used.", "'DropdownMenuTrigger' is defined but never used.", "'motion' is defined but never used.", "'AnimatePresence' is defined but never used.", "'selectedModel' is assigned a value but never used.", "'setSelectedModel' is assigned a value but never used.", "'AI_MODELS' is assigned a value but never used.", "'MODEL_ICONS' is assigned a value but never used.", ["1964", "1965"], ["1966", "1967"], ["1968", "1969"], ["1970", "1971"], "'activeContent' is defined but never used.", "'state' is assigned a value but never used.", ["1972", "1973"], ["1974", "1975"], ["1976", "1977"], ["1978", "1979"], ["1980", "1981"], ["1982", "1983"], "The 'hints' array makes the dependencies of useEffect Hook (at line 92) change on every render. Move it inside the useEffect callback. Alternatively, wrap the initialization of 'hints' in its own useMemo() Hook.", "'index' is defined but never used.", ["1984", "1985"], ["1986", "1987"], ["1988", "1989"], {"messageId": "1990", "fix": "1991", "desc": "1992"}, {"messageId": "1993", "fix": "1994", "desc": "1995"}, {"messageId": "1990", "fix": "1996", "desc": "1992"}, {"messageId": "1993", "fix": "1997", "desc": "1995"}, {"messageId": "1990", "fix": "1998", "desc": "1992"}, {"messageId": "1993", "fix": "1999", "desc": "1995"}, {"messageId": "1990", "fix": "2000", "desc": "1992"}, {"messageId": "1993", "fix": "2001", "desc": "1995"}, {"messageId": "1990", "fix": "2002", "desc": "1992"}, {"messageId": "1993", "fix": "2003", "desc": "1995"}, {"messageId": "1990", "fix": "2004", "desc": "1992"}, {"messageId": "1993", "fix": "2005", "desc": "1995"}, {"messageId": "1990", "fix": "2006", "desc": "1992"}, {"messageId": "1993", "fix": "2007", "desc": "1995"}, {"messageId": "1990", "fix": "2008", "desc": "1992"}, {"messageId": "1993", "fix": "2009", "desc": "1995"}, {"messageId": "1990", "fix": "2010", "desc": "1992"}, {"messageId": "1993", "fix": "2011", "desc": "1995"}, {"messageId": "1990", "fix": "2012", "desc": "1992"}, {"messageId": "1993", "fix": "2013", "desc": "1995"}, {"messageId": "1990", "fix": "2014", "desc": "1992"}, {"messageId": "1993", "fix": "2015", "desc": "1995"}, {"messageId": "1990", "fix": "2016", "desc": "1992"}, {"messageId": "1993", "fix": "2017", "desc": "1995"}, {"messageId": "1990", "fix": "2018", "desc": "1992"}, {"messageId": "1993", "fix": "2019", "desc": "1995"}, {"messageId": "1990", "fix": "2020", "desc": "1992"}, {"messageId": "1993", "fix": "2021", "desc": "1995"}, {"messageId": "1990", "fix": "2022", "desc": "1992"}, {"messageId": "1993", "fix": "2023", "desc": "1995"}, {"messageId": "1990", "fix": "2024", "desc": "1992"}, {"messageId": "1993", "fix": "2025", "desc": "1995"}, {"messageId": "1990", "fix": "2026", "desc": "1992"}, {"messageId": "1993", "fix": "2027", "desc": "1995"}, {"messageId": "1990", "fix": "2028", "desc": "1992"}, {"messageId": "1993", "fix": "2029", "desc": "1995"}, {"desc": "2030", "fix": "2031"}, {"messageId": "1990", "fix": "2032", "desc": "1992"}, {"messageId": "1993", "fix": "2033", "desc": "1995"}, {"messageId": "1990", "fix": "2034", "desc": "1992"}, {"messageId": "1993", "fix": "2035", "desc": "1995"}, {"messageId": "1990", "fix": "2036", "desc": "1992"}, {"messageId": "1993", "fix": "2037", "desc": "1995"}, {"messageId": "1990", "fix": "2038", "desc": "1992"}, {"messageId": "1993", "fix": "2039", "desc": "1995"}, {"messageId": "1990", "fix": "2040", "desc": "1992"}, {"messageId": "1993", "fix": "2041", "desc": "1995"}, {"messageId": "1990", "fix": "2042", "desc": "1992"}, {"messageId": "1993", "fix": "2043", "desc": "1995"}, {"messageId": "1990", "fix": "2044", "desc": "1992"}, {"messageId": "1993", "fix": "2045", "desc": "1995"}, {"messageId": "1990", "fix": "2046", "desc": "1992"}, {"messageId": "1993", "fix": "2047", "desc": "1995"}, {"desc": "2030", "fix": "2048"}, {"messageId": "1990", "fix": "2049", "desc": "1992"}, {"messageId": "1993", "fix": "2050", "desc": "1995"}, {"messageId": "2051", "data": "2052", "fix": "2053", "desc": "2054"}, {"messageId": "2051", "data": "2055", "fix": "2056", "desc": "2057"}, {"messageId": "2051", "data": "2058", "fix": "2059", "desc": "2060"}, {"messageId": "2051", "data": "2061", "fix": "2062", "desc": "2063"}, {"messageId": "2051", "data": "2064", "fix": "2065", "desc": "2054"}, {"messageId": "2051", "data": "2066", "fix": "2067", "desc": "2057"}, {"messageId": "2051", "data": "2068", "fix": "2069", "desc": "2060"}, {"messageId": "2051", "data": "2070", "fix": "2071", "desc": "2063"}, {"messageId": "1990", "fix": "2072", "desc": "1992"}, {"messageId": "1993", "fix": "2073", "desc": "1995"}, {"messageId": "1990", "fix": "2074", "desc": "1992"}, {"messageId": "1993", "fix": "2075", "desc": "1995"}, {"messageId": "1990", "fix": "2076", "desc": "1992"}, {"messageId": "1993", "fix": "2077", "desc": "1995"}, {"messageId": "1990", "fix": "2078", "desc": "1992"}, {"messageId": "1993", "fix": "2079", "desc": "1995"}, {"messageId": "1990", "fix": "2080", "desc": "1992"}, {"messageId": "1993", "fix": "2081", "desc": "1995"}, {"messageId": "1990", "fix": "2082", "desc": "1992"}, {"messageId": "1993", "fix": "2083", "desc": "1995"}, {"messageId": "1990", "fix": "2084", "desc": "1992"}, {"messageId": "1993", "fix": "2085", "desc": "1995"}, {"messageId": "1990", "fix": "2086", "desc": "1992"}, {"messageId": "1993", "fix": "2087", "desc": "1995"}, {"messageId": "1990", "fix": "2088", "desc": "1992"}, {"messageId": "1993", "fix": "2089", "desc": "1995"}, {"messageId": "1990", "fix": "2090", "desc": "1992"}, {"messageId": "1993", "fix": "2091", "desc": "1995"}, {"messageId": "1990", "fix": "2092", "desc": "1992"}, {"messageId": "1993", "fix": "2093", "desc": "1995"}, {"messageId": "1990", "fix": "2094", "desc": "1992"}, {"messageId": "1993", "fix": "2095", "desc": "1995"}, {"messageId": "1990", "fix": "2096", "desc": "1992"}, {"messageId": "1993", "fix": "2097", "desc": "1995"}, {"messageId": "1990", "fix": "2098", "desc": "1992"}, {"messageId": "1993", "fix": "2099", "desc": "1995"}, {"messageId": "1990", "fix": "2100", "desc": "1992"}, {"messageId": "1993", "fix": "2101", "desc": "1995"}, {"messageId": "1990", "fix": "2102", "desc": "1992"}, {"messageId": "1993", "fix": "2103", "desc": "1995"}, {"messageId": "1990", "fix": "2104", "desc": "1992"}, {"messageId": "1993", "fix": "2105", "desc": "1995"}, {"messageId": "1990", "fix": "2106", "desc": "1992"}, {"messageId": "1993", "fix": "2107", "desc": "1995"}, {"messageId": "1990", "fix": "2108", "desc": "1992"}, {"messageId": "1993", "fix": "2109", "desc": "1995"}, {"messageId": "1990", "fix": "2110", "desc": "1992"}, {"messageId": "1993", "fix": "2111", "desc": "1995"}, {"messageId": "1990", "fix": "2112", "desc": "1992"}, {"messageId": "1993", "fix": "2113", "desc": "1995"}, {"messageId": "1990", "fix": "2114", "desc": "1992"}, {"messageId": "1993", "fix": "2115", "desc": "1995"}, {"messageId": "1990", "fix": "2116", "desc": "1992"}, {"messageId": "1993", "fix": "2117", "desc": "1995"}, {"messageId": "1990", "fix": "2118", "desc": "1992"}, {"messageId": "1993", "fix": "2119", "desc": "1995"}, {"messageId": "1990", "fix": "2120", "desc": "1992"}, {"messageId": "1993", "fix": "2121", "desc": "1995"}, {"messageId": "1990", "fix": "2122", "desc": "1992"}, {"messageId": "1993", "fix": "2123", "desc": "1995"}, {"messageId": "1990", "fix": "2124", "desc": "1992"}, {"messageId": "1993", "fix": "2125", "desc": "1995"}, {"messageId": "1990", "fix": "2126", "desc": "1992"}, {"messageId": "1993", "fix": "2127", "desc": "1995"}, {"messageId": "2051", "data": "2128", "fix": "2129", "desc": "2130"}, {"messageId": "2051", "data": "2131", "fix": "2132", "desc": "2133"}, {"messageId": "2051", "data": "2134", "fix": "2135", "desc": "2136"}, {"messageId": "2051", "data": "2137", "fix": "2138", "desc": "2139"}, {"messageId": "1990", "fix": "2140", "desc": "1992"}, {"messageId": "1993", "fix": "2141", "desc": "1995"}, {"messageId": "1990", "fix": "2142", "desc": "1992"}, {"messageId": "1993", "fix": "2143", "desc": "1995"}, {"messageId": "1990", "fix": "2144", "desc": "1992"}, {"messageId": "1993", "fix": "2145", "desc": "1995"}, {"messageId": "1990", "fix": "2146", "desc": "1992"}, {"messageId": "1993", "fix": "2147", "desc": "1995"}, {"messageId": "2051", "data": "2148", "fix": "2149", "desc": "2130"}, {"messageId": "2051", "data": "2150", "fix": "2151", "desc": "2133"}, {"messageId": "2051", "data": "2152", "fix": "2153", "desc": "2136"}, {"messageId": "2051", "data": "2154", "fix": "2155", "desc": "2139"}, {"messageId": "2051", "data": "2156", "fix": "2157", "desc": "2130"}, {"messageId": "2051", "data": "2158", "fix": "2159", "desc": "2133"}, {"messageId": "2051", "data": "2160", "fix": "2161", "desc": "2136"}, {"messageId": "2051", "data": "2162", "fix": "2163", "desc": "2139"}, {"messageId": "1990", "fix": "2164", "desc": "1992"}, {"messageId": "1993", "fix": "2165", "desc": "1995"}, {"messageId": "1990", "fix": "2166", "desc": "1992"}, {"messageId": "1993", "fix": "2167", "desc": "1995"}, {"messageId": "1990", "fix": "2168", "desc": "1992"}, {"messageId": "1993", "fix": "2169", "desc": "1995"}, {"messageId": "2051", "data": "2170", "fix": "2171", "desc": "2130"}, {"messageId": "2051", "data": "2172", "fix": "2173", "desc": "2133"}, {"messageId": "2051", "data": "2174", "fix": "2175", "desc": "2136"}, {"messageId": "2051", "data": "2176", "fix": "2177", "desc": "2139"}, {"messageId": "1990", "fix": "2178", "desc": "1992"}, {"messageId": "1993", "fix": "2179", "desc": "1995"}, {"desc": "2180", "fix": "2181"}, {"messageId": "1990", "fix": "2182", "desc": "1992"}, {"messageId": "1993", "fix": "2183", "desc": "1995"}, {"messageId": "1990", "fix": "2184", "desc": "1992"}, {"messageId": "1993", "fix": "2185", "desc": "1995"}, {"messageId": "1990", "fix": "2186", "desc": "1992"}, {"messageId": "1993", "fix": "2187", "desc": "1995"}, {"messageId": "1990", "fix": "2188", "desc": "1992"}, {"messageId": "1993", "fix": "2189", "desc": "1995"}, {"messageId": "1990", "fix": "2190", "desc": "1992"}, {"messageId": "1993", "fix": "2191", "desc": "1995"}, {"messageId": "1990", "fix": "2192", "desc": "1992"}, {"messageId": "1993", "fix": "2193", "desc": "1995"}, {"messageId": "2051", "data": "2194", "fix": "2195", "desc": "2130"}, {"messageId": "2051", "data": "2196", "fix": "2197", "desc": "2133"}, {"messageId": "2051", "data": "2198", "fix": "2199", "desc": "2136"}, {"messageId": "2051", "data": "2200", "fix": "2201", "desc": "2139"}, {"messageId": "1990", "fix": "2202", "desc": "1992"}, {"messageId": "1993", "fix": "2203", "desc": "1995"}, {"messageId": "2051", "data": "2204", "fix": "2205", "desc": "2130"}, {"messageId": "2051", "data": "2206", "fix": "2207", "desc": "2133"}, {"messageId": "2051", "data": "2208", "fix": "2209", "desc": "2136"}, {"messageId": "2051", "data": "2210", "fix": "2211", "desc": "2139"}, {"messageId": "1990", "fix": "2212", "desc": "1992"}, {"messageId": "1993", "fix": "2213", "desc": "1995"}, {"messageId": "1990", "fix": "2214", "desc": "1992"}, {"messageId": "1993", "fix": "2215", "desc": "1995"}, {"desc": "2216", "fix": "2217"}, {"messageId": "2218", "fix": "2219", "desc": "2220"}, {"messageId": "1990", "fix": "2221", "desc": "1992"}, {"messageId": "1993", "fix": "2222", "desc": "1995"}, {"messageId": "1990", "fix": "2223", "desc": "1992"}, {"messageId": "1993", "fix": "2224", "desc": "1995"}, {"messageId": "1990", "fix": "2225", "desc": "1992"}, {"messageId": "1993", "fix": "2226", "desc": "1995"}, {"messageId": "1990", "fix": "2227", "desc": "1992"}, {"messageId": "1993", "fix": "2228", "desc": "1995"}, {"messageId": "1990", "fix": "2229", "desc": "1992"}, {"messageId": "1993", "fix": "2230", "desc": "1995"}, {"messageId": "1990", "fix": "2231", "desc": "1992"}, {"messageId": "1993", "fix": "2232", "desc": "1995"}, {"messageId": "1990", "fix": "2233", "desc": "1992"}, {"messageId": "1993", "fix": "2234", "desc": "1995"}, {"messageId": "1990", "fix": "2235", "desc": "1992"}, {"messageId": "1993", "fix": "2236", "desc": "1995"}, {"messageId": "1990", "fix": "2237", "desc": "1992"}, {"messageId": "1993", "fix": "2238", "desc": "1995"}, {"messageId": "1990", "fix": "2239", "desc": "1992"}, {"messageId": "1993", "fix": "2240", "desc": "1995"}, {"messageId": "1990", "fix": "2241", "desc": "1992"}, {"messageId": "1993", "fix": "2242", "desc": "1995"}, {"desc": "2243", "fix": "2244"}, {"messageId": "1990", "fix": "2245", "desc": "1992"}, {"messageId": "1993", "fix": "2246", "desc": "1995"}, {"messageId": "1990", "fix": "2247", "desc": "1992"}, {"messageId": "1993", "fix": "2248", "desc": "1995"}, {"messageId": "1990", "fix": "2249", "desc": "1992"}, {"messageId": "1993", "fix": "2250", "desc": "1995"}, {"messageId": "1990", "fix": "2251", "desc": "1992"}, {"messageId": "1993", "fix": "2252", "desc": "1995"}, {"messageId": "1990", "fix": "2253", "desc": "1992"}, {"messageId": "1993", "fix": "2254", "desc": "1995"}, {"messageId": "1990", "fix": "2255", "desc": "1992"}, {"messageId": "1993", "fix": "2256", "desc": "1995"}, {"messageId": "1990", "fix": "2257", "desc": "1992"}, {"messageId": "1993", "fix": "2258", "desc": "1995"}, {"messageId": "1990", "fix": "2259", "desc": "1992"}, {"messageId": "1993", "fix": "2260", "desc": "1995"}, {"messageId": "1990", "fix": "2261", "desc": "1992"}, {"messageId": "1993", "fix": "2262", "desc": "1995"}, {"messageId": "1990", "fix": "2263", "desc": "1992"}, {"messageId": "1993", "fix": "2264", "desc": "1995"}, {"messageId": "1990", "fix": "2265", "desc": "1992"}, {"messageId": "1993", "fix": "2266", "desc": "1995"}, {"messageId": "1990", "fix": "2267", "desc": "1992"}, {"messageId": "1993", "fix": "2268", "desc": "1995"}, {"messageId": "1990", "fix": "2269", "desc": "1992"}, {"messageId": "1993", "fix": "2270", "desc": "1995"}, {"messageId": "1990", "fix": "2271", "desc": "1992"}, {"messageId": "1993", "fix": "2272", "desc": "1995"}, {"messageId": "1990", "fix": "2273", "desc": "1992"}, {"messageId": "1993", "fix": "2274", "desc": "1995"}, {"messageId": "1990", "fix": "2275", "desc": "1992"}, {"messageId": "1993", "fix": "2276", "desc": "1995"}, {"messageId": "1990", "fix": "2277", "desc": "1992"}, {"messageId": "1993", "fix": "2278", "desc": "1995"}, {"messageId": "1990", "fix": "2279", "desc": "1992"}, {"messageId": "1993", "fix": "2280", "desc": "1995"}, {"messageId": "1990", "fix": "2281", "desc": "1992"}, {"messageId": "1993", "fix": "2282", "desc": "1995"}, {"messageId": "1990", "fix": "2283", "desc": "1992"}, {"messageId": "1993", "fix": "2284", "desc": "1995"}, {"messageId": "1990", "fix": "2285", "desc": "1992"}, {"messageId": "1993", "fix": "2286", "desc": "1995"}, {"messageId": "1990", "fix": "2287", "desc": "1992"}, {"messageId": "1993", "fix": "2288", "desc": "1995"}, {"messageId": "1990", "fix": "2289", "desc": "1992"}, {"messageId": "1993", "fix": "2290", "desc": "1995"}, {"messageId": "1990", "fix": "2291", "desc": "1992"}, {"messageId": "1993", "fix": "2292", "desc": "1995"}, {"messageId": "1990", "fix": "2293", "desc": "1992"}, {"messageId": "1993", "fix": "2294", "desc": "1995"}, {"messageId": "1990", "fix": "2295", "desc": "1992"}, {"messageId": "1993", "fix": "2296", "desc": "1995"}, {"messageId": "1990", "fix": "2297", "desc": "1992"}, {"messageId": "1993", "fix": "2298", "desc": "1995"}, {"messageId": "1990", "fix": "2299", "desc": "1992"}, {"messageId": "1993", "fix": "2300", "desc": "1995"}, {"messageId": "1990", "fix": "2301", "desc": "1992"}, {"messageId": "1993", "fix": "2302", "desc": "1995"}, {"messageId": "1990", "fix": "2303", "desc": "1992"}, {"messageId": "1993", "fix": "2304", "desc": "1995"}, {"messageId": "1990", "fix": "2305", "desc": "1992"}, {"messageId": "1993", "fix": "2306", "desc": "1995"}, {"messageId": "1990", "fix": "2307", "desc": "1992"}, {"messageId": "1993", "fix": "2308", "desc": "1995"}, {"messageId": "1990", "fix": "2309", "desc": "1992"}, {"messageId": "1993", "fix": "2310", "desc": "1995"}, {"messageId": "1990", "fix": "2311", "desc": "1992"}, {"messageId": "1993", "fix": "2312", "desc": "1995"}, {"messageId": "1990", "fix": "2313", "desc": "1992"}, {"messageId": "1993", "fix": "2314", "desc": "1995"}, {"messageId": "1990", "fix": "2315", "desc": "1992"}, {"messageId": "1993", "fix": "2316", "desc": "1995"}, {"messageId": "1990", "fix": "2317", "desc": "1992"}, {"messageId": "1993", "fix": "2318", "desc": "1995"}, {"messageId": "1990", "fix": "2319", "desc": "1992"}, {"messageId": "1993", "fix": "2320", "desc": "1995"}, {"messageId": "1990", "fix": "2321", "desc": "1992"}, {"messageId": "1993", "fix": "2322", "desc": "1995"}, {"messageId": "1990", "fix": "2323", "desc": "1992"}, {"messageId": "1993", "fix": "2324", "desc": "1995"}, {"messageId": "1990", "fix": "2325", "desc": "1992"}, {"messageId": "1993", "fix": "2326", "desc": "1995"}, {"messageId": "1990", "fix": "2327", "desc": "1992"}, {"messageId": "1993", "fix": "2328", "desc": "1995"}, {"messageId": "1990", "fix": "2329", "desc": "1992"}, {"messageId": "1993", "fix": "2330", "desc": "1995"}, {"messageId": "1990", "fix": "2331", "desc": "1992"}, {"messageId": "1993", "fix": "2332", "desc": "1995"}, {"messageId": "1990", "fix": "2333", "desc": "1992"}, {"messageId": "1993", "fix": "2334", "desc": "1995"}, {"messageId": "1990", "fix": "2335", "desc": "1992"}, {"messageId": "1993", "fix": "2336", "desc": "1995"}, {"desc": "2337", "fix": "2338"}, {"messageId": "1990", "fix": "2339", "desc": "1992"}, {"messageId": "1993", "fix": "2340", "desc": "1995"}, {"messageId": "2051", "data": "2341", "fix": "2342", "desc": "2130"}, {"messageId": "2051", "data": "2343", "fix": "2344", "desc": "2133"}, {"messageId": "2051", "data": "2345", "fix": "2346", "desc": "2136"}, {"messageId": "2051", "data": "2347", "fix": "2348", "desc": "2139"}, {"messageId": "2051", "data": "2349", "fix": "2350", "desc": "2054"}, {"messageId": "2051", "data": "2351", "fix": "2352", "desc": "2057"}, {"messageId": "2051", "data": "2353", "fix": "2354", "desc": "2060"}, {"messageId": "2051", "data": "2355", "fix": "2356", "desc": "2063"}, {"messageId": "2051", "data": "2357", "fix": "2358", "desc": "2054"}, {"messageId": "2051", "data": "2359", "fix": "2360", "desc": "2057"}, {"messageId": "2051", "data": "2361", "fix": "2362", "desc": "2060"}, {"messageId": "2051", "data": "2363", "fix": "2364", "desc": "2063"}, {"messageId": "2051", "data": "2365", "fix": "2366", "desc": "2054"}, {"messageId": "2051", "data": "2367", "fix": "2368", "desc": "2057"}, {"messageId": "2051", "data": "2369", "fix": "2370", "desc": "2060"}, {"messageId": "2051", "data": "2371", "fix": "2372", "desc": "2063"}, {"messageId": "2051", "data": "2373", "fix": "2374", "desc": "2054"}, {"messageId": "2051", "data": "2375", "fix": "2376", "desc": "2057"}, {"messageId": "2051", "data": "2377", "fix": "2378", "desc": "2060"}, {"messageId": "2051", "data": "2379", "fix": "2380", "desc": "2063"}, {"messageId": "2051", "data": "2381", "fix": "2382", "desc": "2130"}, {"messageId": "2051", "data": "2383", "fix": "2384", "desc": "2133"}, {"messageId": "2051", "data": "2385", "fix": "2386", "desc": "2136"}, {"messageId": "2051", "data": "2387", "fix": "2388", "desc": "2139"}, {"messageId": "2051", "data": "2389", "fix": "2390", "desc": "2054"}, {"messageId": "2051", "data": "2391", "fix": "2392", "desc": "2057"}, {"messageId": "2051", "data": "2393", "fix": "2394", "desc": "2060"}, {"messageId": "2051", "data": "2395", "fix": "2396", "desc": "2063"}, {"messageId": "2051", "data": "2397", "fix": "2398", "desc": "2054"}, {"messageId": "2051", "data": "2399", "fix": "2400", "desc": "2057"}, {"messageId": "2051", "data": "2401", "fix": "2402", "desc": "2060"}, {"messageId": "2051", "data": "2403", "fix": "2404", "desc": "2063"}, {"messageId": "2051", "data": "2405", "fix": "2406", "desc": "2130"}, {"messageId": "2051", "data": "2407", "fix": "2408", "desc": "2133"}, {"messageId": "2051", "data": "2409", "fix": "2410", "desc": "2136"}, {"messageId": "2051", "data": "2411", "fix": "2412", "desc": "2139"}, {"messageId": "2051", "data": "2413", "fix": "2414", "desc": "2130"}, {"messageId": "2051", "data": "2415", "fix": "2416", "desc": "2133"}, {"messageId": "2051", "data": "2417", "fix": "2418", "desc": "2136"}, {"messageId": "2051", "data": "2419", "fix": "2420", "desc": "2139"}, {"messageId": "1990", "fix": "2421", "desc": "1992"}, {"messageId": "1993", "fix": "2422", "desc": "1995"}, {"messageId": "1990", "fix": "2423", "desc": "1992"}, {"messageId": "1993", "fix": "2424", "desc": "1995"}, {"messageId": "1990", "fix": "2425", "desc": "1992"}, {"messageId": "1993", "fix": "2426", "desc": "1995"}, {"messageId": "1990", "fix": "2427", "desc": "1992"}, {"messageId": "1993", "fix": "2428", "desc": "1995"}, {"messageId": "1990", "fix": "2429", "desc": "1992"}, {"messageId": "1993", "fix": "2430", "desc": "1995"}, {"messageId": "1990", "fix": "2431", "desc": "1992"}, {"messageId": "1993", "fix": "2432", "desc": "1995"}, {"messageId": "1990", "fix": "2433", "desc": "1992"}, {"messageId": "1993", "fix": "2434", "desc": "1995"}, {"messageId": "1990", "fix": "2435", "desc": "1992"}, {"messageId": "1993", "fix": "2436", "desc": "1995"}, {"messageId": "1990", "fix": "2437", "desc": "1992"}, {"messageId": "1993", "fix": "2438", "desc": "1995"}, {"messageId": "1990", "fix": "2439", "desc": "1992"}, {"messageId": "1993", "fix": "2440", "desc": "1995"}, {"messageId": "1990", "fix": "2441", "desc": "1992"}, {"messageId": "1993", "fix": "2442", "desc": "1995"}, {"messageId": "1990", "fix": "2443", "desc": "1992"}, {"messageId": "1993", "fix": "2444", "desc": "1995"}, {"messageId": "1990", "fix": "2445", "desc": "1992"}, {"messageId": "1993", "fix": "2446", "desc": "1995"}, "suggestUnknown", {"range": "2447", "text": "2448"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "2449", "text": "2450"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "2451", "text": "2448"}, {"range": "2452", "text": "2450"}, {"range": "2453", "text": "2448"}, {"range": "2454", "text": "2450"}, {"range": "2455", "text": "2448"}, {"range": "2456", "text": "2450"}, {"range": "2457", "text": "2448"}, {"range": "2458", "text": "2450"}, {"range": "2459", "text": "2448"}, {"range": "2460", "text": "2450"}, {"range": "2461", "text": "2448"}, {"range": "2462", "text": "2450"}, {"range": "2463", "text": "2448"}, {"range": "2464", "text": "2450"}, {"range": "2465", "text": "2448"}, {"range": "2466", "text": "2450"}, {"range": "2467", "text": "2448"}, {"range": "2468", "text": "2450"}, {"range": "2469", "text": "2448"}, {"range": "2470", "text": "2450"}, {"range": "2471", "text": "2448"}, {"range": "2472", "text": "2450"}, {"range": "2473", "text": "2448"}, {"range": "2474", "text": "2450"}, {"range": "2475", "text": "2448"}, {"range": "2476", "text": "2450"}, {"range": "2477", "text": "2448"}, {"range": "2478", "text": "2450"}, {"range": "2479", "text": "2448"}, {"range": "2480", "text": "2450"}, {"range": "2481", "text": "2448"}, {"range": "2482", "text": "2450"}, {"range": "2483", "text": "2448"}, {"range": "2484", "text": "2450"}, "Update the dependencies array to be: [fetchData, filters]", {"range": "2485", "text": "2486"}, {"range": "2487", "text": "2448"}, {"range": "2488", "text": "2450"}, {"range": "2489", "text": "2448"}, {"range": "2490", "text": "2450"}, {"range": "2491", "text": "2448"}, {"range": "2492", "text": "2450"}, {"range": "2493", "text": "2448"}, {"range": "2494", "text": "2450"}, {"range": "2495", "text": "2448"}, {"range": "2496", "text": "2450"}, {"range": "2497", "text": "2448"}, {"range": "2498", "text": "2450"}, {"range": "2499", "text": "2448"}, {"range": "2500", "text": "2450"}, {"range": "2501", "text": "2448"}, {"range": "2502", "text": "2450"}, {"range": "2503", "text": "2486"}, {"range": "2504", "text": "2448"}, {"range": "2505", "text": "2450"}, "replaceWithAlt", {"alt": "2506"}, {"range": "2507", "text": "2508"}, "Replace with `&quot;`.", {"alt": "2509"}, {"range": "2510", "text": "2511"}, "Replace with `&ldquo;`.", {"alt": "2512"}, {"range": "2513", "text": "2514"}, "Replace with `&#34;`.", {"alt": "2515"}, {"range": "2516", "text": "2517"}, "Replace with `&rdquo;`.", {"alt": "2506"}, {"range": "2518", "text": "2519"}, {"alt": "2509"}, {"range": "2520", "text": "2521"}, {"alt": "2512"}, {"range": "2522", "text": "2523"}, {"alt": "2515"}, {"range": "2524", "text": "2525"}, {"range": "2526", "text": "2448"}, {"range": "2527", "text": "2450"}, {"range": "2528", "text": "2448"}, {"range": "2529", "text": "2450"}, {"range": "2530", "text": "2448"}, {"range": "2531", "text": "2450"}, {"range": "2532", "text": "2448"}, {"range": "2533", "text": "2450"}, {"range": "2534", "text": "2448"}, {"range": "2535", "text": "2450"}, {"range": "2536", "text": "2448"}, {"range": "2537", "text": "2450"}, {"range": "2538", "text": "2448"}, {"range": "2539", "text": "2450"}, {"range": "2540", "text": "2448"}, {"range": "2541", "text": "2450"}, {"range": "2542", "text": "2448"}, {"range": "2543", "text": "2450"}, {"range": "2544", "text": "2448"}, {"range": "2545", "text": "2450"}, {"range": "2546", "text": "2448"}, {"range": "2547", "text": "2450"}, {"range": "2548", "text": "2448"}, {"range": "2549", "text": "2450"}, {"range": "2550", "text": "2448"}, {"range": "2551", "text": "2450"}, {"range": "2552", "text": "2448"}, {"range": "2553", "text": "2450"}, {"range": "2554", "text": "2448"}, {"range": "2555", "text": "2450"}, {"range": "2556", "text": "2448"}, {"range": "2557", "text": "2450"}, {"range": "2558", "text": "2448"}, {"range": "2559", "text": "2450"}, {"range": "2560", "text": "2448"}, {"range": "2561", "text": "2450"}, {"range": "2562", "text": "2448"}, {"range": "2563", "text": "2450"}, {"range": "2564", "text": "2448"}, {"range": "2565", "text": "2450"}, {"range": "2566", "text": "2448"}, {"range": "2567", "text": "2450"}, {"range": "2568", "text": "2448"}, {"range": "2569", "text": "2450"}, {"range": "2570", "text": "2448"}, {"range": "2571", "text": "2450"}, {"range": "2572", "text": "2448"}, {"range": "2573", "text": "2450"}, {"range": "2574", "text": "2448"}, {"range": "2575", "text": "2450"}, {"range": "2576", "text": "2448"}, {"range": "2577", "text": "2450"}, {"range": "2578", "text": "2448"}, {"range": "2579", "text": "2450"}, {"range": "2580", "text": "2448"}, {"range": "2581", "text": "2450"}, {"alt": "2582"}, {"range": "2583", "text": "2584"}, "Replace with `&apos;`.", {"alt": "2585"}, {"range": "2586", "text": "2587"}, "Replace with `&lsquo;`.", {"alt": "2588"}, {"range": "2589", "text": "2590"}, "Replace with `&#39;`.", {"alt": "2591"}, {"range": "2592", "text": "2593"}, "Replace with `&rsquo;`.", {"range": "2594", "text": "2448"}, {"range": "2595", "text": "2450"}, {"range": "2596", "text": "2448"}, {"range": "2597", "text": "2450"}, {"range": "2598", "text": "2448"}, {"range": "2599", "text": "2450"}, {"range": "2600", "text": "2448"}, {"range": "2601", "text": "2450"}, {"alt": "2582"}, {"range": "2602", "text": "2603"}, {"alt": "2585"}, {"range": "2604", "text": "2605"}, {"alt": "2588"}, {"range": "2606", "text": "2607"}, {"alt": "2591"}, {"range": "2608", "text": "2609"}, {"alt": "2582"}, {"range": "2610", "text": "2603"}, {"alt": "2585"}, {"range": "2611", "text": "2605"}, {"alt": "2588"}, {"range": "2612", "text": "2607"}, {"alt": "2591"}, {"range": "2613", "text": "2609"}, {"range": "2614", "text": "2448"}, {"range": "2615", "text": "2450"}, {"range": "2616", "text": "2448"}, {"range": "2617", "text": "2450"}, {"range": "2618", "text": "2448"}, {"range": "2619", "text": "2450"}, {"alt": "2582"}, {"range": "2620", "text": "2621"}, {"alt": "2585"}, {"range": "2622", "text": "2623"}, {"alt": "2588"}, {"range": "2624", "text": "2625"}, {"alt": "2591"}, {"range": "2626", "text": "2627"}, {"range": "2628", "text": "2448"}, {"range": "2629", "text": "2450"}, "Update the dependencies array to be: [setSections, setLoading, setError, projectId]", {"range": "2630", "text": "2631"}, {"range": "2632", "text": "2448"}, {"range": "2633", "text": "2450"}, {"range": "2634", "text": "2448"}, {"range": "2635", "text": "2450"}, {"range": "2636", "text": "2448"}, {"range": "2637", "text": "2450"}, {"range": "2638", "text": "2448"}, {"range": "2639", "text": "2450"}, {"range": "2640", "text": "2448"}, {"range": "2641", "text": "2450"}, {"range": "2642", "text": "2448"}, {"range": "2643", "text": "2450"}, {"alt": "2582"}, {"range": "2644", "text": "2645"}, {"alt": "2585"}, {"range": "2646", "text": "2647"}, {"alt": "2588"}, {"range": "2648", "text": "2649"}, {"alt": "2591"}, {"range": "2650", "text": "2651"}, {"range": "2652", "text": "2448"}, {"range": "2653", "text": "2450"}, {"alt": "2582"}, {"range": "2654", "text": "2655"}, {"alt": "2585"}, {"range": "2656", "text": "2657"}, {"alt": "2588"}, {"range": "2658", "text": "2659"}, {"alt": "2591"}, {"range": "2660", "text": "2661"}, {"range": "2662", "text": "2448"}, {"range": "2663", "text": "2450"}, {"range": "2664", "text": "2448"}, {"range": "2665", "text": "2450"}, "Update the dependencies array to be: [isSignedIn, user, userLoaded, isAuthenticated, actions, getToken, identifyUser]", {"range": "2666", "text": "2667"}, "replaceEmptyInterfaceWithSuper", {"range": "2668", "text": "2669"}, "Replace empty interface with a type alias.", {"range": "2670", "text": "2448"}, {"range": "2671", "text": "2450"}, {"range": "2672", "text": "2448"}, {"range": "2673", "text": "2450"}, {"range": "2674", "text": "2448"}, {"range": "2675", "text": "2450"}, {"range": "2676", "text": "2448"}, {"range": "2677", "text": "2450"}, {"range": "2678", "text": "2448"}, {"range": "2679", "text": "2450"}, {"range": "2680", "text": "2448"}, {"range": "2681", "text": "2450"}, {"range": "2682", "text": "2448"}, {"range": "2683", "text": "2450"}, {"range": "2684", "text": "2448"}, {"range": "2685", "text": "2450"}, {"range": "2686", "text": "2448"}, {"range": "2687", "text": "2450"}, {"range": "2688", "text": "2448"}, {"range": "2689", "text": "2450"}, {"range": "2690", "text": "2448"}, {"range": "2691", "text": "2450"}, "Update the dependencies array to be: [isLoaded, user, isUserSynced, isSyncing, syncUserToBackend]", {"range": "2692", "text": "2693"}, {"range": "2694", "text": "2448"}, {"range": "2695", "text": "2450"}, {"range": "2696", "text": "2448"}, {"range": "2697", "text": "2450"}, {"range": "2698", "text": "2448"}, {"range": "2699", "text": "2450"}, {"range": "2700", "text": "2448"}, {"range": "2701", "text": "2450"}, {"range": "2702", "text": "2448"}, {"range": "2703", "text": "2450"}, {"range": "2704", "text": "2448"}, {"range": "2705", "text": "2450"}, {"range": "2706", "text": "2448"}, {"range": "2707", "text": "2450"}, {"range": "2708", "text": "2448"}, {"range": "2709", "text": "2450"}, {"range": "2710", "text": "2448"}, {"range": "2711", "text": "2450"}, {"range": "2712", "text": "2448"}, {"range": "2713", "text": "2450"}, {"range": "2714", "text": "2448"}, {"range": "2715", "text": "2450"}, {"range": "2716", "text": "2448"}, {"range": "2717", "text": "2450"}, {"range": "2718", "text": "2448"}, {"range": "2719", "text": "2450"}, {"range": "2720", "text": "2448"}, {"range": "2721", "text": "2450"}, {"range": "2722", "text": "2448"}, {"range": "2723", "text": "2450"}, {"range": "2724", "text": "2448"}, {"range": "2725", "text": "2450"}, {"range": "2726", "text": "2448"}, {"range": "2727", "text": "2450"}, {"range": "2728", "text": "2448"}, {"range": "2729", "text": "2450"}, {"range": "2730", "text": "2448"}, {"range": "2731", "text": "2450"}, {"range": "2732", "text": "2448"}, {"range": "2733", "text": "2450"}, {"range": "2734", "text": "2448"}, {"range": "2735", "text": "2450"}, {"range": "2736", "text": "2448"}, {"range": "2737", "text": "2450"}, {"range": "2738", "text": "2448"}, {"range": "2739", "text": "2450"}, {"range": "2740", "text": "2448"}, {"range": "2741", "text": "2450"}, {"range": "2742", "text": "2448"}, {"range": "2743", "text": "2450"}, {"range": "2744", "text": "2448"}, {"range": "2745", "text": "2450"}, {"range": "2746", "text": "2448"}, {"range": "2747", "text": "2450"}, {"range": "2748", "text": "2448"}, {"range": "2749", "text": "2450"}, {"range": "2750", "text": "2448"}, {"range": "2751", "text": "2450"}, {"range": "2752", "text": "2448"}, {"range": "2753", "text": "2450"}, {"range": "2754", "text": "2448"}, {"range": "2755", "text": "2450"}, {"range": "2756", "text": "2448"}, {"range": "2757", "text": "2450"}, {"range": "2758", "text": "2448"}, {"range": "2759", "text": "2450"}, {"range": "2760", "text": "2448"}, {"range": "2761", "text": "2450"}, {"range": "2762", "text": "2448"}, {"range": "2763", "text": "2450"}, {"range": "2764", "text": "2448"}, {"range": "2765", "text": "2450"}, {"range": "2766", "text": "2448"}, {"range": "2767", "text": "2450"}, {"range": "2768", "text": "2448"}, {"range": "2769", "text": "2450"}, {"range": "2770", "text": "2448"}, {"range": "2771", "text": "2450"}, {"range": "2772", "text": "2448"}, {"range": "2773", "text": "2450"}, {"range": "2774", "text": "2448"}, {"range": "2775", "text": "2450"}, {"range": "2776", "text": "2448"}, {"range": "2777", "text": "2450"}, {"range": "2778", "text": "2448"}, {"range": "2779", "text": "2450"}, {"range": "2780", "text": "2448"}, {"range": "2781", "text": "2450"}, {"range": "2782", "text": "2448"}, {"range": "2783", "text": "2450"}, {"range": "2784", "text": "2448"}, {"range": "2785", "text": "2450"}, "Update the dependencies array to be: [fetchTokens, isSignedIn]", {"range": "2786", "text": "2787"}, {"range": "2788", "text": "2448"}, {"range": "2789", "text": "2450"}, {"alt": "2582"}, {"range": "2790", "text": "2791"}, {"alt": "2585"}, {"range": "2792", "text": "2793"}, {"alt": "2588"}, {"range": "2794", "text": "2795"}, {"alt": "2591"}, {"range": "2796", "text": "2797"}, {"alt": "2506"}, {"range": "2798", "text": "2799"}, {"alt": "2509"}, {"range": "2800", "text": "2801"}, {"alt": "2512"}, {"range": "2802", "text": "2803"}, {"alt": "2515"}, {"range": "2804", "text": "2805"}, {"alt": "2506"}, {"range": "2806", "text": "2807"}, {"alt": "2509"}, {"range": "2808", "text": "2809"}, {"alt": "2512"}, {"range": "2810", "text": "2811"}, {"alt": "2515"}, {"range": "2812", "text": "2813"}, {"alt": "2506"}, {"range": "2814", "text": "2815"}, {"alt": "2509"}, {"range": "2816", "text": "2817"}, {"alt": "2512"}, {"range": "2818", "text": "2819"}, {"alt": "2515"}, {"range": "2820", "text": "2821"}, {"alt": "2506"}, {"range": "2822", "text": "2823"}, {"alt": "2509"}, {"range": "2824", "text": "2825"}, {"alt": "2512"}, {"range": "2826", "text": "2827"}, {"alt": "2515"}, {"range": "2828", "text": "2829"}, {"alt": "2582"}, {"range": "2830", "text": "2831"}, {"alt": "2585"}, {"range": "2832", "text": "2833"}, {"alt": "2588"}, {"range": "2834", "text": "2835"}, {"alt": "2591"}, {"range": "2836", "text": "2837"}, {"alt": "2506"}, {"range": "2838", "text": "2839"}, {"alt": "2509"}, {"range": "2840", "text": "2841"}, {"alt": "2512"}, {"range": "2842", "text": "2843"}, {"alt": "2515"}, {"range": "2844", "text": "2845"}, {"alt": "2506"}, {"range": "2846", "text": "2847"}, {"alt": "2509"}, {"range": "2848", "text": "2849"}, {"alt": "2512"}, {"range": "2850", "text": "2851"}, {"alt": "2515"}, {"range": "2852", "text": "2853"}, {"alt": "2582"}, {"range": "2854", "text": "2855"}, {"alt": "2585"}, {"range": "2856", "text": "2857"}, {"alt": "2588"}, {"range": "2858", "text": "2859"}, {"alt": "2591"}, {"range": "2860", "text": "2861"}, {"alt": "2582"}, {"range": "2862", "text": "2863"}, {"alt": "2585"}, {"range": "2864", "text": "2865"}, {"alt": "2588"}, {"range": "2866", "text": "2867"}, {"alt": "2591"}, {"range": "2868", "text": "2869"}, {"range": "2870", "text": "2448"}, {"range": "2871", "text": "2450"}, {"range": "2872", "text": "2448"}, {"range": "2873", "text": "2450"}, {"range": "2874", "text": "2448"}, {"range": "2875", "text": "2450"}, {"range": "2876", "text": "2448"}, {"range": "2877", "text": "2450"}, {"range": "2878", "text": "2448"}, {"range": "2879", "text": "2450"}, {"range": "2880", "text": "2448"}, {"range": "2881", "text": "2450"}, {"range": "2882", "text": "2448"}, {"range": "2883", "text": "2450"}, {"range": "2884", "text": "2448"}, {"range": "2885", "text": "2450"}, {"range": "2886", "text": "2448"}, {"range": "2887", "text": "2450"}, {"range": "2888", "text": "2448"}, {"range": "2889", "text": "2450"}, {"range": "2890", "text": "2448"}, {"range": "2891", "text": "2450"}, {"range": "2892", "text": "2448"}, {"range": "2893", "text": "2450"}, {"range": "2894", "text": "2448"}, {"range": "2895", "text": "2450"}, [757, 760], "unknown", [757, 760], "never", [1429, 1432], [1429, 1432], [7584, 7587], [7584, 7587], [8541, 8544], [8541, 8544], [9268, 9271], [9268, 9271], [10164, 10167], [10164, 10167], [667, 670], [667, 670], [1339, 1342], [1339, 1342], [787, 790], [787, 790], [1458, 1461], [1458, 1461], [3011, 3014], [3011, 3014], [6282, 6285], [6282, 6285], [7885, 7888], [7885, 7888], [8825, 8828], [8825, 8828], [10392, 10395], [10392, 10395], [908, 911], [908, 911], [1723, 1726], [1723, 1726], [2493, 2496], [2493, 2496], [2624, 2633], "[fetchData, filters]", [2990, 2993], [2990, 2993], [5914, 5917], [5914, 5917], [6558, 6561], [6558, 6561], [7325, 7328], [7325, 7328], [14011, 14014], [14011, 14014], [824, 827], [824, 827], [1532, 1535], [1532, 1535], [2119, 2122], [2119, 2122], [2250, 2259], [8196, 8199], [8196, 8199], "&quot;", [11005, 11039], "\n                                &quot;", "&ldquo;", [11005, 11039], "\n                                &ldquo;", "&#34;", [11005, 11039], "\n                                &#34;", "&rdquo;", [11005, 11039], "\n                                &rdquo;", [11061, 11093], "&quot;\n                              ", [11061, 11093], "&ldquo;\n                              ", [11061, 11093], "&#34;\n                              ", [11061, 11093], "&rdquo;\n                              ", [1043, 1046], [1043, 1046], [4734, 4737], [4734, 4737], [7032, 7035], [7032, 7035], [8375, 8378], [8375, 8378], [8548, 8551], [8548, 8551], [10055, 10058], [10055, 10058], [11830, 11833], [11830, 11833], [14018, 14021], [14018, 14021], [14152, 14155], [14152, 14155], [1915, 1918], [1915, 1918], [2273, 2276], [2273, 2276], [4152, 4155], [4152, 4155], [4552, 4555], [4552, 4555], [1646, 1649], [1646, 1649], [688, 691], [688, 691], [1356, 1359], [1356, 1359], [2902, 2905], [2902, 2905], [3102, 3105], [3102, 3105], [12035, 12038], [12035, 12038], [3881, 3884], [3881, 3884], [5485, 5488], [5485, 5488], [11251, 11254], [11251, 11254], [2164, 2167], [2164, 2167], [2540, 2543], [2540, 2543], [981, 984], [981, 984], [1180, 1183], [1180, 1183], [2540, 2543], [2540, 2543], [4125, 4128], [4125, 4128], "&apos;", [5327, 5362], "\n            Don&apos;t have an account?", "&lsquo;", [5327, 5362], "\n            Don&lsquo;t have an account?", "&#39;", [5327, 5362], "\n            Don&#39;t have an account?", "&rsquo;", [5327, 5362], "\n            Don&rsquo;t have an account?", [2513, 2516], [2513, 2516], [3174, 3177], [3174, 3177], [4650, 4653], [4650, 4653], [7211, 7214], [7211, 7214], [5042, 5079], "\n            Didn&apos;t receive the code?", [5042, 5079], "\n            Didn&lsquo;t receive the code?", [5042, 5079], "\n            Didn&#39;t receive the code?", [5042, 5079], "\n            Didn&rsquo;t receive the code?", [5031, 5068], [5031, 5068], [5031, 5068], [5031, 5068], [3448, 3451], [3448, 3451], [2629, 2632], [2629, 2632], [3061, 3064], [3061, 3064], [4344, 4418], "\n              You don&apos;t have permission to edit this project\n            ", [4344, 4418], "\n              You don&lsquo;t have permission to edit this project\n            ", [4344, 4418], "\n              You don&#39;t have permission to edit this project\n            ", [4344, 4418], "\n              You don&rsquo;t have permission to edit this project\n            ", [7473, 7476], [7473, 7476], [2831, 2866], "[setSections, setLoading, setError, projectId]", [2966, 2969], [2966, 2969], [1700, 1703], [1700, 1703], [1763, 1766], [1763, 1766], [2846, 2849], [2846, 2849], [3477, 3480], [3477, 3480], [4021, 4024], [4021, 4024], [7531, 7628], "\n                    Get notified when projects you&apos;re working on are updated.\n                  ", [7531, 7628], "\n                    Get notified when projects you&lsquo;re working on are updated.\n                  ", [7531, 7628], "\n                    Get notified when projects you&#39;re working on are updated.\n                  ", [7531, 7628], "\n                    Get notified when projects you&rsquo;re working on are updated.\n                  ", [678, 681], [678, 681], [2687, 2801], "\n            Powerful features designed to streamline your workflow and boost your team&apos;s productivity.\n          ", [2687, 2801], "\n            Powerful features designed to streamline your workflow and boost your team&lsquo;s productivity.\n          ", [2687, 2801], "\n            Powerful features designed to streamline your workflow and boost your team&#39;s productivity.\n          ", [2687, 2801], "\n            Powerful features designed to streamline your workflow and boost your team&rsquo;s productivity.\n          ", [962, 965], [962, 965], [1196, 1199], [1196, 1199], [4255, 4321], "[isSignedIn, user, userLoaded, isAuthenticated, actions, getToken, identifyUser]", [75, 161], "type TextareaProps = React.TextareaHTMLAttributes<HTMLTextAreaElement>", [3481, 3484], [3481, 3484], [5157, 5160], [5157, 5160], [643, 646], [643, 646], [1025, 1028], [1025, 1028], [3959, 3962], [3959, 3962], [6186, 6189], [6186, 6189], [7912, 7915], [7912, 7915], [8987, 8990], [8987, 8990], [2087, 2090], [2087, 2090], [1342, 1345], [1342, 1345], [365, 368], [365, 368], [2370, 2411], "[isLoaded, user, isUserSynced, isSyncing, syncUserToBackend]", [7878, 7881], [7878, 7881], [8011, 8014], [8011, 8014], [8445, 8448], [8445, 8448], [9420, 9423], [9420, 9423], [476, 479], [476, 479], [736, 739], [736, 739], [1094, 1097], [1094, 1097], [3178, 3181], [3178, 3181], [4313, 4316], [4313, 4316], [4494, 4497], [4494, 4497], [4533, 4536], [4533, 4536], [4772, 4775], [4772, 4775], [4811, 4814], [4811, 4814], [5053, 5056], [5053, 5056], [5092, 5095], [5092, 5095], [5338, 5341], [5338, 5341], [5523, 5526], [5523, 5526], [5864, 5867], [5864, 5867], [1897, 1900], [1897, 1900], [3169, 3172], [3169, 3172], [3796, 3799], [3796, 3799], [5866, 5869], [5866, 5869], [321, 324], [321, 324], [894, 897], [894, 897], [1625, 1628], [1625, 1628], [1696, 1699], [1696, 1699], [4222, 4225], [4222, 4225], [4679, 4682], [4679, 4682], [810, 813], [810, 813], [1325, 1328], [1325, 1328], [1491, 1494], [1491, 1494], [1631, 1634], [1631, 1634], [1933, 1936], [1933, 1936], [2473, 2476], [2473, 2476], [2234, 2237], [2234, 2237], [90, 93], [90, 93], [112, 115], [112, 115], [2453, 2456], [2453, 2456], [2656, 2659], [2656, 2659], [2821, 2824], [2821, 2824], [3283, 3286], [3283, 3286], [3355, 3358], [3355, 3358], [595, 598], [595, 598], [3995, 3998], [3995, 3998], [12224, 12227], [12224, 12227], [13940, 13943], [13940, 13943], [2685, 2697], "[fetchTokens, isSignedIn]", [3840, 3843], [3840, 3843], [4692, 4803], "\n              Check your browser&apos;s Network tab or PostHog dashboard to verify the event was sent.\n            ", [4692, 4803], "\n              Check your browser&lsquo;s Network tab or PostHog dashboard to verify the event was sent.\n            ", [4692, 4803], "\n              Check your browser&#39;s Network tab or PostHog dashboard to verify the event was sent.\n            ", [4692, 4803], "\n              Check your browser&rsquo;s Network tab or PostHog dashboard to verify the event was sent.\n            ", [5139, 5172], "3. Filter by &quot;posthog\" or \"batch\"", [5139, 5172], "3. Filter by &ldquo;posthog\" or \"batch\"", [5139, 5172], "3. Filter by &#34;posthog\" or \"batch\"", [5139, 5172], "3. Filter by &rdquo;posthog\" or \"batch\"", [5139, 5172], "3. Filter by \"posthog&quot; or \"batch\"", [5139, 5172], "3. Filter by \"posthog&ldquo; or \"batch\"", [5139, 5172], "3. Filter by \"posthog&#34; or \"batch\"", [5139, 5172], "3. Filter by \"posthog&rdquo; or \"batch\"", [5139, 5172], "3. Filter by \"posthog\" or &quot;batch\"", [5139, 5172], "3. Filter by \"posthog\" or &ldquo;batch\"", [5139, 5172], "3. Filter by \"posthog\" or &#34;batch\"", [5139, 5172], "3. Filter by \"posthog\" or &rdquo;batch\"", [5139, 5172], "3. Filter by \"posthog\" or \"batch&quot;", [5139, 5172], "3. Filter by \"posthog\" or \"batch&ldquo;", [5139, 5172], "3. Filter by \"posthog\" or \"batch&#34;", [5139, 5172], "3. Filter by \"posthog\" or \"batch&rdquo;", [4624, 4893], "\n                  We&apos;re old enough to remember when the term \"lifestyle business\" was a dirty word, but as the dust of SaaS-era gluttony settles, \n                  we see that sustainable, attractive opportunities exist - they are not too small or too few & they are ", [4624, 4893], "\n                  We&lsquo;re old enough to remember when the term \"lifestyle business\" was a dirty word, but as the dust of SaaS-era gluttony settles, \n                  we see that sustainable, attractive opportunities exist - they are not too small or too few & they are ", [4624, 4893], "\n                  We&#39;re old enough to remember when the term \"lifestyle business\" was a dirty word, but as the dust of SaaS-era gluttony settles, \n                  we see that sustainable, attractive opportunities exist - they are not too small or too few & they are ", [4624, 4893], "\n                  We&rsquo;re old enough to remember when the term \"lifestyle business\" was a dirty word, but as the dust of SaaS-era gluttony settles, \n                  we see that sustainable, attractive opportunities exist - they are not too small or too few & they are ", [4624, 4893], "\n                  We're old enough to remember when the term &quot;lifestyle business\" was a dirty word, but as the dust of SaaS-era gluttony settles, \n                  we see that sustainable, attractive opportunities exist - they are not too small or too few & they are ", [4624, 4893], "\n                  We're old enough to remember when the term &ldquo;lifestyle business\" was a dirty word, but as the dust of SaaS-era gluttony settles, \n                  we see that sustainable, attractive opportunities exist - they are not too small or too few & they are ", [4624, 4893], "\n                  We're old enough to remember when the term &#34;lifestyle business\" was a dirty word, but as the dust of SaaS-era gluttony settles, \n                  we see that sustainable, attractive opportunities exist - they are not too small or too few & they are ", [4624, 4893], "\n                  We're old enough to remember when the term &rdquo;lifestyle business\" was a dirty word, but as the dust of SaaS-era gluttony settles, \n                  we see that sustainable, attractive opportunities exist - they are not too small or too few & they are ", [4624, 4893], "\n                  We're old enough to remember when the term \"lifestyle business&quot; was a dirty word, but as the dust of SaaS-era gluttony settles, \n                  we see that sustainable, attractive opportunities exist - they are not too small or too few & they are ", [4624, 4893], "\n                  We're old enough to remember when the term \"lifestyle business&ldquo; was a dirty word, but as the dust of SaaS-era gluttony settles, \n                  we see that sustainable, attractive opportunities exist - they are not too small or too few & they are ", [4624, 4893], "\n                  We're old enough to remember when the term \"lifestyle business&#34; was a dirty word, but as the dust of SaaS-era gluttony settles, \n                  we see that sustainable, attractive opportunities exist - they are not too small or too few & they are ", [4624, 4893], "\n                  We're old enough to remember when the term \"lifestyle business&rdquo; was a dirty word, but as the dust of SaaS-era gluttony settles, \n                  we see that sustainable, attractive opportunities exist - they are not too small or too few & they are ", [950, 1048], "\n              The blog post you&apos;re looking for doesn't exist or may have been moved.\n            ", [950, 1048], "\n              The blog post you&lsquo;re looking for doesn't exist or may have been moved.\n            ", [950, 1048], "\n              The blog post you&#39;re looking for doesn't exist or may have been moved.\n            ", [950, 1048], "\n              The blog post you&rsquo;re looking for doesn't exist or may have been moved.\n            ", [950, 1048], "\n              The blog post you're looking for doesn&apos;t exist or may have been moved.\n            ", [950, 1048], "\n              The blog post you're looking for doesn&lsquo;t exist or may have been moved.\n            ", [950, 1048], "\n              The blog post you're looking for doesn&#39;t exist or may have been moved.\n            ", [950, 1048], "\n              The blog post you're looking for doesn&rsquo;t exist or may have been moved.\n            ", [330, 333], [330, 333], [354, 357], [354, 357], [542, 545], [542, 545], [661, 664], [661, 664], [527, 530], [527, 530], [551, 554], [551, 554], [574, 577], [574, 577], [594, 597], [594, 597], [613, 616], [613, 616], [696, 699], [696, 699], [232, 235], [232, 235], [5163, 5166], [5163, 5166], [5867, 5870], [5867, 5870]]