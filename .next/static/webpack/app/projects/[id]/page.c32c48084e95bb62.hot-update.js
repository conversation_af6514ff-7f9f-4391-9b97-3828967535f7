"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/projects/[id]/page",{

/***/ "(app-pages-browser)/./src/components/business-item-hybrid-view.tsx":
/*!******************************************************!*\
  !*** ./src/components/business-item-hybrid-view.tsx ***!
  \******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BusinessItemHybridView: () => (/* binding */ BusinessItemHybridView)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_collapsible__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/collapsible */ \"(app-pages-browser)/./src/components/ui/collapsible.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./src/components/ui/separator.tsx\");\n/* harmony import */ var _components_ui_tooltip__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/tooltip */ \"(app-pages-browser)/./src/components/ui/tooltip.tsx\");\n/* harmony import */ var _data_businessItemsData__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/data/businessItemsData */ \"(app-pages-browser)/./src/data/businessItemsData.ts\");\n/* harmony import */ var _lib_dependencyManager__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/dependencyManager */ \"(app-pages-browser)/./src/lib/dependencyManager.ts\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_ChevronDown_ChevronUp_Clock_HelpCircle_Info_Lightbulb_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,ChevronDown,ChevronUp,Clock,HelpCircle,Info,Lightbulb,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_ChevronDown_ChevronUp_Clock_HelpCircle_Info_Lightbulb_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,ChevronDown,ChevronUp,Clock,HelpCircle,Info,Lightbulb,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_ChevronDown_ChevronUp_Clock_HelpCircle_Info_Lightbulb_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,ChevronDown,ChevronUp,Clock,HelpCircle,Info,Lightbulb,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/lightbulb.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_ChevronDown_ChevronUp_Clock_HelpCircle_Info_Lightbulb_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,ChevronDown,ChevronUp,Clock,HelpCircle,Info,Lightbulb,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_ChevronDown_ChevronUp_Clock_HelpCircle_Info_Lightbulb_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,ChevronDown,ChevronUp,Clock,HelpCircle,Info,Lightbulb,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_ChevronDown_ChevronUp_Clock_HelpCircle_Info_Lightbulb_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,ChevronDown,ChevronUp,Clock,HelpCircle,Info,Lightbulb,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-question-mark.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_ChevronDown_ChevronUp_Clock_HelpCircle_Info_Lightbulb_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,ChevronDown,ChevronUp,Clock,HelpCircle,Info,Lightbulb,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-up.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_ChevronDown_ChevronUp_Clock_HelpCircle_Info_Lightbulb_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,ChevronDown,ChevronUp,Clock,HelpCircle,Info,Lightbulb,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_ChevronDown_ChevronUp_Clock_HelpCircle_Info_Lightbulb_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,ChevronDown,ChevronUp,Clock,HelpCircle,Info,Lightbulb,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_ChevronDown_ChevronUp_Clock_HelpCircle_Info_Lightbulb_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,ChevronDown,ChevronUp,Clock,HelpCircle,Info,Lightbulb,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_9__);\n/* harmony import */ var _business_item_table__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./business-item-table */ \"(app-pages-browser)/./src/components/business-item-table.tsx\");\n/* __next_internal_client_entry_do_not_use__ BusinessItemHybridView auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nfunction BusinessItemHybridView(param) {\n    let { selectedItem, itemDetails, onBackToItems } = param;\n    _s();\n    const [isInfoExpanded, setIsInfoExpanded] = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)(false);\n    // Get comprehensive data for the selected item\n    const itemData = _data_businessItemsData__WEBPACK_IMPORTED_MODULE_7__.businessItemsData.find((item)=>item.id === selectedItem.id);\n    const dependencyCheck = selectedItem.dependencies ? (0,_lib_dependencyManager__WEBPACK_IMPORTED_MODULE_8__.checkItemDependencies)(selectedItem.id) : null;\n    // Get dependent items (items that depend on this one)\n    const dependentItems = _data_businessItemsData__WEBPACK_IMPORTED_MODULE_7__.businessItemsData.filter((item)=>{\n        var _item_dependencies;\n        return (_item_dependencies = item.dependencies) === null || _item_dependencies === void 0 ? void 0 : _item_dependencies.includes(selectedItem.id);\n    });\n    const getStatusColor = (status)=>{\n        switch(status){\n            case \"confirmed\":\n                return \"text-green-600 dark:text-green-400 bg-green-50 dark:bg-green-900/20\";\n            case \"action\":\n                return \"text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20\";\n            case \"idea\":\n                return \"text-yellow-600 dark:text-yellow-400 bg-yellow-50 dark:bg-yellow-900/20\";\n            case \"unproven\":\n                return \"text-gray-600 dark:text-gray-400 bg-gray-50 dark:bg-gray-800\";\n            default:\n                return \"text-gray-600 dark:text-gray-400 bg-gray-50 dark:bg-gray-800\";\n        }\n    };\n    const getStatusIcon = (status)=>{\n        switch(status){\n            case \"confirmed\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_ChevronDown_ChevronUp_Clock_HelpCircle_Info_Lightbulb_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                    lineNumber: 83,\n                    columnNumber: 16\n                }, this);\n            case \"action\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_ChevronDown_ChevronUp_Clock_HelpCircle_Info_Lightbulb_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                    lineNumber: 85,\n                    columnNumber: 16\n                }, this);\n            case \"idea\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_ChevronDown_ChevronUp_Clock_HelpCircle_Info_Lightbulb_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                    lineNumber: 87,\n                    columnNumber: 16\n                }, this);\n            case \"unproven\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_ChevronDown_ChevronUp_Clock_HelpCircle_Info_Lightbulb_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                    lineNumber: 89,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_ChevronDown_ChevronUp_Clock_HelpCircle_Info_Lightbulb_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                    lineNumber: 91,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_6__.TooltipProvider, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            variant: \"ghost\",\n                                            size: \"sm\",\n                                            onClick: onBackToItems,\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_ChevronDown_ChevronUp_Clock_HelpCircle_Info_Lightbulb_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                    lineNumber: 108,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Back to Items\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                            lineNumber: 102,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"text-2xl font-bold\",\n                                                    children: selectedItem.title\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                    lineNumber: 112,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_1__.Badge, {\n                                                    className: \"\".concat(getStatusColor(selectedItem.status), \" border-0\"),\n                                                    children: [\n                                                        getStatusIcon(selectedItem.status),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"ml-1 capitalize\",\n                                                            children: selectedItem.status\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                            lineNumber: 117,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                    lineNumber: 113,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                            lineNumber: 111,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                    lineNumber: 101,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_6__.Tooltip, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_6__.TooltipTrigger, {\n                                                    asChild: true,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        variant: \"ghost\",\n                                                        size: \"sm\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_ChevronDown_ChevronUp_Clock_HelpCircle_Info_Lightbulb_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                            lineNumber: 126,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                        lineNumber: 125,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                    lineNumber: 124,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_6__.TooltipContent, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: \"Click the expand button to see detailed guidance and examples\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                        lineNumber: 130,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                    lineNumber: 129,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                            lineNumber: 123,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_4__.Collapsible, {\n                                            open: isInfoExpanded,\n                                            onOpenChange: setIsInfoExpanded,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_4__.CollapsibleTrigger, {\n                                                asChild: true,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    variant: \"outline\",\n                                                    size: \"sm\",\n                                                    className: \"flex items-center gap-2\",\n                                                    children: isInfoExpanded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_ChevronDown_ChevronUp_Clock_HelpCircle_Info_Lightbulb_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                                lineNumber: 149,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            \"Hide Details\"\n                                                        ]\n                                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_ChevronDown_ChevronUp_Clock_HelpCircle_Info_Lightbulb_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                                lineNumber: 154,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            \"Show Details\"\n                                                        ]\n                                                    }, void 0, true)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                    lineNumber: 142,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                lineNumber: 141,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                            lineNumber: 137,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                    lineNumber: 122,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                            lineNumber: 100,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3\",\n                            children: [\n                                (itemData === null || itemData === void 0 ? void 0 : itemData.question) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-blue-600 dark:text-blue-400\",\n                                        children: itemData.question.replace(\"{PROJECT NAME}\", \"your project\")\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                        lineNumber: 168,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                    lineNumber: 167,\n                                    columnNumber: 15\n                                }, this),\n                                ((itemData === null || itemData === void 0 ? void 0 : itemData.description) || selectedItem.description) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-muted-foreground leading-relaxed\",\n                                        children: (itemData === null || itemData === void 0 ? void 0 : itemData.description) || selectedItem.description\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                        lineNumber: 176,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                    lineNumber: 175,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                            lineNumber: 165,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                    lineNumber: 99,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_4__.Collapsible, {\n                    open: isInfoExpanded,\n                    onOpenChange: setIsInfoExpanded,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_4__.CollapsibleContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            className: \"mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                        className: \"text-lg flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_ChevronDown_ChevronUp_Clock_HelpCircle_Info_Lightbulb_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                lineNumber: 190,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"Business Item Information\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                        lineNumber: 189,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                    lineNumber: 188,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    className: \"space-y-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"lg:col-span-2 space-y-4\",\n                                                children: [\n                                                    ((itemData === null || itemData === void 0 ? void 0 : itemData.description) || selectedItem.description) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-medium text-sm text-muted-foreground mb-2\",\n                                                                children: \"Description\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                                lineNumber: 201,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm leading-relaxed\",\n                                                                children: (itemData === null || itemData === void 0 ? void 0 : itemData.description) || selectedItem.description\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                                lineNumber: 204,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                        lineNumber: 200,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    (itemData === null || itemData === void 0 ? void 0 : itemData.question) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-medium text-sm text-muted-foreground mb-2\",\n                                                                children: \"Key Question\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                                lineNumber: 213,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm bg-blue-50 dark:bg-blue-900/20 p-3 rounded-lg border-l-4 border-blue-500\",\n                                                                children: itemData.question.replace(\"{PROJECT NAME}\", \"your project\")\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                                lineNumber: 216,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                        lineNumber: 212,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    (itemData === null || itemData === void 0 ? void 0 : itemData.guidance) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-medium text-sm text-muted-foreground mb-2\",\n                                                                children: \"Guidance\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                                lineNumber: 228,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-muted-foreground leading-relaxed\",\n                                                                children: itemData.guidance\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                                lineNumber: 231,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                        lineNumber: 227,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    (itemData === null || itemData === void 0 ? void 0 : itemData.values) && itemData.values.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-medium text-sm text-muted-foreground mb-2\",\n                                                                children: \"Sample Responses\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                                lineNumber: 240,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-2\",\n                                                                children: [\n                                                                    itemData.values.slice(0, 2).map((value, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"p-2 bg-gray-50 dark:bg-gray-800 rounded border text-xs\",\n                                                                            children: value\n                                                                        }, index, false, {\n                                                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                                            lineNumber: 245,\n                                                                            columnNumber: 29\n                                                                        }, this)),\n                                                                    itemData.values.length > 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-muted-foreground\",\n                                                                        children: [\n                                                                            \"+\",\n                                                                            itemData.values.length - 2,\n                                                                            \" more examples available\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                                        lineNumber: 253,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                                lineNumber: 243,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                        lineNumber: 239,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                lineNumber: 197,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-medium text-sm text-muted-foreground mb-2\",\n                                                                children: \"Progress Metrics\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                                lineNumber: 267,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-3 gap-2 text-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"p-2 bg-yellow-50 dark:bg-yellow-900/20 rounded\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-sm font-bold text-yellow-600\",\n                                                                                children: selectedItem.ideas\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                                                lineNumber: 272,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-xs text-muted-foreground\",\n                                                                                children: \"Ideas\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                                                lineNumber: 275,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                                        lineNumber: 271,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"p-2 bg-blue-50 dark:bg-blue-900/20 rounded\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-sm font-bold text-blue-600\",\n                                                                                children: selectedItem.actions\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                                                lineNumber: 280,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-xs text-muted-foreground\",\n                                                                                children: \"Actions\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                                                lineNumber: 283,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                                        lineNumber: 279,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"p-2 bg-green-50 dark:bg-green-900/20 rounded\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-sm font-bold text-green-600\",\n                                                                                children: selectedItem.results\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                                                lineNumber: 288,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-xs text-muted-foreground\",\n                                                                                children: \"Results\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                                                lineNumber: 291,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                                        lineNumber: 287,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                                lineNumber: 270,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                        lineNumber: 266,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    selectedItem.dependencies && selectedItem.dependencies.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-medium text-sm text-muted-foreground mb-2\",\n                                                                children: \"Dependencies\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                                lineNumber: 302,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            dependencyCheck && !dependencyCheck.isValid && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2 p-2 bg-red-50 dark:bg-red-900/20 rounded mb-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_ChevronDown_ChevronUp_Clock_HelpCircle_Info_Lightbulb_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                        className: \"h-3 w-3 text-red-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                                        lineNumber: 307,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs text-red-600\",\n                                                                        children: \"Not satisfied\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                                        lineNumber: 308,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                                lineNumber: 306,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-1\",\n                                                                children: [\n                                                                    selectedItem.dependencies.slice(0, 3).map((depId, index)=>{\n                                                                        const depItem = _data_businessItemsData__WEBPACK_IMPORTED_MODULE_7__.businessItemsData.find((item)=>item.id === depId);\n                                                                        const isCompleted = (depItem === null || depItem === void 0 ? void 0 : depItem.status) === \"confirmed\";\n                                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center gap-2 text-xs\",\n                                                                            children: [\n                                                                                isCompleted ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_ChevronDown_ChevronUp_Clock_HelpCircle_Info_Lightbulb_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                                    className: \"h-3 w-3 text-green-600\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                                                    lineNumber: 329,\n                                                                                    columnNumber: 39\n                                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_ChevronDown_ChevronUp_Clock_HelpCircle_Info_Lightbulb_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                    className: \"h-3 w-3 text-gray-400\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                                                    lineNumber: 331,\n                                                                                    columnNumber: 39\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: isCompleted ? \"text-green-600\" : \"text-gray-600\",\n                                                                                    children: (depItem === null || depItem === void 0 ? void 0 : depItem.title) || depId\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                                                    lineNumber: 333,\n                                                                                    columnNumber: 37\n                                                                                }, this)\n                                                                            ]\n                                                                        }, index, true, {\n                                                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                                            lineNumber: 324,\n                                                                            columnNumber: 35\n                                                                        }, this);\n                                                                    }),\n                                                                    selectedItem.dependencies.length > 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-muted-foreground\",\n                                                                        children: [\n                                                                            \"+\",\n                                                                            selectedItem.dependencies.length - 3,\n                                                                            \" more\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                                        lineNumber: 346,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                                lineNumber: 313,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                        lineNumber: 301,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-medium text-sm text-muted-foreground mb-2\",\n                                                                children: \"Metadata\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                                lineNumber: 356,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-1 text-xs\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex justify-between\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-muted-foreground\",\n                                                                                children: \"Category:\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                                                lineNumber: 361,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: selectedItem.category\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                                                lineNumber: 364,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                                        lineNumber: 360,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex justify-between\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-muted-foreground\",\n                                                                                children: \"Input Type:\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                                                lineNumber: 367,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: selectedItem.inputType\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                                                lineNumber: 370,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                                        lineNumber: 366,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex justify-between\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-muted-foreground\",\n                                                                                children: \"Order:\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                                                lineNumber: 373,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: selectedItem.order\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                                                lineNumber: 374,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                                        lineNumber: 372,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                                lineNumber: 359,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                        lineNumber: 355,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                lineNumber: 264,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                        lineNumber: 195,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                    lineNumber: 194,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                            lineNumber: 187,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                        lineNumber: 186,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                    lineNumber: 185,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_5__.Separator, {}, void 0, false, {\n                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                    lineNumber: 386,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold mb-4\",\n                            children: \"Action Items & Results\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                            lineNumber: 390,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_business_item_table__WEBPACK_IMPORTED_MODULE_10__.BusinessItemTable, {\n                            itemDetails: itemDetails,\n                            selectedBusinessItem: selectedItem,\n                            onBackToItems: onBackToItems\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                            lineNumber: 391,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                    lineNumber: 389,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n            lineNumber: 97,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n        lineNumber: 96,\n        columnNumber: 5\n    }, this);\n}\n_s(BusinessItemHybridView, \"xhHbMehHQ3yyT7gl75aBEcicsE0=\");\n_c = BusinessItemHybridView;\nvar _c;\n$RefreshReg$(_c, \"BusinessItemHybridView\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/business-item-hybrid-view.tsx\n"));

/***/ })

});