# Clerk Configuration
# Get these values from your Clerk dashboard at https://dashboard.clerk.com
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_test_your_publishable_key_here
CLERK_SECRET_KEY=sk_test_your_secret_key_here

# Clerk URLs
NEXT_PUBLIC_CLERK_SIGN_IN_URL=/auth/login
NEXT_PUBLIC_CLERK_SIGN_UP_URL=/auth/register
NEXT_PUBLIC_CLERK_AFTER_SIGN_IN_URL=/dashboard
NEXT_PUBLIC_CLERK_AFTER_SIGN_UP_URL=/dashboard

# Optional: JWT Secret (if you want to keep the existing JWT functionality as fallback)
JWT_SECRET=your-secret-key-change-in-production

# API Configuration
NEXT_PUBLIC_API_URL=/api

# Admin API Configuration (Backend URL)
# For development, this is typically your local backend server
# For production, this should be your deployed backend URL
NEXT_PUBLIC_ADMIN_API_URL=http://localhost:3002

# App Configuration
NEXT_PUBLIC_APP_NAME=Siift
NEXT_PUBLIC_APP_URL=http://localhost:3000

# Admin Configuration
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=123456789@

# Build Configuration (optional)
ESLINT_NO_DEV_ERRORS=true
TYPESCRIPT_NO_BUILD_ERRORS=true