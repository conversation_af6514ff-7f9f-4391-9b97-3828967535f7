{"../node_modules/@clerk/nextjs/dist/esm/app-router/client/ClerkProvider.js -> ./keyless-creator-reader.js": {"id": "../node_modules/@clerk/nextjs/dist/esm/app-router/client/ClerkProvider.js -> ./keyless-creator-reader.js", "files": ["static/chunks/_app-pages-browser_node_modules_clerk_nextjs_dist_esm_app-router_client_keyless-creator-reader_js.js"]}, "../node_modules/@clerk/nextjs/dist/esm/app-router/client/keyless-cookie-sync.js -> ../keyless-actions.js": {"id": "../node_modules/@clerk/nextjs/dist/esm/app-router/client/keyless-cookie-sync.js -> ../keyless-actions.js", "files": ["static/chunks/_app-pages-browser_node_modules_clerk_nextjs_dist_esm_app-router_keyless-actions_js.js"]}, "../node_modules/@tanstack/query-devtools/build/dev.js -> ./DevtoolsComponent/EDEL3XIZ.js": {"id": "../node_modules/@tanstack/query-devtools/build/dev.js -> ./DevtoolsComponent/EDEL3XIZ.js", "files": ["static/chunks/_app-pages-browser_node_modules_tanstack_query-devtools_build_DevtoolsComponent_EDEL3XIZ_js.js"]}, "../node_modules/@tanstack/query-devtools/build/dev.js -> ./DevtoolsPanelComponent/RN252AT2.js": {"id": "../node_modules/@tanstack/query-devtools/build/dev.js -> ./DevtoolsPanelComponent/RN252AT2.js", "files": ["static/chunks/_app-pages-browser_node_modules_tanstack_query-devtools_build_DevtoolsPanelComponent_RN252AT2_js.js"]}, "app/projects/[id]/page.tsx -> @/stores/businessItemStore": {"id": "app/projects/[id]/page.tsx -> @/stores/businessItemStore", "files": []}}