"use client";

import { But<PERSON> } from "@/components/ui/button";
import { ICON_SIZES } from "@/lib/constants";
import { AnimatePresence, motion } from "framer-motion";
import { BusinessItemHybridView } from "../business-item-hybrid-view";
import { BusinessSectionsGridEnhanced } from "../business-sections/BusinessSectionsGridEnhanced";
import { ContentSections } from "./ContentSections";

interface ProjectMainContentProps {
  activeContent: "drafts" | "files" | null;
  setActiveContent: (content: "drafts" | "files" | null) => void;
  mockDraftItems: any[];
  mockFileItems: any[];
  selectedItem: any;
  itemDetails: any[];
  sections: any[];
  isLoading: boolean;
  error: string | null;
  onBusinessItemClick: (item: any) => void;
  onBackToItems: () => void;
}

export function ProjectMainContent({
  activeContent,
  setActiveContent,
  mockDraftItems,
  mockFileItems,
  selectedItem,
  itemDetails,
  sections,
  isLoading,
  error,
  onBusinessItemClick,
  onBackToItems,
}: ProjectMainContentProps) {
  return (
    <div className="flex-1 min-w-0 min-h-0 w-full max-w-full p-4 pb-20 overflow-y-auto bg-gray-50 dark:bg-gray-900 relative transition-all duration-300 ease-in-out main-content-bottom-fade">
      {/* Background layer with fade effect */}
      <div className="absolute inset-0 project-main-content pointer-events-none" />

      {/* Content layer */}
      <div className="relative z-30">
        {/* Content Section - Drafts and Files */}
        <ContentSections
          activeContent={activeContent}
          setActiveContent={setActiveContent}
          mockDraftItems={mockDraftItems}
          mockFileItems={mockFileItems}
        />

        <AnimatePresence mode="wait">
          {/* Business Sections Grid - Default View */}
          {!activeContent && !selectedItem && (
            <motion.div
              key="business-sections"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3, ease: "easeInOut" }}
            >
              {/* Loading State */}
              {isLoading && (
                <div className="flex items-center justify-center h-64">
                  <div className="text-center">
                    <div
                      className={`animate-spin rounded-full ${ICON_SIZES.lg} border-b-2 border-gray-900 mx-auto mb-4`}
                    ></div>
                    <p className="text-gray-600">
                      Loading business sections...
                    </p>
                  </div>
                </div>
              )}

              {/* Error State */}
              {error && (
                <div className="flex items-center justify-center h-64">
                  <div className="text-center">
                    <p className="text-red-600 mb-4">{error}</p>
                    <Button onClick={() => window.location.reload()}>
                      Try Again
                    </Button>
                  </div>
                </div>
              )}

              {/* Business Sections Grid */}
              {!isLoading && !error && sections.length > 0 && (
                <BusinessSectionsGridEnhanced
                  sections={sections}
                  onItemClick={onBusinessItemClick}
                />
              )}

              {/* Empty State */}
              {!isLoading && !error && sections.length === 0 && (
                <div className="flex items-center justify-center h-64">
                  <div className="text-center">
                    <p className="text-gray-600 mb-4">
                      No business sections found
                    </p>
                    <Button onClick={() => window.location.reload()}>
                      Reload
                    </Button>
                  </div>
                </div>
              )}
            </motion.div>
          )}

          {/* Business Item Detail View */}
          {!activeContent && selectedItem && (
            <motion.div
              key="business-detail"
              initial={{ opacity: 0, x: 30 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -30 }}
              transition={{ duration: 0.3, ease: "easeInOut" }}
              className="-m-4 -p-4"
            >
              <BusinessItemHybridView
                selectedItem={selectedItem}
                itemDetails={itemDetails}
                onBackToItems={onBackToItems}
              />
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  );
}
