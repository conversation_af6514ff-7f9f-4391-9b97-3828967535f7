"use client";

import { TooltipProvider } from "@/components/ui/tooltip";
import {
  BusinessItem,
  BusinessItemDetail,
} from "@/types/BusinessSection.types";
import { useState } from "react";
import { BusinessItemTable } from "./business-item-table";

interface BusinessItemHybridViewProps {
  selectedItem: BusinessItem;
  itemDetails: BusinessItemDetail[];
  onBackToItems: () => void;
}

export function BusinessItemHybridView({
  selectedItem,
  itemDetails,
  onBackToItems,
}: BusinessItemHybridViewProps) {
  const [isInfoExpanded, setIsInfoExpanded] = useState(false);

  // Get comprehensive data for the selected item

  // Get dependent items (items that depend on this one)

  return (
    <TooltipProvider>
      <BusinessItemTable
        itemDetails={itemDetails}
        selectedBusinessItem={selectedItem}
        onBackToItems={onBackToItems}
      />
    </TooltipProvider>
  );
}
