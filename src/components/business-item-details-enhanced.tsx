"use client";

import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Separator } from "@/components/ui/separator";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { 
  ArrowLeft, 
  CheckCircle, 
  Clock, 
  Lightbulb, 
  Zap, 
  AlertCircle,
  Info,
  Users,
  Target,
  Calendar,
  Edit
} from "lucide-react";
import type { BusinessItem } from "@/types/BusinessSection.types";
import { checkItemDependencies } from "@/lib/dependencyManager";
import { businessItemsData } from "@/data/businessItemsData";

interface BusinessItemDetailsEnhancedProps {
  selectedItem: BusinessItem;
  onBackToItems: () => void;
  onEditItem?: (item: BusinessItem) => void;
}

export function BusinessItemDetailsEnhanced({
  selectedItem,
  onBackToItems,
  onEditItem
}: BusinessItemDetailsEnhancedProps) {
  // Get comprehensive data for the selected item
  const itemData = businessItemsData.find(item => item.id === selectedItem.id);
  const dependencyCheck = selectedItem.dependencies ? checkItemDependencies(selectedItem.id) : null;
  
  // Get dependent items (items that depend on this one)
  const dependentItems = businessItemsData.filter(item => 
    item.dependencies?.includes(selectedItem.id)
  );

  const getStatusColor = (status: string) => {
    switch (status) {
      case "confirmed":
        return "text-green-600 dark:text-green-400 bg-green-50 dark:bg-green-900/20";
      case "action":
        return "text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20";
      case "idea":
        return "text-yellow-600 dark:text-yellow-400 bg-yellow-50 dark:bg-yellow-900/20";
      case "unproven":
        return "text-gray-600 dark:text-gray-400 bg-gray-50 dark:bg-gray-800";
      default:
        return "text-gray-600 dark:text-gray-400 bg-gray-50 dark:bg-gray-800";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "confirmed":
        return <CheckCircle className="h-4 w-4" />;
      case "action":
        return <Zap className="h-4 w-4" />;
      case "idea":
        return <Lightbulb className="h-4 w-4" />;
      case "unproven":
        return <Clock className="h-4 w-4" />;
      default:
        return <Clock className="h-4 w-4" />;
    }
  };

  return (
    <TooltipProvider>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={onBackToItems}
              className="flex items-center gap-2"
            >
              <ArrowLeft className="h-4 w-4" />
              Back to Items
            </Button>
            <div className="flex items-center gap-3">
              <Badge variant="outline" className="text-xs">
                {selectedItem.category}
              </Badge>
              {selectedItem.inputType && (
                <Badge variant="secondary" className="text-xs">
                  {selectedItem.inputType}
                </Badge>
              )}
            </div>
          </div>
          
          {onEditItem && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => onEditItem(selectedItem)}
              className="flex items-center gap-2"
            >
              <Edit className="h-4 w-4" />
              Edit
            </Button>
          )}
        </div>

        {/* Main Content */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Left Column - Main Details */}
          <div className="lg:col-span-2 space-y-6">
            {/* Title and Status */}
            <Card>
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="space-y-2">
                    <CardTitle className="text-2xl">{selectedItem.title}</CardTitle>
                    <div className="flex items-center gap-2">
                      <Badge className={`${getStatusColor(selectedItem.status)} border-0`}>
                        {getStatusIcon(selectedItem.status)}
                        <span className="ml-1 capitalize">{selectedItem.status}</span>
                      </Badge>
                      {selectedItem.order && (
                        <Badge variant="outline" className="text-xs">
                          Order: {selectedItem.order}
                        </Badge>
                      )}
                    </div>
                  </div>
                </div>
              </CardHeader>
              
              {(itemData?.description || selectedItem.description) && (
                <CardContent>
                  <div className="space-y-2">
                    <h4 className="font-medium text-sm text-muted-foreground">Description</h4>
                    <p className="text-sm leading-relaxed">
                      {itemData?.description || selectedItem.description}
                    </p>
                  </div>
                </CardContent>
              )}
            </Card>

            {/* Question and Guidance */}
            {(itemData?.question || itemData?.guidance) && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Planning Guide</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {itemData?.question && (
                    <div>
                      <h4 className="font-medium text-sm text-muted-foreground mb-2">Key Question</h4>
                      <p className="text-sm bg-blue-50 dark:bg-blue-900/20 p-3 rounded-lg border-l-4 border-blue-500">
                        {itemData.question.replace('{PROJECT NAME}', 'your project')}
                      </p>
                    </div>
                  )}
                  
                  {itemData?.guidance && (
                    <div>
                      <h4 className="font-medium text-sm text-muted-foreground mb-2">Guidance</h4>
                      <p className="text-sm text-muted-foreground leading-relaxed">
                        {itemData.guidance}
                      </p>
                    </div>
                  )}
                </CardContent>
              </Card>
            )}

            {/* Sample Values */}
            {itemData?.values && itemData.values.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Sample Responses</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {itemData.values.map((value, index) => (
                      <div key={index} className="p-3 bg-gray-50 dark:bg-gray-800 rounded-lg border">
                        <p className="text-sm leading-relaxed">{value}</p>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}
          </div>

          {/* Right Column - Metadata and Dependencies */}
          <div className="space-y-6">
            {/* Status Metrics */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Progress Metrics</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-3 gap-4 text-center">
                  <div>
                    <div className="text-lg font-bold text-yellow-600">{selectedItem.ideas}</div>
                    <div className="text-xs text-muted-foreground">Ideas</div>
                  </div>
                  <div>
                    <div className="text-lg font-bold text-blue-600">{selectedItem.actions}</div>
                    <div className="text-xs text-muted-foreground">Actions</div>
                  </div>
                  <div>
                    <div className="text-lg font-bold text-green-600">{selectedItem.results}</div>
                    <div className="text-xs text-muted-foreground">Results</div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Dependencies */}
            {selectedItem.dependencies && selectedItem.dependencies.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg flex items-center gap-2">
                    <Target className="h-4 w-4" />
                    Dependencies
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  {dependencyCheck && !dependencyCheck.isValid && (
                    <div className="flex items-center gap-2 p-2 bg-red-50 dark:bg-red-900/20 rounded-lg">
                      <AlertCircle className="h-4 w-4 text-red-600" />
                      <span className="text-sm text-red-600">Dependencies not satisfied</span>
                    </div>
                  )}
                  
                  <div className="space-y-2">
                    {selectedItem.dependencies.map((depId, index) => {
                      const depItem = businessItemsData.find(item => item.id === depId);
                      const isCompleted = depItem?.status === "confirmed";
                      
                      return (
                        <div key={index} className="flex items-center gap-2 p-2 rounded border">
                          {isCompleted ? (
                            <CheckCircle className="h-4 w-4 text-green-600" />
                          ) : (
                            <Clock className="h-4 w-4 text-gray-400" />
                          )}
                          <span className={`text-sm ${isCompleted ? 'text-green-600' : 'text-gray-600'}`}>
                            {depItem?.title || depId}
                          </span>
                        </div>
                      );
                    })}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Dependent Items */}
            {dependentItems.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg flex items-center gap-2">
                    <Users className="h-4 w-4" />
                    Blocks These Items
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    {dependentItems.slice(0, 5).map((item, index) => (
                      <div key={index} className="flex items-center gap-2 p-2 rounded border">
                        <Info className="h-4 w-4 text-blue-600" />
                        <span className="text-sm">{item.title}</span>
                      </div>
                    ))}
                    {dependentItems.length > 5 && (
                      <p className="text-xs text-muted-foreground">
                        +{dependentItems.length - 5} more items
                      </p>
                    )}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Metadata */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Metadata</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex justify-between text-sm">
                  <span className="text-muted-foreground">Category:</span>
                  <span>{selectedItem.category}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-muted-foreground">Input Type:</span>
                  <span>{selectedItem.inputType}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-muted-foreground">Order:</span>
                  <span>{selectedItem.order}</span>
                </div>
                {selectedItem.dependencies && (
                  <div className="flex justify-between text-sm">
                    <span className="text-muted-foreground">Dependencies:</span>
                    <span>{selectedItem.dependencies.length}</span>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </TooltipProvider>
  );
}
