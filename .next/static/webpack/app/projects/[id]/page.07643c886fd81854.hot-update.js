"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/projects/[id]/page",{

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js":
/*!****************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/square-pen.js ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ SquarePen)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7\",\n            key: \"1m0v6g\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z\",\n            key: \"ohrbg2\"\n        }\n    ]\n];\nconst SquarePen = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"square-pen\", __iconNode);\n //# sourceMappingURL=square-pen.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/business-item-details-enhanced.tsx":
/*!***********************************************************!*\
  !*** ./src/components/business-item-details-enhanced.tsx ***!
  \***********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BusinessItemDetailsEnhanced: () => (/* binding */ BusinessItemDetailsEnhanced)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_tooltip__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/tooltip */ \"(app-pages-browser)/./src/components/ui/tooltip.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Edit_Info_Lightbulb_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,Clock,Edit,Info,Lightbulb,Target,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Edit_Info_Lightbulb_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,Clock,Edit,Info,Lightbulb,Target,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Edit_Info_Lightbulb_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,Clock,Edit,Info,Lightbulb,Target,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/lightbulb.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Edit_Info_Lightbulb_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,Clock,Edit,Info,Lightbulb,Target,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Edit_Info_Lightbulb_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,Clock,Edit,Info,Lightbulb,Target,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Edit_Info_Lightbulb_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,Clock,Edit,Info,Lightbulb,Target,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Edit_Info_Lightbulb_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,Clock,Edit,Info,Lightbulb,Target,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Edit_Info_Lightbulb_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,Clock,Edit,Info,Lightbulb,Target,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Edit_Info_Lightbulb_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,Clock,Edit,Info,Lightbulb,Target,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Edit_Info_Lightbulb_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,Clock,Edit,Info,Lightbulb,Target,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _lib_dependencyManager__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/dependencyManager */ \"(app-pages-browser)/./src/lib/dependencyManager.ts\");\n/* harmony import */ var _data_businessItemsData__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/data/businessItemsData */ \"(app-pages-browser)/./src/data/businessItemsData.ts\");\n/* __next_internal_client_entry_do_not_use__ BusinessItemDetailsEnhanced auto */ \n\n\n\n\n\n\n\nfunction BusinessItemDetailsEnhanced(param) {\n    let { selectedItem, onBackToItems, onEditItem } = param;\n    // Get comprehensive data for the selected item\n    const itemData = _data_businessItemsData__WEBPACK_IMPORTED_MODULE_6__.businessItemsData.find((item)=>item.id === selectedItem.id);\n    const dependencyCheck = selectedItem.dependencies ? (0,_lib_dependencyManager__WEBPACK_IMPORTED_MODULE_5__.checkItemDependencies)(selectedItem.id) : null;\n    // Get dependent items (items that depend on this one)\n    const dependentItems = _data_businessItemsData__WEBPACK_IMPORTED_MODULE_6__.businessItemsData.filter((item)=>{\n        var _item_dependencies;\n        return (_item_dependencies = item.dependencies) === null || _item_dependencies === void 0 ? void 0 : _item_dependencies.includes(selectedItem.id);\n    });\n    const getStatusColor = (status)=>{\n        switch(status){\n            case \"confirmed\":\n                return \"text-green-600 dark:text-green-400 bg-green-50 dark:bg-green-900/20\";\n            case \"action\":\n                return \"text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20\";\n            case \"idea\":\n                return \"text-yellow-600 dark:text-yellow-400 bg-yellow-50 dark:bg-yellow-900/20\";\n            case \"unproven\":\n                return \"text-gray-600 dark:text-gray-400 bg-gray-50 dark:bg-gray-800\";\n            default:\n                return \"text-gray-600 dark:text-gray-400 bg-gray-50 dark:bg-gray-800\";\n        }\n    };\n    const getStatusIcon = (status)=>{\n        switch(status){\n            case \"confirmed\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Edit_Info_Lightbulb_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-details-enhanced.tsx\",\n                    lineNumber: 64,\n                    columnNumber: 16\n                }, this);\n            case \"action\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Edit_Info_Lightbulb_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-details-enhanced.tsx\",\n                    lineNumber: 66,\n                    columnNumber: 16\n                }, this);\n            case \"idea\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Edit_Info_Lightbulb_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-details-enhanced.tsx\",\n                    lineNumber: 68,\n                    columnNumber: 16\n                }, this);\n            case \"unproven\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Edit_Info_Lightbulb_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-details-enhanced.tsx\",\n                    lineNumber: 70,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Edit_Info_Lightbulb_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-details-enhanced.tsx\",\n                    lineNumber: 72,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_4__.TooltipProvider, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"ghost\",\n                                    size: \"sm\",\n                                    onClick: onBackToItems,\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Edit_Info_Lightbulb_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-details-enhanced.tsx\",\n                                            lineNumber: 88,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Back to Items\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-details-enhanced.tsx\",\n                                    lineNumber: 82,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_1__.Badge, {\n                                            variant: \"outline\",\n                                            className: \"text-xs\",\n                                            children: selectedItem.category\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-details-enhanced.tsx\",\n                                            lineNumber: 92,\n                                            columnNumber: 15\n                                        }, this),\n                                        selectedItem.inputType && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_1__.Badge, {\n                                            variant: \"secondary\",\n                                            className: \"text-xs\",\n                                            children: selectedItem.inputType\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-details-enhanced.tsx\",\n                                            lineNumber: 96,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-details-enhanced.tsx\",\n                                    lineNumber: 91,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-details-enhanced.tsx\",\n                            lineNumber: 81,\n                            columnNumber: 11\n                        }, this),\n                        onEditItem && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            variant: \"outline\",\n                            size: \"sm\",\n                            onClick: ()=>onEditItem(selectedItem),\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Edit_Info_Lightbulb_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-details-enhanced.tsx\",\n                                    lineNumber: 110,\n                                    columnNumber: 15\n                                }, this),\n                                \"Edit\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-details-enhanced.tsx\",\n                            lineNumber: 104,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-details-enhanced.tsx\",\n                    lineNumber: 80,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-2 space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start justify-between\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                            className: \"text-2xl\",\n                                                            children: selectedItem.title\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-details-enhanced.tsx\",\n                                                            lineNumber: 125,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_1__.Badge, {\n                                                                    className: \"\".concat(getStatusColor(selectedItem.status), \" border-0\"),\n                                                                    children: [\n                                                                        getStatusIcon(selectedItem.status),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"ml-1 capitalize\",\n                                                                            children: selectedItem.status\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-details-enhanced.tsx\",\n                                                                            lineNumber: 129,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-details-enhanced.tsx\",\n                                                                    lineNumber: 127,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                selectedItem.order && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_1__.Badge, {\n                                                                    variant: \"outline\",\n                                                                    className: \"text-xs\",\n                                                                    children: [\n                                                                        \"Order: \",\n                                                                        selectedItem.order\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-details-enhanced.tsx\",\n                                                                    lineNumber: 132,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-details-enhanced.tsx\",\n                                                            lineNumber: 126,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-details-enhanced.tsx\",\n                                                    lineNumber: 124,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-details-enhanced.tsx\",\n                                                lineNumber: 123,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-details-enhanced.tsx\",\n                                            lineNumber: 122,\n                                            columnNumber: 15\n                                        }, this),\n                                        ((itemData === null || itemData === void 0 ? void 0 : itemData.description) || selectedItem.description) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium text-sm text-muted-foreground\",\n                                                        children: \"Description\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-details-enhanced.tsx\",\n                                                        lineNumber: 144,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm leading-relaxed\",\n                                                        children: (itemData === null || itemData === void 0 ? void 0 : itemData.description) || selectedItem.description\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-details-enhanced.tsx\",\n                                                        lineNumber: 145,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-details-enhanced.tsx\",\n                                                lineNumber: 143,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-details-enhanced.tsx\",\n                                            lineNumber: 142,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-details-enhanced.tsx\",\n                                    lineNumber: 121,\n                                    columnNumber: 13\n                                }, this),\n                                ((itemData === null || itemData === void 0 ? void 0 : itemData.question) || (itemData === null || itemData === void 0 ? void 0 : itemData.guidance)) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                className: \"text-lg\",\n                                                children: \"Planning Guide\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-details-enhanced.tsx\",\n                                                lineNumber: 157,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-details-enhanced.tsx\",\n                                            lineNumber: 156,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                (itemData === null || itemData === void 0 ? void 0 : itemData.question) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-medium text-sm text-muted-foreground mb-2\",\n                                                            children: \"Key Question\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-details-enhanced.tsx\",\n                                                            lineNumber: 162,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm bg-blue-50 dark:bg-blue-900/20 p-3 rounded-lg border-l-4 border-blue-500\",\n                                                            children: itemData.question.replace('{PROJECT NAME}', 'your project')\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-details-enhanced.tsx\",\n                                                            lineNumber: 163,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-details-enhanced.tsx\",\n                                                    lineNumber: 161,\n                                                    columnNumber: 21\n                                                }, this),\n                                                (itemData === null || itemData === void 0 ? void 0 : itemData.guidance) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-medium text-sm text-muted-foreground mb-2\",\n                                                            children: \"Guidance\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-details-enhanced.tsx\",\n                                                            lineNumber: 171,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-muted-foreground leading-relaxed\",\n                                                            children: itemData.guidance\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-details-enhanced.tsx\",\n                                                            lineNumber: 172,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-details-enhanced.tsx\",\n                                                    lineNumber: 170,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-details-enhanced.tsx\",\n                                            lineNumber: 159,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-details-enhanced.tsx\",\n                                    lineNumber: 155,\n                                    columnNumber: 15\n                                }, this),\n                                (itemData === null || itemData === void 0 ? void 0 : itemData.values) && itemData.values.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                className: \"text-lg\",\n                                                children: \"Sample Responses\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-details-enhanced.tsx\",\n                                                lineNumber: 185,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-details-enhanced.tsx\",\n                                            lineNumber: 184,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: itemData.values.map((value, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-3 bg-gray-50 dark:bg-gray-800 rounded-lg border\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm leading-relaxed\",\n                                                            children: value\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-details-enhanced.tsx\",\n                                                            lineNumber: 191,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, index, false, {\n                                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-details-enhanced.tsx\",\n                                                        lineNumber: 190,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-details-enhanced.tsx\",\n                                                lineNumber: 188,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-details-enhanced.tsx\",\n                                            lineNumber: 187,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-details-enhanced.tsx\",\n                                    lineNumber: 183,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-details-enhanced.tsx\",\n                            lineNumber: 119,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                className: \"text-lg\",\n                                                children: \"Progress Metrics\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-details-enhanced.tsx\",\n                                                lineNumber: 205,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-details-enhanced.tsx\",\n                                            lineNumber: 204,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            className: \"space-y-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-3 gap-4 text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-lg font-bold text-yellow-600\",\n                                                                children: selectedItem.ideas\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-details-enhanced.tsx\",\n                                                                lineNumber: 210,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-muted-foreground\",\n                                                                children: \"Ideas\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-details-enhanced.tsx\",\n                                                                lineNumber: 211,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-details-enhanced.tsx\",\n                                                        lineNumber: 209,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-lg font-bold text-blue-600\",\n                                                                children: selectedItem.actions\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-details-enhanced.tsx\",\n                                                                lineNumber: 214,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-muted-foreground\",\n                                                                children: \"Actions\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-details-enhanced.tsx\",\n                                                                lineNumber: 215,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-details-enhanced.tsx\",\n                                                        lineNumber: 213,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-lg font-bold text-green-600\",\n                                                                children: selectedItem.results\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-details-enhanced.tsx\",\n                                                                lineNumber: 218,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-muted-foreground\",\n                                                                children: \"Results\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-details-enhanced.tsx\",\n                                                                lineNumber: 219,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-details-enhanced.tsx\",\n                                                        lineNumber: 217,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-details-enhanced.tsx\",\n                                                lineNumber: 208,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-details-enhanced.tsx\",\n                                            lineNumber: 207,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-details-enhanced.tsx\",\n                                    lineNumber: 203,\n                                    columnNumber: 13\n                                }, this),\n                                selectedItem.dependencies && selectedItem.dependencies.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                className: \"text-lg flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Edit_Info_Lightbulb_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-details-enhanced.tsx\",\n                                                        lineNumber: 230,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"Dependencies\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-details-enhanced.tsx\",\n                                                lineNumber: 229,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-details-enhanced.tsx\",\n                                            lineNumber: 228,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            className: \"space-y-3\",\n                                            children: [\n                                                dependencyCheck && !dependencyCheck.isValid && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2 p-2 bg-red-50 dark:bg-red-900/20 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Edit_Info_Lightbulb_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                            className: \"h-4 w-4 text-red-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-details-enhanced.tsx\",\n                                                            lineNumber: 237,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm text-red-600\",\n                                                            children: \"Dependencies not satisfied\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-details-enhanced.tsx\",\n                                                            lineNumber: 238,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-details-enhanced.tsx\",\n                                                    lineNumber: 236,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: selectedItem.dependencies.map((depId, index)=>{\n                                                        const depItem = _data_businessItemsData__WEBPACK_IMPORTED_MODULE_6__.businessItemsData.find((item)=>item.id === depId);\n                                                        const isCompleted = (depItem === null || depItem === void 0 ? void 0 : depItem.status) === \"confirmed\";\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-2 p-2 rounded border\",\n                                                            children: [\n                                                                isCompleted ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Edit_Info_Lightbulb_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-green-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-details-enhanced.tsx\",\n                                                                    lineNumber: 250,\n                                                                    columnNumber: 29\n                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Edit_Info_Lightbulb_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-gray-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-details-enhanced.tsx\",\n                                                                    lineNumber: 252,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm \".concat(isCompleted ? 'text-green-600' : 'text-gray-600'),\n                                                                    children: (depItem === null || depItem === void 0 ? void 0 : depItem.title) || depId\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-details-enhanced.tsx\",\n                                                                    lineNumber: 254,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, index, true, {\n                                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-details-enhanced.tsx\",\n                                                            lineNumber: 248,\n                                                            columnNumber: 25\n                                                        }, this);\n                                                    })\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-details-enhanced.tsx\",\n                                                    lineNumber: 242,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-details-enhanced.tsx\",\n                                            lineNumber: 234,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-details-enhanced.tsx\",\n                                    lineNumber: 227,\n                                    columnNumber: 15\n                                }, this),\n                                dependentItems.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                className: \"text-lg flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Edit_Info_Lightbulb_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-details-enhanced.tsx\",\n                                                        lineNumber: 270,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"Blocks These Items\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-details-enhanced.tsx\",\n                                                lineNumber: 269,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-details-enhanced.tsx\",\n                                            lineNumber: 268,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    dependentItems.slice(0, 5).map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-2 p-2 rounded border\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Edit_Info_Lightbulb_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-blue-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-details-enhanced.tsx\",\n                                                                    lineNumber: 278,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm\",\n                                                                    children: item.title\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-details-enhanced.tsx\",\n                                                                    lineNumber: 279,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, index, true, {\n                                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-details-enhanced.tsx\",\n                                                            lineNumber: 277,\n                                                            columnNumber: 23\n                                                        }, this)),\n                                                    dependentItems.length > 5 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-muted-foreground\",\n                                                        children: [\n                                                            \"+\",\n                                                            dependentItems.length - 5,\n                                                            \" more items\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-details-enhanced.tsx\",\n                                                        lineNumber: 283,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-details-enhanced.tsx\",\n                                                lineNumber: 275,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-details-enhanced.tsx\",\n                                            lineNumber: 274,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-details-enhanced.tsx\",\n                                    lineNumber: 267,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                className: \"text-lg\",\n                                                children: \"Metadata\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-details-enhanced.tsx\",\n                                                lineNumber: 295,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-details-enhanced.tsx\",\n                                            lineNumber: 294,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            className: \"space-y-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-muted-foreground\",\n                                                            children: \"Category:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-details-enhanced.tsx\",\n                                                            lineNumber: 299,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: selectedItem.category\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-details-enhanced.tsx\",\n                                                            lineNumber: 300,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-details-enhanced.tsx\",\n                                                    lineNumber: 298,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-muted-foreground\",\n                                                            children: \"Input Type:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-details-enhanced.tsx\",\n                                                            lineNumber: 303,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: selectedItem.inputType\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-details-enhanced.tsx\",\n                                                            lineNumber: 304,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-details-enhanced.tsx\",\n                                                    lineNumber: 302,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-muted-foreground\",\n                                                            children: \"Order:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-details-enhanced.tsx\",\n                                                            lineNumber: 307,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: selectedItem.order\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-details-enhanced.tsx\",\n                                                            lineNumber: 308,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-details-enhanced.tsx\",\n                                                    lineNumber: 306,\n                                                    columnNumber: 17\n                                                }, this),\n                                                selectedItem.dependencies && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-muted-foreground\",\n                                                            children: \"Dependencies:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-details-enhanced.tsx\",\n                                                            lineNumber: 312,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: selectedItem.dependencies.length\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-details-enhanced.tsx\",\n                                                            lineNumber: 313,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-details-enhanced.tsx\",\n                                                    lineNumber: 311,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-details-enhanced.tsx\",\n                                            lineNumber: 297,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-details-enhanced.tsx\",\n                                    lineNumber: 293,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-details-enhanced.tsx\",\n                            lineNumber: 201,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-details-enhanced.tsx\",\n                    lineNumber: 117,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-details-enhanced.tsx\",\n            lineNumber: 78,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-details-enhanced.tsx\",\n        lineNumber: 77,\n        columnNumber: 5\n    }, this);\n}\n_c = BusinessItemDetailsEnhanced;\nvar _c;\n$RefreshReg$(_c, \"BusinessItemDetailsEnhanced\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/business-item-details-enhanced.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/project/ProjectMainContent.tsx":
/*!*******************************************************!*\
  !*** ./src/components/project/ProjectMainContent.tsx ***!
  \*******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ProjectMainContent: () => (/* binding */ ProjectMainContent)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _lib_constants__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/constants */ \"(app-pages-browser)/./src/lib/constants.ts\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _business_item_details_enhanced__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../business-item-details-enhanced */ \"(app-pages-browser)/./src/components/business-item-details-enhanced.tsx\");\n/* harmony import */ var _business_sections_BusinessSectionsGridEnhanced__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../business-sections/BusinessSectionsGridEnhanced */ \"(app-pages-browser)/./src/components/business-sections/BusinessSectionsGridEnhanced.tsx\");\n/* harmony import */ var _ContentSections__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./ContentSections */ \"(app-pages-browser)/./src/components/project/ContentSections.tsx\");\n/* __next_internal_client_entry_do_not_use__ ProjectMainContent auto */ \n\n\n\n\n\n\nfunction ProjectMainContent(param) {\n    let { activeContent, setActiveContent, mockDraftItems, mockFileItems, selectedItem, sections, isLoading, error, onBusinessItemClick, onBackToItems } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex-1 min-w-0 min-h-0 w-full max-w-full p-4 pb-20 overflow-y-auto bg-gray-50 dark:bg-gray-900 relative transition-all duration-300 ease-in-out main-content-bottom-fade\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 project-main-content pointer-events-none\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectMainContent.tsx\",\n                lineNumber: 39,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-30\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ContentSections__WEBPACK_IMPORTED_MODULE_5__.ContentSections, {\n                        activeContent: activeContent,\n                        setActiveContent: setActiveContent,\n                        mockDraftItems: mockDraftItems,\n                        mockFileItems: mockFileItems\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectMainContent.tsx\",\n                        lineNumber: 44,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.AnimatePresence, {\n                        mode: \"wait\",\n                        children: [\n                            !activeContent && !selectedItem && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                exit: {\n                                    opacity: 0,\n                                    y: -20\n                                },\n                                transition: {\n                                    duration: 0.3,\n                                    ease: \"easeInOut\"\n                                },\n                                children: [\n                                    isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center h-64\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"animate-spin rounded-full \".concat(_lib_constants__WEBPACK_IMPORTED_MODULE_2__.ICON_SIZES.lg, \" border-b-2 border-gray-900 mx-auto mb-4\")\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectMainContent.tsx\",\n                                                    lineNumber: 65,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600\",\n                                                    children: \"Loading business sections...\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectMainContent.tsx\",\n                                                    lineNumber: 68,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectMainContent.tsx\",\n                                            lineNumber: 64,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectMainContent.tsx\",\n                                        lineNumber: 63,\n                                        columnNumber: 17\n                                    }, this),\n                                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center h-64\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-red-600 mb-4\",\n                                                    children: error\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectMainContent.tsx\",\n                                                    lineNumber: 79,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                                    onClick: ()=>window.location.reload(),\n                                                    children: \"Try Again\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectMainContent.tsx\",\n                                                    lineNumber: 80,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectMainContent.tsx\",\n                                            lineNumber: 78,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectMainContent.tsx\",\n                                        lineNumber: 77,\n                                        columnNumber: 17\n                                    }, this),\n                                    !isLoading && !error && sections.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_business_sections_BusinessSectionsGridEnhanced__WEBPACK_IMPORTED_MODULE_4__.BusinessSectionsGridEnhanced, {\n                                        sections: sections,\n                                        onItemClick: onBusinessItemClick\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectMainContent.tsx\",\n                                        lineNumber: 89,\n                                        columnNumber: 17\n                                    }, this),\n                                    !isLoading && !error && sections.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center h-64\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600 mb-4\",\n                                                    children: \"No business sections found\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectMainContent.tsx\",\n                                                    lineNumber: 99,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                                    onClick: ()=>window.location.reload(),\n                                                    children: \"Reload\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectMainContent.tsx\",\n                                                    lineNumber: 102,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectMainContent.tsx\",\n                                            lineNumber: 98,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectMainContent.tsx\",\n                                        lineNumber: 97,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, \"business-sections\", true, {\n                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectMainContent.tsx\",\n                                lineNumber: 54,\n                                columnNumber: 13\n                            }, this),\n                            !activeContent && selectedItem && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    x: 30\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    x: 0\n                                },\n                                exit: {\n                                    opacity: 0,\n                                    x: -30\n                                },\n                                transition: {\n                                    duration: 0.3,\n                                    ease: \"easeInOut\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_business_item_details_enhanced__WEBPACK_IMPORTED_MODULE_3__.BusinessItemDetailsEnhanced, {\n                                    selectedItem: selectedItem,\n                                    onBackToItems: onBackToItems\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectMainContent.tsx\",\n                                    lineNumber: 120,\n                                    columnNumber: 15\n                                }, this)\n                            }, \"business-detail\", false, {\n                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectMainContent.tsx\",\n                                lineNumber: 113,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectMainContent.tsx\",\n                        lineNumber: 51,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectMainContent.tsx\",\n                lineNumber: 42,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectMainContent.tsx\",\n        lineNumber: 37,\n        columnNumber: 5\n    }, this);\n}\n_c = ProjectMainContent;\nvar _c;\n$RefreshReg$(_c, \"ProjectMainContent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL3Byb2plY3QvUHJvamVjdE1haW5Db250ZW50LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7O0FBRWdEO0FBQ0g7QUFDVztBQUN3QjtBQUNpQjtBQUM3QztBQWdCN0MsU0FBU08sbUJBQW1CLEtBV1Q7UUFYUyxFQUNqQ0MsYUFBYSxFQUNiQyxnQkFBZ0IsRUFDaEJDLGNBQWMsRUFDZEMsYUFBYSxFQUNiQyxZQUFZLEVBQ1pDLFFBQVEsRUFDUkMsU0FBUyxFQUNUQyxLQUFLLEVBQ0xDLG1CQUFtQixFQUNuQkMsYUFBYSxFQUNXLEdBWFM7SUFZakMscUJBQ0UsOERBQUNDO1FBQUlDLFdBQVU7OzBCQUViLDhEQUFDRDtnQkFBSUMsV0FBVTs7Ozs7OzBCQUdmLDhEQUFDRDtnQkFBSUMsV0FBVTs7a0NBRWIsOERBQUNiLDZEQUFlQTt3QkFDZEUsZUFBZUE7d0JBQ2ZDLGtCQUFrQkE7d0JBQ2xCQyxnQkFBZ0JBO3dCQUNoQkMsZUFBZUE7Ozs7OztrQ0FHakIsOERBQUNULDBEQUFlQTt3QkFBQ2tCLE1BQUs7OzRCQUVuQixDQUFDWixpQkFBaUIsQ0FBQ0ksOEJBQ2xCLDhEQUFDVCxpREFBTUEsQ0FBQ2UsR0FBRztnQ0FFVEcsU0FBUztvQ0FBRUMsU0FBUztvQ0FBR0MsR0FBRztnQ0FBRztnQ0FDN0JDLFNBQVM7b0NBQUVGLFNBQVM7b0NBQUdDLEdBQUc7Z0NBQUU7Z0NBQzVCRSxNQUFNO29DQUFFSCxTQUFTO29DQUFHQyxHQUFHLENBQUM7Z0NBQUc7Z0NBQzNCRyxZQUFZO29DQUFFQyxVQUFVO29DQUFLQyxNQUFNO2dDQUFZOztvQ0FHOUNkLDJCQUNDLDhEQUFDSTt3Q0FBSUMsV0FBVTtrREFDYiw0RUFBQ0Q7NENBQUlDLFdBQVU7OzhEQUNiLDhEQUFDRDtvREFDQ0MsV0FBVyw2QkFBMkMsT0FBZGxCLHNEQUFVQSxDQUFDNEIsRUFBRSxFQUFDOzs7Ozs7OERBRXhELDhEQUFDQztvREFBRVgsV0FBVTs4REFBZ0I7Ozs7Ozs7Ozs7Ozs7Ozs7O29DQVFsQ0osdUJBQ0MsOERBQUNHO3dDQUFJQyxXQUFVO2tEQUNiLDRFQUFDRDs0Q0FBSUMsV0FBVTs7OERBQ2IsOERBQUNXO29EQUFFWCxXQUFVOzhEQUFxQko7Ozs7Ozs4REFDbEMsOERBQUNmLHlEQUFNQTtvREFBQytCLFNBQVMsSUFBTUMsT0FBT0MsUUFBUSxDQUFDQyxNQUFNOzhEQUFJOzs7Ozs7Ozs7Ozs7Ozs7OztvQ0FRdEQsQ0FBQ3BCLGFBQWEsQ0FBQ0MsU0FBU0YsU0FBU3NCLE1BQU0sR0FBRyxtQkFDekMsOERBQUM5Qix5R0FBNEJBO3dDQUMzQlEsVUFBVUE7d0NBQ1Z1QixhQUFhcEI7Ozs7OztvQ0FLaEIsQ0FBQ0YsYUFBYSxDQUFDQyxTQUFTRixTQUFTc0IsTUFBTSxLQUFLLG1CQUMzQyw4REFBQ2pCO3dDQUFJQyxXQUFVO2tEQUNiLDRFQUFDRDs0Q0FBSUMsV0FBVTs7OERBQ2IsOERBQUNXO29EQUFFWCxXQUFVOzhEQUFxQjs7Ozs7OzhEQUdsQyw4REFBQ25CLHlEQUFNQTtvREFBQytCLFNBQVMsSUFBTUMsT0FBT0MsUUFBUSxDQUFDQyxNQUFNOzhEQUFJOzs7Ozs7Ozs7Ozs7Ozs7Ozs7K0JBL0NuRDs7Ozs7NEJBeURQLENBQUMxQixpQkFBaUJJLDhCQUNqQiw4REFBQ1QsaURBQU1BLENBQUNlLEdBQUc7Z0NBRVRHLFNBQVM7b0NBQUVDLFNBQVM7b0NBQUdlLEdBQUc7Z0NBQUc7Z0NBQzdCYixTQUFTO29DQUFFRixTQUFTO29DQUFHZSxHQUFHO2dDQUFFO2dDQUM1QlosTUFBTTtvQ0FBRUgsU0FBUztvQ0FBR2UsR0FBRyxDQUFDO2dDQUFHO2dDQUMzQlgsWUFBWTtvQ0FBRUMsVUFBVTtvQ0FBS0MsTUFBTTtnQ0FBWTswQ0FFL0MsNEVBQUN4Qix3RkFBMkJBO29DQUMxQlEsY0FBY0E7b0NBQ2RLLGVBQWVBOzs7Ozs7K0JBUmI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBZ0JsQjtLQTFHZ0JWIiwic291cmNlcyI6WyIvVXNlcnMvYXJlZi9EYXRhL25ldyBlcmEvc2lpZnQtbmV4dC9zcmMvY29tcG9uZW50cy9wcm9qZWN0L1Byb2plY3RNYWluQ29udGVudC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCI7XG5cbmltcG9ydCB7IEJ1dHRvbiB9IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvYnV0dG9uXCI7XG5pbXBvcnQgeyBJQ09OX1NJWkVTIH0gZnJvbSBcIkAvbGliL2NvbnN0YW50c1wiO1xuaW1wb3J0IHsgQW5pbWF0ZVByZXNlbmNlLCBtb3Rpb24gfSBmcm9tIFwiZnJhbWVyLW1vdGlvblwiO1xuaW1wb3J0IHsgQnVzaW5lc3NJdGVtRGV0YWlsc0VuaGFuY2VkIH0gZnJvbSBcIi4uL2J1c2luZXNzLWl0ZW0tZGV0YWlscy1lbmhhbmNlZFwiO1xuaW1wb3J0IHsgQnVzaW5lc3NTZWN0aW9uc0dyaWRFbmhhbmNlZCB9IGZyb20gXCIuLi9idXNpbmVzcy1zZWN0aW9ucy9CdXNpbmVzc1NlY3Rpb25zR3JpZEVuaGFuY2VkXCI7XG5pbXBvcnQgeyBDb250ZW50U2VjdGlvbnMgfSBmcm9tIFwiLi9Db250ZW50U2VjdGlvbnNcIjtcblxuaW50ZXJmYWNlIFByb2plY3RNYWluQ29udGVudFByb3BzIHtcbiAgYWN0aXZlQ29udGVudDogXCJkcmFmdHNcIiB8IFwiZmlsZXNcIiB8IG51bGw7XG4gIHNldEFjdGl2ZUNvbnRlbnQ6IChjb250ZW50OiBcImRyYWZ0c1wiIHwgXCJmaWxlc1wiIHwgbnVsbCkgPT4gdm9pZDtcbiAgbW9ja0RyYWZ0SXRlbXM6IGFueVtdO1xuICBtb2NrRmlsZUl0ZW1zOiBhbnlbXTtcbiAgc2VsZWN0ZWRJdGVtOiBhbnk7XG4gIGl0ZW1EZXRhaWxzOiBhbnlbXTtcbiAgc2VjdGlvbnM6IGFueVtdO1xuICBpc0xvYWRpbmc6IGJvb2xlYW47XG4gIGVycm9yOiBzdHJpbmcgfCBudWxsO1xuICBvbkJ1c2luZXNzSXRlbUNsaWNrOiAoaXRlbTogYW55KSA9PiB2b2lkO1xuICBvbkJhY2tUb0l0ZW1zOiAoKSA9PiB2b2lkO1xufVxuXG5leHBvcnQgZnVuY3Rpb24gUHJvamVjdE1haW5Db250ZW50KHtcbiAgYWN0aXZlQ29udGVudCxcbiAgc2V0QWN0aXZlQ29udGVudCxcbiAgbW9ja0RyYWZ0SXRlbXMsXG4gIG1vY2tGaWxlSXRlbXMsXG4gIHNlbGVjdGVkSXRlbSxcbiAgc2VjdGlvbnMsXG4gIGlzTG9hZGluZyxcbiAgZXJyb3IsXG4gIG9uQnVzaW5lc3NJdGVtQ2xpY2ssXG4gIG9uQmFja1RvSXRlbXMsXG59OiBQcm9qZWN0TWFpbkNvbnRlbnRQcm9wcykge1xuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xIG1pbi13LTAgbWluLWgtMCB3LWZ1bGwgbWF4LXctZnVsbCBwLTQgcGItMjAgb3ZlcmZsb3cteS1hdXRvIGJnLWdyYXktNTAgZGFyazpiZy1ncmF5LTkwMCByZWxhdGl2ZSB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0zMDAgZWFzZS1pbi1vdXQgbWFpbi1jb250ZW50LWJvdHRvbS1mYWRlXCI+XG4gICAgICB7LyogQmFja2dyb3VuZCBsYXllciB3aXRoIGZhZGUgZWZmZWN0ICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSBpbnNldC0wIHByb2plY3QtbWFpbi1jb250ZW50IHBvaW50ZXItZXZlbnRzLW5vbmVcIiAvPlxuXG4gICAgICB7LyogQ29udGVudCBsYXllciAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmUgei0zMFwiPlxuICAgICAgICB7LyogQ29udGVudCBTZWN0aW9uIC0gRHJhZnRzIGFuZCBGaWxlcyAqL31cbiAgICAgICAgPENvbnRlbnRTZWN0aW9uc1xuICAgICAgICAgIGFjdGl2ZUNvbnRlbnQ9e2FjdGl2ZUNvbnRlbnR9XG4gICAgICAgICAgc2V0QWN0aXZlQ29udGVudD17c2V0QWN0aXZlQ29udGVudH1cbiAgICAgICAgICBtb2NrRHJhZnRJdGVtcz17bW9ja0RyYWZ0SXRlbXN9XG4gICAgICAgICAgbW9ja0ZpbGVJdGVtcz17bW9ja0ZpbGVJdGVtc31cbiAgICAgICAgLz5cblxuICAgICAgICA8QW5pbWF0ZVByZXNlbmNlIG1vZGU9XCJ3YWl0XCI+XG4gICAgICAgICAgey8qIEJ1c2luZXNzIFNlY3Rpb25zIEdyaWQgLSBEZWZhdWx0IFZpZXcgKi99XG4gICAgICAgICAgeyFhY3RpdmVDb250ZW50ICYmICFzZWxlY3RlZEl0ZW0gJiYgKFxuICAgICAgICAgICAgPG1vdGlvbi5kaXZcbiAgICAgICAgICAgICAga2V5PVwiYnVzaW5lc3Mtc2VjdGlvbnNcIlxuICAgICAgICAgICAgICBpbml0aWFsPXt7IG9wYWNpdHk6IDAsIHk6IDIwIH19XG4gICAgICAgICAgICAgIGFuaW1hdGU9e3sgb3BhY2l0eTogMSwgeTogMCB9fVxuICAgICAgICAgICAgICBleGl0PXt7IG9wYWNpdHk6IDAsIHk6IC0yMCB9fVxuICAgICAgICAgICAgICB0cmFuc2l0aW9uPXt7IGR1cmF0aW9uOiAwLjMsIGVhc2U6IFwiZWFzZUluT3V0XCIgfX1cbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgey8qIExvYWRpbmcgU3RhdGUgKi99XG4gICAgICAgICAgICAgIHtpc0xvYWRpbmcgJiYgKFxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgaC02NFwiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2XG4gICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgYW5pbWF0ZS1zcGluIHJvdW5kZWQtZnVsbCAke0lDT05fU0laRVMubGd9IGJvcmRlci1iLTIgYm9yZGVyLWdyYXktOTAwIG14LWF1dG8gbWItNGB9XG4gICAgICAgICAgICAgICAgICAgID48L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgIExvYWRpbmcgYnVzaW5lc3Mgc2VjdGlvbnMuLi5cbiAgICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICl9XG5cbiAgICAgICAgICAgICAgey8qIEVycm9yIFN0YXRlICovfVxuICAgICAgICAgICAgICB7ZXJyb3IgJiYgKFxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgaC02NFwiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXJlZC02MDAgbWItNFwiPntlcnJvcn08L3A+XG4gICAgICAgICAgICAgICAgICAgIDxCdXR0b24gb25DbGljaz17KCkgPT4gd2luZG93LmxvY2F0aW9uLnJlbG9hZCgpfT5cbiAgICAgICAgICAgICAgICAgICAgICBUcnkgQWdhaW5cbiAgICAgICAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgKX1cblxuICAgICAgICAgICAgICB7LyogQnVzaW5lc3MgU2VjdGlvbnMgR3JpZCAqL31cbiAgICAgICAgICAgICAgeyFpc0xvYWRpbmcgJiYgIWVycm9yICYmIHNlY3Rpb25zLmxlbmd0aCA+IDAgJiYgKFxuICAgICAgICAgICAgICAgIDxCdXNpbmVzc1NlY3Rpb25zR3JpZEVuaGFuY2VkXG4gICAgICAgICAgICAgICAgICBzZWN0aW9ucz17c2VjdGlvbnN9XG4gICAgICAgICAgICAgICAgICBvbkl0ZW1DbGljaz17b25CdXNpbmVzc0l0ZW1DbGlja31cbiAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICApfVxuXG4gICAgICAgICAgICAgIHsvKiBFbXB0eSBTdGF0ZSAqL31cbiAgICAgICAgICAgICAgeyFpc0xvYWRpbmcgJiYgIWVycm9yICYmIHNlY3Rpb25zLmxlbmd0aCA9PT0gMCAmJiAoXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBoLTY0XCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDAgbWItNFwiPlxuICAgICAgICAgICAgICAgICAgICAgIE5vIGJ1c2luZXNzIHNlY3Rpb25zIGZvdW5kXG4gICAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICAgICAgPEJ1dHRvbiBvbkNsaWNrPXsoKSA9PiB3aW5kb3cubG9jYXRpb24ucmVsb2FkKCl9PlxuICAgICAgICAgICAgICAgICAgICAgIFJlbG9hZFxuICAgICAgICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgPC9tb3Rpb24uZGl2PlxuICAgICAgICAgICl9XG5cbiAgICAgICAgICB7LyogQnVzaW5lc3MgSXRlbSBEZXRhaWwgVmlldyAqL31cbiAgICAgICAgICB7IWFjdGl2ZUNvbnRlbnQgJiYgc2VsZWN0ZWRJdGVtICYmIChcbiAgICAgICAgICAgIDxtb3Rpb24uZGl2XG4gICAgICAgICAgICAgIGtleT1cImJ1c2luZXNzLWRldGFpbFwiXG4gICAgICAgICAgICAgIGluaXRpYWw9e3sgb3BhY2l0eTogMCwgeDogMzAgfX1cbiAgICAgICAgICAgICAgYW5pbWF0ZT17eyBvcGFjaXR5OiAxLCB4OiAwIH19XG4gICAgICAgICAgICAgIGV4aXQ9e3sgb3BhY2l0eTogMCwgeDogLTMwIH19XG4gICAgICAgICAgICAgIHRyYW5zaXRpb249e3sgZHVyYXRpb246IDAuMywgZWFzZTogXCJlYXNlSW5PdXRcIiB9fVxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICA8QnVzaW5lc3NJdGVtRGV0YWlsc0VuaGFuY2VkXG4gICAgICAgICAgICAgICAgc2VsZWN0ZWRJdGVtPXtzZWxlY3RlZEl0ZW19XG4gICAgICAgICAgICAgICAgb25CYWNrVG9JdGVtcz17b25CYWNrVG9JdGVtc31cbiAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgIDwvbW90aW9uLmRpdj5cbiAgICAgICAgICApfVxuICAgICAgICA8L0FuaW1hdGVQcmVzZW5jZT5cbiAgICAgIDwvZGl2PlxuICAgIDwvZGl2PlxuICApO1xufVxuIl0sIm5hbWVzIjpbIkJ1dHRvbiIsIklDT05fU0laRVMiLCJBbmltYXRlUHJlc2VuY2UiLCJtb3Rpb24iLCJCdXNpbmVzc0l0ZW1EZXRhaWxzRW5oYW5jZWQiLCJCdXNpbmVzc1NlY3Rpb25zR3JpZEVuaGFuY2VkIiwiQ29udGVudFNlY3Rpb25zIiwiUHJvamVjdE1haW5Db250ZW50IiwiYWN0aXZlQ29udGVudCIsInNldEFjdGl2ZUNvbnRlbnQiLCJtb2NrRHJhZnRJdGVtcyIsIm1vY2tGaWxlSXRlbXMiLCJzZWxlY3RlZEl0ZW0iLCJzZWN0aW9ucyIsImlzTG9hZGluZyIsImVycm9yIiwib25CdXNpbmVzc0l0ZW1DbGljayIsIm9uQmFja1RvSXRlbXMiLCJkaXYiLCJjbGFzc05hbWUiLCJtb2RlIiwiaW5pdGlhbCIsIm9wYWNpdHkiLCJ5IiwiYW5pbWF0ZSIsImV4aXQiLCJ0cmFuc2l0aW9uIiwiZHVyYXRpb24iLCJlYXNlIiwibGciLCJwIiwib25DbGljayIsIndpbmRvdyIsImxvY2F0aW9uIiwicmVsb2FkIiwibGVuZ3RoIiwib25JdGVtQ2xpY2siLCJ4Il0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/project/ProjectMainContent.tsx\n"));

/***/ })

});