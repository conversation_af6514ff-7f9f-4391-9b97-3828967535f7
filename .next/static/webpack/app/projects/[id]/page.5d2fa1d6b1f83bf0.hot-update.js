"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/projects/[id]/page",{

/***/ "(app-pages-browser)/./src/stores/businessItemStoreEnhanced.ts":
/*!*************************************************!*\
  !*** ./src/stores/businessItemStoreEnhanced.ts ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkItemDependencies: () => (/* binding */ checkItemDependencies),\n/* harmony export */   getItemData: () => (/* binding */ getItemData),\n/* harmony export */   getRelatedItems: () => (/* binding */ getRelatedItems),\n/* harmony export */   useBusinessItemStoreEnhanced: () => (/* binding */ useBusinessItemStoreEnhanced)\n/* harmony export */ });\n/* harmony import */ var _data_businessItemsData__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/data/businessItemsData */ \"(app-pages-browser)/./src/data/businessItemsData.ts\");\n/* harmony import */ var _services_businessSectionsApi__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/services/businessSectionsApi */ \"(app-pages-browser)/./src/services/businessSectionsApi.ts\");\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! zustand */ \"(app-pages-browser)/./node_modules/zustand/esm/react.mjs\");\n/* harmony import */ var zustand_middleware__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! zustand/middleware */ \"(app-pages-browser)/./node_modules/zustand/esm/middleware.mjs\");\n// Enhanced business item store using the new comprehensive data structure\n\n\n\n\nconst useBusinessItemStoreEnhanced = (0,zustand__WEBPACK_IMPORTED_MODULE_2__.create)()((0,zustand_middleware__WEBPACK_IMPORTED_MODULE_3__.devtools)((set, get)=>({\n        selectedItem: null,\n        itemDetails: [],\n        isLoading: false,\n        error: null,\n        setSelectedItem: (item)=>set({\n                selectedItem: item\n            }),\n        setItemDetails: (details)=>set({\n                itemDetails: details\n            }),\n        setLoading: (isLoading)=>set({\n                isLoading\n            }),\n        setError: (error)=>set({\n                error\n            }),\n        // Fetch comprehensive item details using the new data structure\n        fetchItemDetails: async function(itemId) {\n            let projectId = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"default\";\n            try {\n                var _itemData_question;\n                set({\n                    isLoading: true,\n                    error: null\n                });\n                // First, try to get stored details from browser storage\n                const storedDetails = browserStorageService.getItemDetails(itemId, projectId);\n                if (storedDetails.length > 0) {\n                    // Use stored details if available\n                    console.log(\"Using stored details for\", itemId, storedDetails);\n                    set({\n                        itemDetails: storedDetails,\n                        isLoading: false\n                    });\n                    return;\n                }\n                console.log(\"No stored details found, creating initial data for\", itemId);\n                // If no stored details, create initial details from comprehensive data\n                const itemData = _data_businessItemsData__WEBPACK_IMPORTED_MODULE_0__.businessItemsData.find((item)=>item.id === itemId);\n                if (!itemData) {\n                    throw new Error(\"Item with ID \".concat(itemId, \" not found\"));\n                }\n                // Create initial detail items from sample values\n                const initialDetails = [];\n                // Add sample values as editable detail items for the table\n                if (itemData.values && itemData.values.length > 0) {\n                    itemData.values.forEach((value, index)=>{\n                        var _itemData_question;\n                        initialDetails.push({\n                            id: \"\".concat(itemData.id, \"-value-\").concat(index),\n                            title: \"Response \".concat(index + 1),\n                            status: \"idea\",\n                            actions: \"Sample response - edit to customize\",\n                            result: value,\n                            description: \"Sample response for: \".concat(((_itemData_question = itemData.question) === null || _itemData_question === void 0 ? void 0 : _itemData_question.replace(\"{PROJECT NAME}\", \"your project\")) || itemData.title),\n                            createdAt: new Date().toISOString(),\n                            updatedAt: new Date().toISOString()\n                        });\n                    });\n                }\n                // Add a blank item for new entries\n                initialDetails.push({\n                    id: \"\".concat(itemData.id, \"-new-entry\"),\n                    title: \"Add your response\",\n                    status: \"unproven\",\n                    actions: \"Click to edit and add your own analysis\",\n                    result: \"Enter your findings here...\",\n                    description: \"Your analysis for: \".concat(((_itemData_question = itemData.question) === null || _itemData_question === void 0 ? void 0 : _itemData_question.replace(\"{PROJECT NAME}\", \"your project\")) || itemData.title),\n                    createdAt: new Date().toISOString(),\n                    updatedAt: new Date().toISOString()\n                });\n                // Save initial details to browser storage\n                browserStorageService.saveItemDetails(itemId, initialDetails, projectId);\n                console.log(\"Created initial details for\", itemId, initialDetails);\n                set({\n                    itemDetails: initialDetails,\n                    isLoading: false\n                });\n            } catch (error) {\n                console.error(\"Error fetching item details:\", error);\n                set({\n                    error: error instanceof Error ? error.message : \"Failed to fetch item details\",\n                    isLoading: false\n                });\n            }\n        },\n        // Update item status with dependency validation\n        updateItemStatus: async function(itemId, status) {\n            let projectId = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : \"default\";\n            try {\n                set({\n                    isLoading: true,\n                    error: null\n                });\n                // Check dependencies before allowing status change to \"confirmed\"\n                if (status === \"confirmed\") {\n                    const canProgress = get().checkDependencies(itemId);\n                    if (!canProgress) {\n                        throw new Error(\"Cannot mark as confirmed: dependencies not satisfied\");\n                    }\n                }\n                // Use the API to update the item\n                await _services_businessSectionsApi__WEBPACK_IMPORTED_MODULE_1__.BusinessSectionsApi.updateItem(projectId, itemId, {\n                    status\n                });\n                // Update local state if we have the item selected\n                const { selectedItem } = get();\n                if (selectedItem && selectedItem.id === itemId) {\n                    set({\n                        selectedItem: {\n                            ...selectedItem,\n                            status\n                        }\n                    });\n                }\n                set({\n                    isLoading: false\n                });\n            } catch (error) {\n                console.error(\"Error updating item status:\", error);\n                set({\n                    error: error instanceof Error ? error.message : \"Failed to update item status\",\n                    isLoading: false\n                });\n            }\n        },\n        addItemDetail: (detail)=>{\n            const { selectedItem } = get();\n            if (!selectedItem) return;\n            const updatedDetails = [\n                ...get().itemDetails,\n                detail\n            ];\n            set({\n                itemDetails: updatedDetails\n            });\n            // Save to browser storage\n            browserStorageService.saveItemDetails(selectedItem.id, updatedDetails, \"default\");\n        },\n        updateItemDetail: (id, updates)=>{\n            const { selectedItem } = get();\n            if (!selectedItem) return;\n            const updatedDetails = get().itemDetails.map((item)=>item.id === id ? {\n                    ...item,\n                    ...updates,\n                    updatedAt: new Date().toISOString()\n                } : item);\n            set({\n                itemDetails: updatedDetails\n            });\n            // Save to browser storage\n            browserStorageService.saveItemDetails(selectedItem.id, updatedDetails, \"default\");\n        },\n        removeItemDetail: (id)=>{\n            const { selectedItem } = get();\n            if (!selectedItem) return;\n            const updatedDetails = get().itemDetails.filter((item)=>item.id !== id);\n            set({\n                itemDetails: updatedDetails\n            });\n            // Save to browser storage\n            browserStorageService.saveItemDetails(selectedItem.id, updatedDetails, \"default\");\n        },\n        // Get comprehensive item data\n        getItemData: (itemId)=>{\n            return _data_businessItemsData__WEBPACK_IMPORTED_MODULE_0__.businessItemsData.find((item)=>item.id === itemId);\n        },\n        // Check if item dependencies are satisfied\n        checkDependencies: (itemId)=>{\n            const itemData = _data_businessItemsData__WEBPACK_IMPORTED_MODULE_0__.businessItemsData.find((item)=>item.id === itemId);\n            if (!itemData || !itemData.dependencies || itemData.dependencies.length === 0) {\n                return true;\n            }\n            return itemData.dependencies.every((depId)=>{\n                const depItem = _data_businessItemsData__WEBPACK_IMPORTED_MODULE_0__.businessItemsData.find((dep)=>dep.id === depId);\n                return (depItem === null || depItem === void 0 ? void 0 : depItem.status) === \"confirmed\";\n            });\n        },\n        // Get related items (dependencies and dependents)\n        getRelatedItems: (itemId)=>{\n            var _itemData_dependencies;\n            const itemData = _data_businessItemsData__WEBPACK_IMPORTED_MODULE_0__.businessItemsData.find((item)=>item.id === itemId);\n            // Get dependencies\n            const dependencies = (itemData === null || itemData === void 0 ? void 0 : (_itemData_dependencies = itemData.dependencies) === null || _itemData_dependencies === void 0 ? void 0 : _itemData_dependencies.map((depId)=>_data_businessItemsData__WEBPACK_IMPORTED_MODULE_0__.businessItemsData.find((item)=>item.id === depId)).filter(Boolean)) || [];\n            // Get dependents (items that depend on this one)\n            const dependents = _data_businessItemsData__WEBPACK_IMPORTED_MODULE_0__.businessItemsData.filter((item)=>{\n                var _item_dependencies;\n                return (_item_dependencies = item.dependencies) === null || _item_dependencies === void 0 ? void 0 : _item_dependencies.includes(itemId);\n            });\n            return {\n                dependencies,\n                dependents\n            };\n        }\n    }), {\n    name: \"business-item-store-enhanced\"\n}));\n// Helper functions for easy access\nconst getItemData = (itemId)=>{\n    return _data_businessItemsData__WEBPACK_IMPORTED_MODULE_0__.businessItemsData.find((item)=>item.id === itemId);\n};\nconst checkItemDependencies = (itemId)=>{\n    const itemData = _data_businessItemsData__WEBPACK_IMPORTED_MODULE_0__.businessItemsData.find((item)=>item.id === itemId);\n    if (!itemData || !itemData.dependencies || itemData.dependencies.length === 0) {\n        return true;\n    }\n    return itemData.dependencies.every((depId)=>{\n        const depItem = _data_businessItemsData__WEBPACK_IMPORTED_MODULE_0__.businessItemsData.find((dep)=>dep.id === depId);\n        return (depItem === null || depItem === void 0 ? void 0 : depItem.status) === \"confirmed\";\n    });\n};\nconst getRelatedItems = (itemId)=>{\n    var _itemData_dependencies;\n    const itemData = _data_businessItemsData__WEBPACK_IMPORTED_MODULE_0__.businessItemsData.find((item)=>item.id === itemId);\n    const dependencies = (itemData === null || itemData === void 0 ? void 0 : (_itemData_dependencies = itemData.dependencies) === null || _itemData_dependencies === void 0 ? void 0 : _itemData_dependencies.map((depId)=>_data_businessItemsData__WEBPACK_IMPORTED_MODULE_0__.businessItemsData.find((item)=>item.id === depId)).filter(Boolean)) || [];\n    const dependents = _data_businessItemsData__WEBPACK_IMPORTED_MODULE_0__.businessItemsData.filter((item)=>{\n        var _item_dependencies;\n        return (_item_dependencies = item.dependencies) === null || _item_dependencies === void 0 ? void 0 : _item_dependencies.includes(itemId);\n    });\n    return {\n        dependencies,\n        dependents\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/stores/businessItemStoreEnhanced.ts\n"));

/***/ })

});