"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/projects/[id]/page",{

/***/ "(app-pages-browser)/./src/components/business-item-hybrid-view.tsx":
/*!******************************************************!*\
  !*** ./src/components/business-item-hybrid-view.tsx ***!
  \******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BusinessItemHybridView: () => (/* binding */ BusinessItemHybridView)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_collapsible__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/collapsible */ \"(app-pages-browser)/./src/components/ui/collapsible.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./src/components/ui/separator.tsx\");\n/* harmony import */ var _components_ui_tooltip__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/tooltip */ \"(app-pages-browser)/./src/components/ui/tooltip.tsx\");\n/* harmony import */ var _data_businessItemsData__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/data/businessItemsData */ \"(app-pages-browser)/./src/data/businessItemsData.ts\");\n/* harmony import */ var _lib_dependencyManager__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/dependencyManager */ \"(app-pages-browser)/./src/lib/dependencyManager.ts\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_ChevronDown_ChevronUp_Clock_HelpCircle_Lightbulb_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,ChevronDown,ChevronUp,Clock,HelpCircle,Lightbulb,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_ChevronDown_ChevronUp_Clock_HelpCircle_Lightbulb_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,ChevronDown,ChevronUp,Clock,HelpCircle,Lightbulb,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_ChevronDown_ChevronUp_Clock_HelpCircle_Lightbulb_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,ChevronDown,ChevronUp,Clock,HelpCircle,Lightbulb,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/lightbulb.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_ChevronDown_ChevronUp_Clock_HelpCircle_Lightbulb_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,ChevronDown,ChevronUp,Clock,HelpCircle,Lightbulb,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_ChevronDown_ChevronUp_Clock_HelpCircle_Lightbulb_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,ChevronDown,ChevronUp,Clock,HelpCircle,Lightbulb,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_ChevronDown_ChevronUp_Clock_HelpCircle_Lightbulb_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,ChevronDown,ChevronUp,Clock,HelpCircle,Lightbulb,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-question-mark.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_ChevronDown_ChevronUp_Clock_HelpCircle_Lightbulb_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,ChevronDown,ChevronUp,Clock,HelpCircle,Lightbulb,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-up.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_ChevronDown_ChevronUp_Clock_HelpCircle_Lightbulb_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,ChevronDown,ChevronUp,Clock,HelpCircle,Lightbulb,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_ChevronDown_ChevronUp_Clock_HelpCircle_Lightbulb_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,ChevronDown,ChevronUp,Clock,HelpCircle,Lightbulb,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var _business_item_table__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./business-item-table */ \"(app-pages-browser)/./src/components/business-item-table.tsx\");\n/* __next_internal_client_entry_do_not_use__ BusinessItemHybridView auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction BusinessItemHybridView(param) {\n    let { selectedItem, itemDetails, onBackToItems, projectName = \"your project\" } = param;\n    _s();\n    const [isInfoExpanded, setIsInfoExpanded] = (0,react__WEBPACK_IMPORTED_MODULE_8__.useState)(false);\n    // Get comprehensive data for the selected item\n    const itemData = _data_businessItemsData__WEBPACK_IMPORTED_MODULE_6__.businessItemsData.find((item)=>item.id === selectedItem.id);\n    const dependencyCheck = selectedItem.dependencies ? (0,_lib_dependencyManager__WEBPACK_IMPORTED_MODULE_7__.checkItemDependencies)(selectedItem.id) : null;\n    // Get dependent items (items that depend on this one)\n    const getStatusColor = (status)=>{\n        switch(status){\n            case \"confirmed\":\n                return \"text-green-600 dark:text-green-400 bg-green-50 dark:bg-green-900/20\";\n            case \"action\":\n                return \"text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20\";\n            case \"idea\":\n                return \"text-yellow-600 dark:text-yellow-400 bg-yellow-50 dark:bg-yellow-900/20\";\n            case \"unproven\":\n                return \"text-gray-600 dark:text-gray-400 bg-gray-50 dark:bg-gray-800\";\n            default:\n                return \"text-gray-600 dark:text-gray-400 bg-gray-50 dark:bg-gray-800\";\n        }\n    };\n    const getStatusIcon = (status)=>{\n        switch(status){\n            case \"confirmed\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_ChevronDown_ChevronUp_Clock_HelpCircle_Lightbulb_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                    lineNumber: 80,\n                    columnNumber: 16\n                }, this);\n            case \"action\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_ChevronDown_ChevronUp_Clock_HelpCircle_Lightbulb_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                    lineNumber: 82,\n                    columnNumber: 16\n                }, this);\n            case \"idea\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_ChevronDown_ChevronUp_Clock_HelpCircle_Lightbulb_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                    lineNumber: 84,\n                    columnNumber: 16\n                }, this);\n            case \"unproven\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_ChevronDown_ChevronUp_Clock_HelpCircle_Lightbulb_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                    lineNumber: 86,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_ChevronDown_ChevronUp_Clock_HelpCircle_Lightbulb_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                    lineNumber: 88,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_5__.TooltipProvider, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            variant: \"ghost\",\n                                            size: \"sm\",\n                                            onClick: onBackToItems,\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_ChevronDown_ChevronUp_Clock_HelpCircle_Lightbulb_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                    lineNumber: 105,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Back to Items\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                            lineNumber: 99,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"text-2xl font-bold\",\n                                                    children: selectedItem.title\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                    lineNumber: 109,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_1__.Badge, {\n                                                    className: \"\".concat(getStatusColor(selectedItem.status), \" border-0\"),\n                                                    children: [\n                                                        getStatusIcon(selectedItem.status),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"ml-1 capitalize\",\n                                                            children: selectedItem.status\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                            lineNumber: 114,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                    lineNumber: 110,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                            lineNumber: 108,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                    lineNumber: 98,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_5__.Tooltip, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_5__.TooltipTrigger, {\n                                                    asChild: true,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        variant: \"ghost\",\n                                                        size: \"sm\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_ChevronDown_ChevronUp_Clock_HelpCircle_Lightbulb_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                            lineNumber: 123,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                        lineNumber: 122,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                    lineNumber: 121,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_5__.TooltipContent, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: \"Click the expand button to see detailed guidance and examples\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                        lineNumber: 127,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                    lineNumber: 126,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                            lineNumber: 120,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_3__.Collapsible, {\n                                            open: isInfoExpanded,\n                                            onOpenChange: setIsInfoExpanded,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_3__.CollapsibleTrigger, {\n                                                asChild: true,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    variant: \"outline\",\n                                                    size: \"sm\",\n                                                    className: \"flex items-center gap-2\",\n                                                    children: isInfoExpanded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_ChevronDown_ChevronUp_Clock_HelpCircle_Lightbulb_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                                lineNumber: 146,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            \"Hide Details\"\n                                                        ]\n                                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_ChevronDown_ChevronUp_Clock_HelpCircle_Lightbulb_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                                lineNumber: 151,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            \"Show Details\"\n                                                        ]\n                                                    }, void 0, true)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                    lineNumber: 139,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                lineNumber: 138,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                            lineNumber: 134,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                    lineNumber: 119,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                            lineNumber: 97,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3\",\n                            children: [\n                                (itemData === null || itemData === void 0 ? void 0 : itemData.question) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-blue-600 dark:text-blue-400\",\n                                        children: itemData.question.replace(\"{PROJECT NAME}\", \"your project\")\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                        lineNumber: 165,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                    lineNumber: 164,\n                                    columnNumber: 15\n                                }, this),\n                                ((itemData === null || itemData === void 0 ? void 0 : itemData.description) || selectedItem.description) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-muted-foreground leading-relaxed\",\n                                        children: (itemData === null || itemData === void 0 ? void 0 : itemData.description) || selectedItem.description\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                        lineNumber: 173,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                    lineNumber: 172,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                            lineNumber: 162,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                    lineNumber: 96,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_3__.Collapsible, {\n                    open: isInfoExpanded,\n                    onOpenChange: setIsInfoExpanded,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_3__.CollapsibleContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-6 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg border\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"lg:col-span-2 space-y-4\",\n                                        children: [\n                                            ((itemData === null || itemData === void 0 ? void 0 : itemData.description) || selectedItem.description) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium text-sm text-muted-foreground mb-2\",\n                                                        children: \"Description\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                        lineNumber: 191,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm leading-relaxed\",\n                                                        children: (itemData === null || itemData === void 0 ? void 0 : itemData.description) || selectedItem.description\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                        lineNumber: 194,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                lineNumber: 190,\n                                                columnNumber: 21\n                                            }, this),\n                                            (itemData === null || itemData === void 0 ? void 0 : itemData.question) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium text-sm text-muted-foreground mb-2\",\n                                                        children: \"Key Question\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                        lineNumber: 203,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm bg-blue-50 dark:bg-blue-900/20 p-3 rounded-lg border-l-4 border-blue-500\",\n                                                        children: itemData.question.replace(\"{PROJECT NAME}\", \"your project\")\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                        lineNumber: 206,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                lineNumber: 202,\n                                                columnNumber: 21\n                                            }, this),\n                                            (itemData === null || itemData === void 0 ? void 0 : itemData.guidance) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium text-sm text-muted-foreground mb-2\",\n                                                        children: \"Guidance\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                        lineNumber: 218,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-muted-foreground leading-relaxed\",\n                                                        children: itemData.guidance\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                        lineNumber: 221,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                lineNumber: 217,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium text-sm text-muted-foreground mb-2\",\n                                                        children: \"Sample Data\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                        lineNumber: 229,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-muted-foreground\",\n                                                        children: \"Sample responses have been loaded into the table below. You can edit them or add your own entries.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                        lineNumber: 232,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                lineNumber: 228,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                        lineNumber: 187,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium text-sm text-muted-foreground mb-2\",\n                                                        children: \"Progress Metrics\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                        lineNumber: 243,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-3 gap-2 text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-2 bg-yellow-50 dark:bg-yellow-900/20 rounded\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm font-bold text-yellow-600\",\n                                                                        children: selectedItem.ideas\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                                        lineNumber: 248,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-xs text-muted-foreground\",\n                                                                        children: \"Ideas\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                                        lineNumber: 251,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                                lineNumber: 247,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-2 bg-blue-50 dark:bg-blue-900/20 rounded\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm font-bold text-blue-600\",\n                                                                        children: selectedItem.actions\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                                        lineNumber: 256,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-xs text-muted-foreground\",\n                                                                        children: \"Actions\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                                        lineNumber: 259,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                                lineNumber: 255,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-2 bg-green-50 dark:bg-green-900/20 rounded\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm font-bold text-green-600\",\n                                                                        children: selectedItem.results\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                                        lineNumber: 264,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-xs text-muted-foreground\",\n                                                                        children: \"Results\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                                        lineNumber: 267,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                                lineNumber: 263,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                        lineNumber: 246,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                lineNumber: 242,\n                                                columnNumber: 19\n                                            }, this),\n                                            selectedItem.dependencies && selectedItem.dependencies.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium text-sm text-muted-foreground mb-2\",\n                                                        children: \"Dependencies\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                        lineNumber: 278,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    dependencyCheck && !dependencyCheck.isValid && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2 p-2 bg-red-50 dark:bg-red-900/20 rounded mb-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_ChevronDown_ChevronUp_Clock_HelpCircle_Lightbulb_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                className: \"h-3 w-3 text-red-600\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                                lineNumber: 283,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs text-red-600\",\n                                                                children: \"Not satisfied\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                                lineNumber: 284,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                        lineNumber: 282,\n                                                        columnNumber: 27\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-1\",\n                                                        children: [\n                                                            selectedItem.dependencies.slice(0, 3).map((depId, index)=>{\n                                                                const depItem = _data_businessItemsData__WEBPACK_IMPORTED_MODULE_6__.businessItemsData.find((item)=>item.id === depId);\n                                                                const isCompleted = (depItem === null || depItem === void 0 ? void 0 : depItem.status) === \"confirmed\";\n                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2 text-xs\",\n                                                                    children: [\n                                                                        isCompleted ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_ChevronDown_ChevronUp_Clock_HelpCircle_Lightbulb_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                            className: \"h-3 w-3 text-green-600\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                                            lineNumber: 305,\n                                                                            columnNumber: 37\n                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_ChevronDown_ChevronUp_Clock_HelpCircle_Lightbulb_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                            className: \"h-3 w-3 text-gray-400\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                                            lineNumber: 307,\n                                                                            columnNumber: 37\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: isCompleted ? \"text-green-600\" : \"text-gray-600\",\n                                                                            children: (depItem === null || depItem === void 0 ? void 0 : depItem.title) || depId\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                                            lineNumber: 309,\n                                                                            columnNumber: 35\n                                                                        }, this)\n                                                                    ]\n                                                                }, index, true, {\n                                                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                                    lineNumber: 300,\n                                                                    columnNumber: 33\n                                                                }, this);\n                                                            }),\n                                                            selectedItem.dependencies.length > 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-muted-foreground\",\n                                                                children: [\n                                                                    \"+\",\n                                                                    selectedItem.dependencies.length - 3,\n                                                                    \" more\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                                lineNumber: 322,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                        lineNumber: 289,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                lineNumber: 277,\n                                                columnNumber: 23\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium text-sm text-muted-foreground mb-2\",\n                                                        children: \"Metadata\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                        lineNumber: 332,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-1 text-xs\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-muted-foreground\",\n                                                                        children: \"Category:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                                        lineNumber: 337,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: selectedItem.category\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                                        lineNumber: 338,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                                lineNumber: 336,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-muted-foreground\",\n                                                                        children: \"Input Type:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                                        lineNumber: 341,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: selectedItem.inputType\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                                        lineNumber: 344,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                                lineNumber: 340,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-muted-foreground\",\n                                                                        children: \"Order:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                                        lineNumber: 347,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: selectedItem.order\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                                        lineNumber: 348,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                                lineNumber: 346,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                        lineNumber: 335,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                lineNumber: 331,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                        lineNumber: 240,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                lineNumber: 185,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                            lineNumber: 184,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                        lineNumber: 183,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                    lineNumber: 182,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_4__.Separator, {}, void 0, false, {\n                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                    lineNumber: 359,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold mb-4\",\n                            children: \"Action Items & Results\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                            lineNumber: 363,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_business_item_table__WEBPACK_IMPORTED_MODULE_9__.BusinessItemTable, {\n                            itemDetails: itemDetails,\n                            selectedBusinessItem: selectedItem,\n                            onBackToItems: onBackToItems\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                            lineNumber: 364,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                    lineNumber: 362,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n            lineNumber: 94,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n        lineNumber: 93,\n        columnNumber: 5\n    }, this);\n}\n_s(BusinessItemHybridView, \"xhHbMehHQ3yyT7gl75aBEcicsE0=\");\n_c = BusinessItemHybridView;\nvar _c;\n$RefreshReg$(_c, \"BusinessItemHybridView\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/business-item-hybrid-view.tsx\n"));

/***/ })

});