"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/projects/[id]/page",{

/***/ "(app-pages-browser)/./src/components/project/ProjectDetailHeader.tsx":
/*!********************************************************!*\
  !*** ./src/components/project/ProjectDetailHeader.tsx ***!
  \********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ProjectDetailHeader: () => (/* binding */ ProjectDetailHeader)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_ui_popover__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/popover */ \"(app-pages-browser)/./src/components/ui/popover.tsx\");\n/* harmony import */ var _components_ui_sidebar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/sidebar */ \"(app-pages-browser)/./src/components/ui/sidebar.tsx\");\n/* harmony import */ var _components_ui_sidebar_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/sidebar-button */ \"(app-pages-browser)/./src/components/ui/sidebar-button.tsx\");\n/* harmony import */ var _hooks_useAnalytics__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/useAnalytics */ \"(app-pages-browser)/./src/hooks/useAnalytics.ts\");\n/* harmony import */ var _lib_constants__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/constants */ \"(app-pages-browser)/./src/lib/constants.ts\");\n/* harmony import */ var _mockdata_businessItemQuestions__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/mockdata/businessItemQuestions */ \"(app-pages-browser)/./src/mockdata/businessItemQuestions.ts\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_HelpCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,HelpCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_HelpCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,HelpCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-question-mark.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_7__);\n/* __next_internal_client_entry_do_not_use__ ProjectDetailHeader auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction ProjectDetailHeader(param) {\n    let { selectedBusinessItem, onBackToItems } = param;\n    _s();\n    const { trackClick, trackCustomEvent } = (0,_hooks_useAnalytics__WEBPACK_IMPORTED_MODULE_4__.useAnalytics)();\n    const [isHelpOpen, setIsHelpOpen] = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(false);\n    const [isInfoOpen, setIsInfoOpen] = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(false);\n    const { state, isMobile } = (0,_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_2__.useSidebar)();\n    // Get dynamic question and answer based on business item\n    const { question, answer } = (0,_mockdata_businessItemQuestions__WEBPACK_IMPORTED_MODULE_6__.getBusinessItemQA)(selectedBusinessItem);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"flex h-20 shrink-0 items-center gap-2 transition-[width] ease-linear border-b border-border\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-between w-full h-full px-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-4 h-full\",\n                    children: [\n                        isMobile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar_button__WEBPACK_IMPORTED_MODULE_3__.SidebarButton, {\n                            onClick: ()=>{\n                                trackClick(\"back-to-items\", \"project-detail-header\");\n                                trackCustomEvent(\"navigation_clicked\", {\n                                    destination: \"items\",\n                                    from_page: \"item-detail\",\n                                    location: \"header\"\n                                });\n                                onBackToItems();\n                            },\n                            icon: _barrel_optimize_names_ArrowLeft_HelpCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n                            variant: \"ghost\",\n                            size: \"lg\",\n                            layout: \"icon-only\",\n                            showBorder: true,\n                            hoverColor: \"grey\",\n                            hoverScale: true,\n                            iconClassName: _lib_constants__WEBPACK_IMPORTED_MODULE_5__.ICON_SIZES.lg\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                            lineNumber: 41,\n                            columnNumber: 13\n                        }, this),\n                        state === \"collapsed\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-xl font-semibold text-gray-900 dark:text-gray-100\",\n                            children: (selectedBusinessItem === null || selectedBusinessItem === void 0 ? void 0 : selectedBusinessItem.title) || \"Untitled Item\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                            lineNumber: 62,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                    lineNumber: 39,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 flex flex-col justify-center mr-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-3 mb-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm font-bold text-[var(--brand-dark)] dark:text-[var(--primary)]\",\n                                children: question\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                                lineNumber: 72,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_1__.Popover, {\n                                open: isInfoOpen,\n                                onOpenChange: setIsInfoOpen,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_1__.PopoverTrigger, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar_button__WEBPACK_IMPORTED_MODULE_3__.SidebarButton, {\n                                            icon: _barrel_optimize_names_ArrowLeft_HelpCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n                                            variant: \"ghost\",\n                                            size: \"sm\",\n                                            layout: \"icon-only\",\n                                            showBorder: false,\n                                            hoverColor: \"grey\",\n                                            hoverScale: true,\n                                            onClick: ()=>{\n                                                trackClick(\"info-button\", \"project-header\");\n                                                trackCustomEvent(\"info_clicked\", {\n                                                    from_item: selectedBusinessItem === null || selectedBusinessItem === void 0 ? void 0 : selectedBusinessItem.title,\n                                                    location: \"header\"\n                                                });\n                                            },\n                                            iconClassName: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                                            lineNumber: 77,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                                        lineNumber: 76,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_1__.PopoverContent, {\n                                        align: \"start\",\n                                        side: \"bottom\",\n                                        className: \"w-80\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-semibold text-sm\",\n                                                    children: \"Description\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                                                    lineNumber: 97,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-600 dark:text-gray-400 leading-relaxed\",\n                                                    children: answer\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                                                    lineNumber: 98,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                                            lineNumber: 96,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                                        lineNumber: 95,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                                lineNumber: 75,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                        lineNumber: 71,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                    lineNumber: 69,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-2 h-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_1__.Popover, {\n                        open: isHelpOpen,\n                        onOpenChange: setIsHelpOpen,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_1__.PopoverTrigger, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar_button__WEBPACK_IMPORTED_MODULE_3__.SidebarButton, {\n                                    icon: _barrel_optimize_names_ArrowLeft_HelpCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n                                    variant: \"ghost\",\n                                    size: \"lg\",\n                                    layout: \"icon-only\",\n                                    showBorder: true,\n                                    hoverColor: \"grey\",\n                                    hoverScale: true,\n                                    onClick: ()=>{\n                                        trackClick(\"help-button\", \"project-header\");\n                                        trackCustomEvent(\"help_clicked\", {\n                                            from_item: selectedBusinessItem === null || selectedBusinessItem === void 0 ? void 0 : selectedBusinessItem.title,\n                                            location: \"header\"\n                                        });\n                                    },\n                                    iconClassName: _lib_constants__WEBPACK_IMPORTED_MODULE_5__.ICON_SIZES.lg\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                                    lineNumber: 111,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                                lineNumber: 110,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_1__.PopoverContent, {\n                                align: \"end\",\n                                side: \"bottom\",\n                                className: \"w-80\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold text-sm\",\n                                            children: \"Table Guide\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                                            lineNumber: 131,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2 text-xs\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2 p-2 border-gray-200 dark:border-gray-700 rounded-md border-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-blue-500 rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                                                            lineNumber: 134,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    children: \"Idea:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                                                                    lineNumber: 136,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \" What is the main idea of the item?\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                                                            lineNumber: 135,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                                                    lineNumber: 133,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2 p-2 border-gray-200 dark:border-gray-700 rounded-md border-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-yellow-500 rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                                                            lineNumber: 140,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    children: \"Action:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                                                                    lineNumber: 142,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \" What was done to achieve the idea?\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                                                            lineNumber: 141,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                                                    lineNumber: 139,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2 p-2 border-gray-200 dark:border-gray-700 rounded-md border-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-green-500 rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                                                            lineNumber: 147,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    children: \"Result:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                                                                    lineNumber: 149,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \" What was the outcome of the action?\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                                                            lineNumber: 148,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                                                    lineNumber: 146,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2 p-2 border-gray-200 dark:border-gray-700 rounded-md border-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-purple-500 rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                                                            lineNumber: 154,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    children: \"Status:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                                                                    lineNumber: 156,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \" Current state\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                                                            lineNumber: 155,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                                                    lineNumber: 153,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                                            lineNumber: 132,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"pt-2 border-t border-gray-200 dark:border-gray-700\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-gray-600 dark:text-gray-400\",\n                                                children: \"Click cells to edit • Drag rows to reorder • Use + to add items\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                                                lineNumber: 161,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                                            lineNumber: 160,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                                    lineNumber: 130,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                                lineNumber: 129,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                        lineNumber: 109,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                    lineNumber: 108,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n            lineNumber: 37,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n        lineNumber: 36,\n        columnNumber: 5\n    }, this);\n}\n_s(ProjectDetailHeader, \"tPemVZUBvKAktCNdsiqoqorGMA8=\", false, function() {\n    return [\n        _hooks_useAnalytics__WEBPACK_IMPORTED_MODULE_4__.useAnalytics,\n        _components_ui_sidebar__WEBPACK_IMPORTED_MODULE_2__.useSidebar\n    ];\n});\n_c = ProjectDetailHeader;\nvar _c;\n$RefreshReg$(_c, \"ProjectDetailHeader\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/project/ProjectDetailHeader.tsx\n"));

/***/ })

});