"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/projects/[id]/page",{

/***/ "(app-pages-browser)/./src/app/projects/[id]/page.tsx":
/*!****************************************!*\
  !*** ./src/app/projects/[id]/page.tsx ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProjectDetailPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_project__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/project */ \"(app-pages-browser)/./src/components/project/index.ts\");\n/* harmony import */ var _components_project_sidebar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/project-sidebar */ \"(app-pages-browser)/./src/components/project-sidebar.tsx\");\n/* harmony import */ var _components_ui_sidebar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/sidebar */ \"(app-pages-browser)/./src/components/ui/sidebar.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _hooks_useResizable__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../../hooks/useResizable */ \"(app-pages-browser)/./src/hooks/useResizable.ts\");\n/* harmony import */ var _lib_businessSectionsDataNew__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../../lib/businessSectionsDataNew */ \"(app-pages-browser)/./src/lib/businessSectionsDataNew.ts\");\n/* harmony import */ var _stores_businessSectionStore__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../../../stores/businessSectionStore */ \"(app-pages-browser)/./src/stores/businessSectionStore.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n// Mock data\nconst mockDraftItems = [\n    {\n        id: 1,\n        title: \"Project proposal\",\n        status: \"draft\",\n        lastModified: \"2 hours ago\"\n    },\n    {\n        id: 2,\n        title: \"Design brief\",\n        status: \"draft\",\n        lastModified: \"1 day ago\"\n    },\n    {\n        id: 3,\n        title: \"Technical specs\",\n        status: \"draft\",\n        lastModified: \"3 days ago\"\n    }\n];\n// Mock file items data\nconst mockFileItems = [\n    {\n        id: 1,\n        title: \"logo.svg\",\n        type: \"image\",\n        size: \"24KB\"\n    },\n    {\n        id: 2,\n        title: \"wireframes.fig\",\n        type: \"design\",\n        size: \"1.2MB\"\n    },\n    {\n        id: 3,\n        title: \"requirements.pdf\",\n        type: \"document\",\n        size: \"156KB\"\n    },\n    {\n        id: 4,\n        title: \"styleguide.pdf\",\n        type: \"document\",\n        size: \"2.1MB\"\n    }\n];\nfunction ProjectDetailPage() {\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.useParams)();\n    const projectId = params.id;\n    const [activeContent, setActiveContent] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(null);\n    const [chatWidth, setChatWidth] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(\"45%\");\n    const [isChatCollapsed, setIsChatCollapsed] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    const [sidebarWidth, setSidebarWidth] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(\"45vw\");\n    // Sync sidebar width with chat width\n    const handleChatWidthChange = (width)=>{\n        setChatWidth(width);\n        setSidebarWidth(width === \"45%\" ? \"45vw\" : \"45vw\");\n    };\n    const { sections, isLoading, error, setSections, setLoading, setError } = (0,_stores_businessSectionStore__WEBPACK_IMPORTED_MODULE_8__.useBusinessSectionStore)();\n    const { selectedItem, itemDetails, setSelectedItem, setItemDetails, fetchItemDetails } = useBusinessItemStoreEnhanced();\n    // Dynamic sidebar width based on selected item\n    const currentSidebarWidth = selectedItem ? \"30vw\" : sidebarWidth;\n    // Reset to default view on page load/refresh\n    (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)({\n        \"ProjectDetailPage.useEffect\": ()=>{\n            setSelectedItem(null);\n            setItemDetails([]);\n        }\n    }[\"ProjectDetailPage.useEffect\"], [\n        setSelectedItem,\n        setItemDetails\n    ]);\n    // Load business sections on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)({\n        \"ProjectDetailPage.useEffect\": ()=>{\n            const loadBusinessSections = {\n                \"ProjectDetailPage.useEffect.loadBusinessSections\": async ()=>{\n                    try {\n                        setLoading(true);\n                        setError(null);\n                        const data = await (0,_lib_businessSectionsDataNew__WEBPACK_IMPORTED_MODULE_7__.fetchBusinessSectionsNew)(projectId);\n                        setSections(data);\n                    } catch (err) {\n                        setError(\"Failed to load business sections\");\n                        console.error(\"Error loading business sections:\", err);\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"ProjectDetailPage.useEffect.loadBusinessSections\"];\n            loadBusinessSections();\n        }\n    }[\"ProjectDetailPage.useEffect\"], [\n        setSections,\n        setLoading,\n        setError\n    ]);\n    // Handle business item selection - show detail view\n    const handleBusinessItemClick = (item)=>{\n        setSelectedItem(item);\n        // Clear item details since we're using the enhanced view with comprehensive data\n        setItemDetails([]);\n    };\n    // Handle back to items\n    const handleBackToItems = ()=>{\n        setSelectedItem(null);\n    };\n    // Resize functionality\n    const resizable = (0,_hooks_useResizable__WEBPACK_IMPORTED_MODULE_6__.useResizable)({\n        initialWidth: sidebarWidth,\n        minWidthPercent: 10,\n        maxWidthPercent: 70,\n        onWidthChange: {\n            \"ProjectDetailPage.useResizable[resizable]\": (width)=>{\n                // Convert percentage to viewport width\n                const widthPercent = parseFloat(width.replace(\"%\", \"\"));\n                const vwWidth = \"\".concat(widthPercent, \"vw\");\n                setSidebarWidth(vwWidth);\n                // Update chatWidth to match sidebarWidth for consistency\n                if (widthPercent <= 70) {\n                    setChatWidth(\"45%\");\n                } else {\n                    setChatWidth(\"45%\");\n                }\n            }\n        }[\"ProjectDetailPage.useResizable[resizable]\"],\n        onCollapse: {\n            \"ProjectDetailPage.useResizable[resizable]\": ()=>{\n                setIsChatCollapsed(true);\n            }\n        }[\"ProjectDetailPage.useResizable[resizable]\"]\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-screen overflow-hidden\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_3__.SidebarProvider, {\n            defaultOpen: true,\n            style: {\n                \"--sidebar-width\": currentSidebarWidth,\n                \"--sidebar-width-mobile\": \"18rem\",\n                \"--sidebar-width-icon\": \"5rem\",\n                transition: \"all 0.3s ease-in-out\"\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_project_sidebar__WEBPACK_IMPORTED_MODULE_2__.ProjectSidebar, {\n                    projectId: projectId,\n                    chatWidth: chatWidth,\n                    setChatWidth: handleChatWidthChange,\n                    isChatCollapsed: isChatCollapsed,\n                    setIsChatCollapsed: setIsChatCollapsed,\n                    selectedBusinessItem: selectedItem,\n                    showDescription: !!selectedItem,\n                    onBackToProject: handleBackToItems,\n                    resizeHandle: {\n                        onMouseDown: resizable.handleMouseDown,\n                        isDragging: resizable.isDragging\n                    }\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/app/projects/[id]/page.tsx\",\n                    lineNumber: 139,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_3__.SidebarInset, {\n                    className: \"flex-1 flex flex-col h-screen overflow-hidden\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_project__WEBPACK_IMPORTED_MODULE_1__.ProjectHeader, {\n                            activeContent: activeContent,\n                            setActiveContent: setActiveContent,\n                            selectedBusinessItem: selectedItem,\n                            onBackToItems: handleBackToItems\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/app/projects/[id]/page.tsx\",\n                            lineNumber: 154,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_project__WEBPACK_IMPORTED_MODULE_1__.ProjectMainContent, {\n                            activeContent: activeContent,\n                            setActiveContent: setActiveContent,\n                            mockDraftItems: mockDraftItems,\n                            mockFileItems: mockFileItems,\n                            selectedItem: selectedItem,\n                            itemDetails: itemDetails,\n                            sections: sections,\n                            isLoading: isLoading,\n                            error: error,\n                            onBusinessItemClick: handleBusinessItemClick,\n                            onBackToItems: handleBackToItems\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/app/projects/[id]/page.tsx\",\n                            lineNumber: 161,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"fixed bottom-0 h-16 bg-gradient-to-t from-gray-50 via-gray-50/80 to-transparent dark:from-gray-900 dark:via-gray-900/80 dark:to-transparent pointer-events-none z-10\",\n                            style: {\n                                left: \"var(--sidebar-width, 45vw)\",\n                                right: \"0\"\n                            }\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/app/projects/[id]/page.tsx\",\n                            lineNumber: 176,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"fixed bottom-0 h-8 backdrop-blur-sm pointer-events-none z-10\",\n                            style: {\n                                left: \"var(--sidebar-width, 45vw)\",\n                                right: \"0\"\n                            }\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/app/projects/[id]/page.tsx\",\n                            lineNumber: 183,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/app/projects/[id]/page.tsx\",\n                    lineNumber: 153,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Data/new era/siift-next/src/app/projects/[id]/page.tsx\",\n            lineNumber: 128,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Data/new era/siift-next/src/app/projects/[id]/page.tsx\",\n        lineNumber: 127,\n        columnNumber: 5\n    }, this);\n}\n_s(ProjectDetailPage, \"VVb0RU0eT8v9bmztSSyYjcIe8Ik=\", true, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_4__.useParams,\n        _stores_businessSectionStore__WEBPACK_IMPORTED_MODULE_8__.useBusinessSectionStore,\n        _hooks_useResizable__WEBPACK_IMPORTED_MODULE_6__.useResizable\n    ];\n});\n_c = ProjectDetailPage;\nvar _c;\n$RefreshReg$(_c, \"ProjectDetailPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/projects/[id]/page.tsx\n"));

/***/ })

});