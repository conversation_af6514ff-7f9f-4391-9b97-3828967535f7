"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/projects/[id]/page",{

/***/ "(app-pages-browser)/./src/app/projects/[id]/page.tsx":
/*!****************************************!*\
  !*** ./src/app/projects/[id]/page.tsx ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProjectDetailPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_project__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/project */ \"(app-pages-browser)/./src/components/project/index.ts\");\n/* harmony import */ var _components_project_sidebar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/project-sidebar */ \"(app-pages-browser)/./src/components/project-sidebar.tsx\");\n/* harmony import */ var _components_ui_sidebar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/sidebar */ \"(app-pages-browser)/./src/components/ui/sidebar.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _hooks_useResizable__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../../hooks/useResizable */ \"(app-pages-browser)/./src/hooks/useResizable.ts\");\n/* harmony import */ var _lib_businessSectionsDataNew__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../../lib/businessSectionsDataNew */ \"(app-pages-browser)/./src/lib/businessSectionsDataNew.ts\");\n/* harmony import */ var _stores_businessSectionStore__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../../../stores/businessSectionStore */ \"(app-pages-browser)/./src/stores/businessSectionStore.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n// Mock data\nconst mockDraftItems = [\n    {\n        id: 1,\n        title: \"Project proposal\",\n        status: \"draft\",\n        lastModified: \"2 hours ago\"\n    },\n    {\n        id: 2,\n        title: \"Design brief\",\n        status: \"draft\",\n        lastModified: \"1 day ago\"\n    },\n    {\n        id: 3,\n        title: \"Technical specs\",\n        status: \"draft\",\n        lastModified: \"3 days ago\"\n    }\n];\n// Mock file items data\nconst mockFileItems = [\n    {\n        id: 1,\n        title: \"logo.svg\",\n        type: \"image\",\n        size: \"24KB\"\n    },\n    {\n        id: 2,\n        title: \"wireframes.fig\",\n        type: \"design\",\n        size: \"1.2MB\"\n    },\n    {\n        id: 3,\n        title: \"requirements.pdf\",\n        type: \"document\",\n        size: \"156KB\"\n    },\n    {\n        id: 4,\n        title: \"styleguide.pdf\",\n        type: \"document\",\n        size: \"2.1MB\"\n    }\n];\nfunction ProjectDetailPage() {\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.useParams)();\n    const projectId = params.id;\n    const [activeContent, setActiveContent] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(null);\n    const [chatWidth, setChatWidth] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(\"45%\");\n    const [isChatCollapsed, setIsChatCollapsed] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    const [sidebarWidth, setSidebarWidth] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(\"45vw\");\n    // Sync sidebar width with chat width\n    const handleChatWidthChange = (width)=>{\n        setChatWidth(width);\n        setSidebarWidth(width === \"45%\" ? \"45vw\" : \"45vw\");\n    };\n    const { sections, isLoading, error, setSections, setLoading, setError } = (0,_stores_businessSectionStore__WEBPACK_IMPORTED_MODULE_8__.useBusinessSectionStore)();\n    const { selectedItem, itemDetails, setSelectedItem, setItemDetails } = useBusinessItemStore();\n    // Dynamic sidebar width based on selected item\n    const currentSidebarWidth = selectedItem ? \"30vw\" : sidebarWidth;\n    // Reset to default view on page load/refresh\n    (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)({\n        \"ProjectDetailPage.useEffect\": ()=>{\n            setSelectedItem(null);\n            setItemDetails([]);\n        }\n    }[\"ProjectDetailPage.useEffect\"], [\n        setSelectedItem,\n        setItemDetails\n    ]);\n    // Load business sections on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)({\n        \"ProjectDetailPage.useEffect\": ()=>{\n            const loadBusinessSections = {\n                \"ProjectDetailPage.useEffect.loadBusinessSections\": async ()=>{\n                    try {\n                        setLoading(true);\n                        setError(null);\n                        const data = await (0,_lib_businessSectionsDataNew__WEBPACK_IMPORTED_MODULE_7__.fetchBusinessSectionsNew)(projectId);\n                        setSections(data);\n                    } catch (err) {\n                        setError(\"Failed to load business sections\");\n                        console.error(\"Error loading business sections:\", err);\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"ProjectDetailPage.useEffect.loadBusinessSections\"];\n            loadBusinessSections();\n        }\n    }[\"ProjectDetailPage.useEffect\"], [\n        setSections,\n        setLoading,\n        setError\n    ]);\n    // Handle business item selection - show detail view\n    const handleBusinessItemClick = (item)=>{\n        setSelectedItem(item);\n        // Clear item details since we're using the enhanced view with comprehensive data\n        setItemDetails([]);\n    };\n    // Handle back to items\n    const handleBackToItems = ()=>{\n        setSelectedItem(null);\n    };\n    // Resize functionality\n    const resizable = (0,_hooks_useResizable__WEBPACK_IMPORTED_MODULE_6__.useResizable)({\n        initialWidth: sidebarWidth,\n        minWidthPercent: 10,\n        maxWidthPercent: 70,\n        onWidthChange: {\n            \"ProjectDetailPage.useResizable[resizable]\": (width)=>{\n                // Convert percentage to viewport width\n                const widthPercent = parseFloat(width.replace(\"%\", \"\"));\n                const vwWidth = \"\".concat(widthPercent, \"vw\");\n                setSidebarWidth(vwWidth);\n                // Update chatWidth to match sidebarWidth for consistency\n                if (widthPercent <= 70) {\n                    setChatWidth(\"45%\");\n                } else {\n                    setChatWidth(\"45%\");\n                }\n            }\n        }[\"ProjectDetailPage.useResizable[resizable]\"],\n        onCollapse: {\n            \"ProjectDetailPage.useResizable[resizable]\": ()=>{\n                setIsChatCollapsed(true);\n            }\n        }[\"ProjectDetailPage.useResizable[resizable]\"]\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-screen overflow-hidden\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_3__.SidebarProvider, {\n            defaultOpen: true,\n            style: {\n                \"--sidebar-width\": currentSidebarWidth,\n                \"--sidebar-width-mobile\": \"18rem\",\n                \"--sidebar-width-icon\": \"5rem\",\n                transition: \"all 0.3s ease-in-out\"\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_project_sidebar__WEBPACK_IMPORTED_MODULE_2__.ProjectSidebar, {\n                    projectId: projectId,\n                    chatWidth: chatWidth,\n                    setChatWidth: handleChatWidthChange,\n                    isChatCollapsed: isChatCollapsed,\n                    setIsChatCollapsed: setIsChatCollapsed,\n                    selectedBusinessItem: selectedItem,\n                    showDescription: !!selectedItem,\n                    onBackToProject: handleBackToItems,\n                    resizeHandle: {\n                        onMouseDown: resizable.handleMouseDown,\n                        isDragging: resizable.isDragging\n                    }\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/app/projects/[id]/page.tsx\",\n                    lineNumber: 134,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_3__.SidebarInset, {\n                    className: \"flex-1 flex flex-col h-screen overflow-hidden\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_project__WEBPACK_IMPORTED_MODULE_1__.ProjectHeader, {\n                            activeContent: activeContent,\n                            setActiveContent: setActiveContent,\n                            selectedBusinessItem: selectedItem,\n                            onBackToItems: handleBackToItems\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/app/projects/[id]/page.tsx\",\n                            lineNumber: 149,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_project__WEBPACK_IMPORTED_MODULE_1__.ProjectMainContent, {\n                            activeContent: activeContent,\n                            setActiveContent: setActiveContent,\n                            mockDraftItems: mockDraftItems,\n                            mockFileItems: mockFileItems,\n                            selectedItem: selectedItem,\n                            itemDetails: itemDetails,\n                            sections: sections,\n                            isLoading: isLoading,\n                            error: error,\n                            onBusinessItemClick: handleBusinessItemClick,\n                            onBackToItems: handleBackToItems\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/app/projects/[id]/page.tsx\",\n                            lineNumber: 156,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"fixed bottom-0 h-16 bg-gradient-to-t from-gray-50 via-gray-50/80 to-transparent dark:from-gray-900 dark:via-gray-900/80 dark:to-transparent pointer-events-none z-10\",\n                            style: {\n                                left: \"var(--sidebar-width, 45vw)\",\n                                right: \"0\"\n                            }\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/app/projects/[id]/page.tsx\",\n                            lineNumber: 171,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"fixed bottom-0 h-8 backdrop-blur-sm pointer-events-none z-10\",\n                            style: {\n                                left: \"var(--sidebar-width, 45vw)\",\n                                right: \"0\"\n                            }\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/app/projects/[id]/page.tsx\",\n                            lineNumber: 178,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/app/projects/[id]/page.tsx\",\n                    lineNumber: 148,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Data/new era/siift-next/src/app/projects/[id]/page.tsx\",\n            lineNumber: 123,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Data/new era/siift-next/src/app/projects/[id]/page.tsx\",\n        lineNumber: 122,\n        columnNumber: 5\n    }, this);\n}\n_s(ProjectDetailPage, \"ZRFTZ7FQRtw8sqsm2d34g9VD/0U=\", true, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_4__.useParams,\n        _stores_businessSectionStore__WEBPACK_IMPORTED_MODULE_8__.useBusinessSectionStore,\n        _hooks_useResizable__WEBPACK_IMPORTED_MODULE_6__.useResizable\n    ];\n});\n_c = ProjectDetailPage;\nvar _c;\n$RefreshReg$(_c, \"ProjectDetailPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/projects/[id]/page.tsx\n"));

/***/ })

});