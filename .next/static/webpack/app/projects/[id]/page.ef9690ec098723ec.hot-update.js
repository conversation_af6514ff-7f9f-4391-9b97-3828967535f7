"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/projects/[id]/page",{

/***/ "(app-pages-browser)/./src/components/business-item-hybrid-view.tsx":
/*!******************************************************!*\
  !*** ./src/components/business-item-hybrid-view.tsx ***!
  \******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BusinessItemHybridView: () => (/* binding */ BusinessItemHybridView)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_collapsible__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/collapsible */ \"(app-pages-browser)/./src/components/ui/collapsible.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./src/components/ui/separator.tsx\");\n/* harmony import */ var _components_ui_tooltip__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/tooltip */ \"(app-pages-browser)/./src/components/ui/tooltip.tsx\");\n/* harmony import */ var _data_businessItemsData__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/data/businessItemsData */ \"(app-pages-browser)/./src/data/businessItemsData.ts\");\n/* harmony import */ var _lib_dependencyManager__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/dependencyManager */ \"(app-pages-browser)/./src/lib/dependencyManager.ts\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_ChevronDown_ChevronUp_Clock_HelpCircle_Lightbulb_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,ChevronDown,ChevronUp,Clock,HelpCircle,Lightbulb,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_ChevronDown_ChevronUp_Clock_HelpCircle_Lightbulb_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,ChevronDown,ChevronUp,Clock,HelpCircle,Lightbulb,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_ChevronDown_ChevronUp_Clock_HelpCircle_Lightbulb_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,ChevronDown,ChevronUp,Clock,HelpCircle,Lightbulb,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/lightbulb.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_ChevronDown_ChevronUp_Clock_HelpCircle_Lightbulb_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,ChevronDown,ChevronUp,Clock,HelpCircle,Lightbulb,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_ChevronDown_ChevronUp_Clock_HelpCircle_Lightbulb_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,ChevronDown,ChevronUp,Clock,HelpCircle,Lightbulb,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_ChevronDown_ChevronUp_Clock_HelpCircle_Lightbulb_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,ChevronDown,ChevronUp,Clock,HelpCircle,Lightbulb,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-question-mark.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_ChevronDown_ChevronUp_Clock_HelpCircle_Lightbulb_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,ChevronDown,ChevronUp,Clock,HelpCircle,Lightbulb,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-up.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_ChevronDown_ChevronUp_Clock_HelpCircle_Lightbulb_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,ChevronDown,ChevronUp,Clock,HelpCircle,Lightbulb,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_ChevronDown_ChevronUp_Clock_HelpCircle_Lightbulb_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,ChevronDown,ChevronUp,Clock,HelpCircle,Lightbulb,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var _business_item_table__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./business-item-table */ \"(app-pages-browser)/./src/components/business-item-table.tsx\");\n/* __next_internal_client_entry_do_not_use__ BusinessItemHybridView auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction BusinessItemHybridView(param) {\n    let { selectedItem, itemDetails, onBackToItems, projectName = \"your project\" } = param;\n    _s();\n    const [isInfoExpanded, setIsInfoExpanded] = (0,react__WEBPACK_IMPORTED_MODULE_8__.useState)(false);\n    // Get comprehensive data for the selected item\n    const itemData = _data_businessItemsData__WEBPACK_IMPORTED_MODULE_6__.businessItemsData.find((item)=>item.id === selectedItem.id);\n    const dependencyCheck = selectedItem.dependencies ? (0,_lib_dependencyManager__WEBPACK_IMPORTED_MODULE_7__.checkItemDependencies)(selectedItem.id) : null;\n    // Get dependent items (items that depend on this one)\n    const getStatusColor = (status)=>{\n        switch(status){\n            case \"confirmed\":\n                return \"text-green-600 dark:text-green-400 bg-green-50 dark:bg-green-900/20\";\n            case \"action\":\n                return \"text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20\";\n            case \"idea\":\n                return \"text-yellow-600 dark:text-yellow-400 bg-yellow-50 dark:bg-yellow-900/20\";\n            case \"unproven\":\n                return \"text-gray-600 dark:text-gray-400 bg-gray-50 dark:bg-gray-800\";\n            default:\n                return \"text-gray-600 dark:text-gray-400 bg-gray-50 dark:bg-gray-800\";\n        }\n    };\n    const getStatusIcon = (status)=>{\n        switch(status){\n            case \"confirmed\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_ChevronDown_ChevronUp_Clock_HelpCircle_Lightbulb_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                    lineNumber: 80,\n                    columnNumber: 16\n                }, this);\n            case \"action\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_ChevronDown_ChevronUp_Clock_HelpCircle_Lightbulb_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                    lineNumber: 82,\n                    columnNumber: 16\n                }, this);\n            case \"idea\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_ChevronDown_ChevronUp_Clock_HelpCircle_Lightbulb_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                    lineNumber: 84,\n                    columnNumber: 16\n                }, this);\n            case \"unproven\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_ChevronDown_ChevronUp_Clock_HelpCircle_Lightbulb_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                    lineNumber: 86,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_ChevronDown_ChevronUp_Clock_HelpCircle_Lightbulb_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                    lineNumber: 88,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_5__.TooltipProvider, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            variant: \"ghost\",\n                                            size: \"sm\",\n                                            onClick: onBackToItems,\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_ChevronDown_ChevronUp_Clock_HelpCircle_Lightbulb_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                    lineNumber: 105,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Back to Items\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                            lineNumber: 99,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"text-2xl font-bold\",\n                                                    children: selectedItem.title\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                    lineNumber: 109,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_1__.Badge, {\n                                                    className: \"\".concat(getStatusColor(selectedItem.status), \" border-0\"),\n                                                    children: [\n                                                        getStatusIcon(selectedItem.status),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"ml-1 capitalize\",\n                                                            children: selectedItem.status\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                            lineNumber: 114,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                    lineNumber: 110,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                            lineNumber: 108,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                    lineNumber: 98,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_5__.Tooltip, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_5__.TooltipTrigger, {\n                                                    asChild: true,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        variant: \"ghost\",\n                                                        size: \"sm\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_ChevronDown_ChevronUp_Clock_HelpCircle_Lightbulb_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                            lineNumber: 123,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                        lineNumber: 122,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                    lineNumber: 121,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_5__.TooltipContent, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: \"Click the expand button to see detailed guidance and examples\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                        lineNumber: 127,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                    lineNumber: 126,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                            lineNumber: 120,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_3__.Collapsible, {\n                                            open: isInfoExpanded,\n                                            onOpenChange: setIsInfoExpanded,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_3__.CollapsibleTrigger, {\n                                                asChild: true,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    variant: \"outline\",\n                                                    size: \"sm\",\n                                                    className: \"flex items-center gap-2\",\n                                                    children: isInfoExpanded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_ChevronDown_ChevronUp_Clock_HelpCircle_Lightbulb_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                                lineNumber: 146,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            \"Hide Details\"\n                                                        ]\n                                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_ChevronDown_ChevronUp_Clock_HelpCircle_Lightbulb_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                                lineNumber: 151,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            \"Show Details\"\n                                                        ]\n                                                    }, void 0, true)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                    lineNumber: 139,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                lineNumber: 138,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                            lineNumber: 134,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                    lineNumber: 119,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                            lineNumber: 97,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3\",\n                            children: [\n                                (itemData === null || itemData === void 0 ? void 0 : itemData.question) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-blue-600 dark:text-blue-400\",\n                                        children: itemData.question.replace(\"{PROJECT NAME}\", projectName)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                        lineNumber: 165,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                    lineNumber: 164,\n                                    columnNumber: 15\n                                }, this),\n                                ((itemData === null || itemData === void 0 ? void 0 : itemData.description) || selectedItem.description) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-muted-foreground leading-relaxed\",\n                                        children: (itemData === null || itemData === void 0 ? void 0 : itemData.description) || selectedItem.description\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                        lineNumber: 173,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                    lineNumber: 172,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                            lineNumber: 162,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                    lineNumber: 96,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_3__.Collapsible, {\n                    open: isInfoExpanded,\n                    onOpenChange: setIsInfoExpanded,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_3__.CollapsibleContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-6 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg border\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"lg:col-span-2 space-y-4\",\n                                        children: [\n                                            ((itemData === null || itemData === void 0 ? void 0 : itemData.description) || selectedItem.description) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium text-sm text-muted-foreground mb-2\",\n                                                        children: \"Description\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                        lineNumber: 191,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm leading-relaxed\",\n                                                        children: (itemData === null || itemData === void 0 ? void 0 : itemData.description) || selectedItem.description\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                        lineNumber: 194,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                lineNumber: 190,\n                                                columnNumber: 21\n                                            }, this),\n                                            (itemData === null || itemData === void 0 ? void 0 : itemData.question) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium text-sm text-muted-foreground mb-2\",\n                                                        children: \"Key Question\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                        lineNumber: 203,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm bg-blue-50 dark:bg-blue-900/20 p-3 rounded-lg border-l-4 border-blue-500\",\n                                                        children: itemData.question.replace(\"{PROJECT NAME}\", \"your project\")\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                        lineNumber: 206,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                lineNumber: 202,\n                                                columnNumber: 21\n                                            }, this),\n                                            (itemData === null || itemData === void 0 ? void 0 : itemData.guidance) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium text-sm text-muted-foreground mb-2\",\n                                                        children: \"Guidance\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                        lineNumber: 218,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-muted-foreground leading-relaxed\",\n                                                        children: itemData.guidance\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                        lineNumber: 221,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                lineNumber: 217,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium text-sm text-muted-foreground mb-2\",\n                                                        children: \"Sample Data\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                        lineNumber: 229,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-muted-foreground\",\n                                                        children: \"Sample responses have been loaded into the table below. You can edit them or add your own entries.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                        lineNumber: 232,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                lineNumber: 228,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                        lineNumber: 187,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium text-sm text-muted-foreground mb-2\",\n                                                        children: \"Progress Metrics\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                        lineNumber: 243,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-3 gap-2 text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-2 bg-yellow-50 dark:bg-yellow-900/20 rounded\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm font-bold text-yellow-600\",\n                                                                        children: selectedItem.ideas\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                                        lineNumber: 248,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-xs text-muted-foreground\",\n                                                                        children: \"Ideas\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                                        lineNumber: 251,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                                lineNumber: 247,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-2 bg-blue-50 dark:bg-blue-900/20 rounded\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm font-bold text-blue-600\",\n                                                                        children: selectedItem.actions\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                                        lineNumber: 256,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-xs text-muted-foreground\",\n                                                                        children: \"Actions\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                                        lineNumber: 259,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                                lineNumber: 255,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-2 bg-green-50 dark:bg-green-900/20 rounded\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm font-bold text-green-600\",\n                                                                        children: selectedItem.results\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                                        lineNumber: 264,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-xs text-muted-foreground\",\n                                                                        children: \"Results\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                                        lineNumber: 267,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                                lineNumber: 263,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                        lineNumber: 246,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                lineNumber: 242,\n                                                columnNumber: 19\n                                            }, this),\n                                            selectedItem.dependencies && selectedItem.dependencies.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium text-sm text-muted-foreground mb-2\",\n                                                        children: \"Dependencies\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                        lineNumber: 278,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    dependencyCheck && !dependencyCheck.isValid && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2 p-2 bg-red-50 dark:bg-red-900/20 rounded mb-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_ChevronDown_ChevronUp_Clock_HelpCircle_Lightbulb_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                className: \"h-3 w-3 text-red-600\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                                lineNumber: 283,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs text-red-600\",\n                                                                children: \"Not satisfied\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                                lineNumber: 284,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                        lineNumber: 282,\n                                                        columnNumber: 27\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-1\",\n                                                        children: [\n                                                            selectedItem.dependencies.slice(0, 3).map((depId, index)=>{\n                                                                const depItem = _data_businessItemsData__WEBPACK_IMPORTED_MODULE_6__.businessItemsData.find((item)=>item.id === depId);\n                                                                const isCompleted = (depItem === null || depItem === void 0 ? void 0 : depItem.status) === \"confirmed\";\n                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2 text-xs\",\n                                                                    children: [\n                                                                        isCompleted ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_ChevronDown_ChevronUp_Clock_HelpCircle_Lightbulb_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                            className: \"h-3 w-3 text-green-600\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                                            lineNumber: 305,\n                                                                            columnNumber: 37\n                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_ChevronDown_ChevronUp_Clock_HelpCircle_Lightbulb_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                            className: \"h-3 w-3 text-gray-400\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                                            lineNumber: 307,\n                                                                            columnNumber: 37\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: isCompleted ? \"text-green-600\" : \"text-gray-600\",\n                                                                            children: (depItem === null || depItem === void 0 ? void 0 : depItem.title) || depId\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                                            lineNumber: 309,\n                                                                            columnNumber: 35\n                                                                        }, this)\n                                                                    ]\n                                                                }, index, true, {\n                                                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                                    lineNumber: 300,\n                                                                    columnNumber: 33\n                                                                }, this);\n                                                            }),\n                                                            selectedItem.dependencies.length > 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-muted-foreground\",\n                                                                children: [\n                                                                    \"+\",\n                                                                    selectedItem.dependencies.length - 3,\n                                                                    \" more\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                                lineNumber: 322,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                        lineNumber: 289,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                lineNumber: 277,\n                                                columnNumber: 23\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium text-sm text-muted-foreground mb-2\",\n                                                        children: \"Metadata\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                        lineNumber: 332,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-1 text-xs\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-muted-foreground\",\n                                                                        children: \"Category:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                                        lineNumber: 337,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: selectedItem.category\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                                        lineNumber: 338,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                                lineNumber: 336,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-muted-foreground\",\n                                                                        children: \"Input Type:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                                        lineNumber: 341,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: selectedItem.inputType\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                                        lineNumber: 344,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                                lineNumber: 340,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-muted-foreground\",\n                                                                        children: \"Order:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                                        lineNumber: 347,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: selectedItem.order\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                                        lineNumber: 348,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                                lineNumber: 346,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                        lineNumber: 335,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                                lineNumber: 331,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                        lineNumber: 240,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                                lineNumber: 185,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                            lineNumber: 184,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                        lineNumber: 183,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                    lineNumber: 182,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_4__.Separator, {}, void 0, false, {\n                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                    lineNumber: 359,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold mb-4\",\n                            children: \"Action Items & Results\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                            lineNumber: 363,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_business_item_table__WEBPACK_IMPORTED_MODULE_9__.BusinessItemTable, {\n                            itemDetails: itemDetails,\n                            selectedBusinessItem: selectedItem,\n                            onBackToItems: onBackToItems\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                            lineNumber: 364,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                    lineNumber: 362,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n            lineNumber: 94,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n        lineNumber: 93,\n        columnNumber: 5\n    }, this);\n}\n_s(BusinessItemHybridView, \"xhHbMehHQ3yyT7gl75aBEcicsE0=\");\n_c = BusinessItemHybridView;\nvar _c;\n$RefreshReg$(_c, \"BusinessItemHybridView\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/business-item-hybrid-view.tsx\n"));

/***/ })

});