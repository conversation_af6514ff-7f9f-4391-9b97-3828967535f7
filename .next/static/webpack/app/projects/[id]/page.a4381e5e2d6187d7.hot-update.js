"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/projects/[id]/page",{

/***/ "(app-pages-browser)/./src/mockdata/businessItemQuestions.ts":
/*!***********************************************!*\
  !*** ./src/mockdata/businessItemQuestions.ts ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BUSINESS_ITEM_QUESTIONS: () => (/* binding */ BUSINESS_ITEM_QUESTIONS),\n/* harmony export */   getBusinessItemQA: () => (/* binding */ getBusinessItemQA)\n/* harmony export */ });\n/* harmony import */ var _data_businessItemsData__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/data/businessItemsData */ \"(app-pages-browser)/./src/data/businessItemsData.ts\");\n/**\n * Mock data for business item questions and answers\n * Each business item type has specific questions and default answers\n */ \nconst BUSINESS_ITEM_QUESTIONS = {\n    // Problem-related items\n    problem: {\n        question: \"What specific problem are we trying to solve?\",\n        defaultAnswer: \"Define the core problem that needs to be addressed and its impact on the business.\",\n        getAnswer: (item)=>(item === null || item === void 0 ? void 0 : item.title) ? \"Problem identified: \".concat(item.title) : \"Define the core problem that needs to be addressed and its impact on the business.\"\n    },\n    // Audience-related items\n    audience: {\n        question: \"Who is our target audience for this initiative?\",\n        defaultAnswer: \"Identify the specific user groups, demographics, and stakeholders who will be affected.\",\n        getAnswer: (item)=>(item === null || item === void 0 ? void 0 : item.title) ? \"Target audience: \".concat(item.title) : \"Identify the specific user groups, demographics, and stakeholders who will be affected.\"\n    },\n    // Alternatives-related items\n    alternatives: {\n        question: \"What alternative solutions have we considered?\",\n        defaultAnswer: \"List and evaluate different approaches, technologies, or strategies that could address this need.\",\n        getAnswer: (item)=>(item === null || item === void 0 ? void 0 : item.title) ? \"Alternative approach: \".concat(item.title) : \"List and evaluate different approaches, technologies, or strategies that could address this need.\"\n    },\n    // Solution-related items\n    solution: {\n        question: \"What is our proposed solution approach?\",\n        defaultAnswer: \"Describe the recommended solution, its key features, and implementation strategy.\",\n        getAnswer: (item)=>(item === null || item === void 0 ? void 0 : item.title) ? \"Proposed solution: \".concat(item.title) : \"Describe the recommended solution, its key features, and implementation strategy.\"\n    },\n    // Market-related items\n    market: {\n        question: \"What market opportunity does this address?\",\n        defaultAnswer: \"Define the market size, competitive landscape, and business opportunity.\",\n        getAnswer: (item)=>(item === null || item === void 0 ? void 0 : item.title) ? \"Market opportunity: \".concat(item.title) : \"Define the market size, competitive landscape, and business opportunity.\"\n    },\n    // Technology-related items\n    technology: {\n        question: \"What technology stack and architecture will we use?\",\n        defaultAnswer: \"Outline the technical requirements, tools, platforms, and infrastructure needed.\",\n        getAnswer: (item)=>(item === null || item === void 0 ? void 0 : item.title) ? \"Technology approach: \".concat(item.title) : \"Outline the technical requirements, tools, platforms, and infrastructure needed.\"\n    },\n    // Timeline-related items\n    timeline: {\n        question: \"What is our project timeline and key milestones?\",\n        defaultAnswer: \"Define project phases, deadlines, dependencies, and critical path items.\",\n        getAnswer: (item)=>(item === null || item === void 0 ? void 0 : item.title) ? \"Timeline focus: \".concat(item.title) : \"Define project phases, deadlines, dependencies, and critical path items.\"\n    },\n    // Budget-related items\n    budget: {\n        question: \"What are the financial requirements and ROI expectations?\",\n        defaultAnswer: \"Specify budget allocation, cost breakdown, and expected return on investment.\",\n        getAnswer: (item)=>(item === null || item === void 0 ? void 0 : item.title) ? \"Budget consideration: \".concat(item.title) : \"Specify budget allocation, cost breakdown, and expected return on investment.\"\n    },\n    // Risk-related items\n    risk: {\n        question: \"What are the key risks and mitigation strategies?\",\n        defaultAnswer: \"Identify potential risks, their impact, probability, and mitigation plans.\",\n        getAnswer: (item)=>(item === null || item === void 0 ? void 0 : item.title) ? \"Risk factor: \".concat(item.title) : \"Identify potential risks, their impact, probability, and mitigation plans.\"\n    },\n    // Success metrics\n    metrics: {\n        question: \"How will we measure success and track progress?\",\n        defaultAnswer: \"Define KPIs, success criteria, measurement methods, and reporting frequency.\",\n        getAnswer: (item)=>(item === null || item === void 0 ? void 0 : item.title) ? \"Success metric: \".concat(item.title) : \"Define KPIs, success criteria, measurement methods, and reporting frequency.\"\n    },\n    // Team and resources\n    team: {\n        question: \"What team structure and resources do we need?\",\n        defaultAnswer: \"Specify roles, responsibilities, skill requirements, and resource allocation.\",\n        getAnswer: (item)=>(item === null || item === void 0 ? void 0 : item.title) ? \"Team focus: \".concat(item.title) : \"Specify roles, responsibilities, skill requirements, and resource allocation.\"\n    },\n    // Default fallback for any other item types\n    default: {\n        question: \"What is the main objective of this business item?\",\n        defaultAnswer: \"Define the core purpose and expected outcomes for this business initiative.\",\n        getAnswer: (item)=>(item === null || item === void 0 ? void 0 : item.title) ? \"This item focuses on: \".concat(item.title) : \"Define the core purpose and expected outcomes for this business initiative.\"\n    }\n};\n/**\n * Helper function to get question and answer for a business item\n * @param item - The business item object\n * @returns Object with question and answer\n */ function getBusinessItemQA(item) {\n    if (!item) {\n        return {\n            question: BUSINESS_ITEM_QUESTIONS.default.question,\n            answer: BUSINESS_ITEM_QUESTIONS.default.defaultAnswer\n        };\n    }\n    // First, try to get comprehensive data from businessItemsData\n    const comprehensiveData = _data_businessItemsData__WEBPACK_IMPORTED_MODULE_0__.businessItemsData.find((dataItem)=>dataItem.id === item.id);\n    if (comprehensiveData) {\n        var _comprehensiveData_question;\n        // Use the comprehensive data with project name replacement\n        const question = ((_comprehensiveData_question = comprehensiveData.question) === null || _comprehensiveData_question === void 0 ? void 0 : _comprehensiveData_question.replace(\"{PROJECT NAME}\", \"Siift\")) || comprehensiveData.title;\n        const answer = comprehensiveData.description || \"Working on: \".concat(comprehensiveData.title);\n        return {\n            question,\n            answer\n        };\n    }\n    // Fallback to the old logic if no comprehensive data found\n    const itemType = determineItemType(item);\n    const qaData = BUSINESS_ITEM_QUESTIONS[itemType] || BUSINESS_ITEM_QUESTIONS.default;\n    return {\n        question: qaData.question,\n        answer: qaData.getAnswer ? qaData.getAnswer(item) : qaData.defaultAnswer\n    };\n}\n/**\n * Determine the type of business item based on its properties\n * @param item - The business item object\n * @returns The item type string\n */ function determineItemType(item) {\n    if (!(item === null || item === void 0 ? void 0 : item.title)) return \"default\";\n    const title = item.title.toLowerCase();\n    // Check for keywords in the title to determine type\n    if (title.includes(\"problem\") || title.includes(\"issue\") || title.includes(\"challenge\")) {\n        return \"problem\";\n    }\n    if (title.includes(\"audience\") || title.includes(\"user\") || title.includes(\"customer\")) {\n        return \"audience\";\n    }\n    if (title.includes(\"alternative\") || title.includes(\"option\") || title.includes(\"approach\")) {\n        return \"alternatives\";\n    }\n    if (title.includes(\"solution\") || title.includes(\"resolve\") || title.includes(\"fix\")) {\n        return \"solution\";\n    }\n    if (title.includes(\"market\") || title.includes(\"opportunity\") || title.includes(\"competition\")) {\n        return \"market\";\n    }\n    if (title.includes(\"technology\") || title.includes(\"tech\") || title.includes(\"platform\")) {\n        return \"technology\";\n    }\n    if (title.includes(\"timeline\") || title.includes(\"schedule\") || title.includes(\"deadline\")) {\n        return \"timeline\";\n    }\n    if (title.includes(\"budget\") || title.includes(\"cost\") || title.includes(\"financial\")) {\n        return \"budget\";\n    }\n    if (title.includes(\"risk\") || title.includes(\"threat\") || title.includes(\"danger\")) {\n        return \"risk\";\n    }\n    if (title.includes(\"metric\") || title.includes(\"kpi\") || title.includes(\"measure\")) {\n        return \"metrics\";\n    }\n    if (title.includes(\"team\") || title.includes(\"resource\") || title.includes(\"staff\")) {\n        return \"team\";\n    }\n    return \"default\";\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/mockdata/businessItemQuestions.ts\n"));

/***/ })

});