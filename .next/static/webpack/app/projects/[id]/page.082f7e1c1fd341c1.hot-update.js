"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/projects/[id]/page",{

/***/ "(app-pages-browser)/./src/mockdata/businessItemQuestions.ts":
/*!***********************************************!*\
  !*** ./src/mockdata/businessItemQuestions.ts ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BUSINESS_ITEM_QUESTIONS: () => (/* binding */ BUSINESS_ITEM_QUESTIONS),\n/* harmony export */   getBusinessItemQA: () => (/* binding */ getBusinessItemQA)\n/* harmony export */ });\n/**\n * Mock data for business item questions and answers\n * Each business item type has specific questions and default answers\n */ const BUSINESS_ITEM_QUESTIONS = {\n    // Problem-related items\n    problem: {\n        question: \"What specific problem are we trying to solve?\",\n        defaultAnswer: \"Define the core problem that needs to be addressed and its impact on the business.\",\n        getAnswer: (item)=>(item === null || item === void 0 ? void 0 : item.title) ? \"Problem identified: \".concat(item.title) : \"Define the core problem that needs to be addressed and its impact on the business.\"\n    },\n    // Audience-related items\n    audience: {\n        question: \"Who is our target audience for this initiative?\",\n        defaultAnswer: \"Identify the specific user groups, demographics, and stakeholders who will be affected.\",\n        getAnswer: (item)=>(item === null || item === void 0 ? void 0 : item.title) ? \"Target audience: \".concat(item.title) : \"Identify the specific user groups, demographics, and stakeholders who will be affected.\"\n    },\n    // Alternatives-related items\n    alternatives: {\n        question: \"What alternative solutions have we considered?\",\n        defaultAnswer: \"List and evaluate different approaches, technologies, or strategies that could address this need.\",\n        getAnswer: (item)=>(item === null || item === void 0 ? void 0 : item.title) ? \"Alternative approach: \".concat(item.title) : \"List and evaluate different approaches, technologies, or strategies that could address this need.\"\n    },\n    // Solution-related items\n    solution: {\n        question: \"What is our proposed solution approach?\",\n        defaultAnswer: \"Describe the recommended solution, its key features, and implementation strategy.\",\n        getAnswer: (item)=>(item === null || item === void 0 ? void 0 : item.title) ? \"Proposed solution: \".concat(item.title) : \"Describe the recommended solution, its key features, and implementation strategy.\"\n    },\n    // Market-related items\n    market: {\n        question: \"What market opportunity does this address?\",\n        defaultAnswer: \"Define the market size, competitive landscape, and business opportunity.\",\n        getAnswer: (item)=>(item === null || item === void 0 ? void 0 : item.title) ? \"Market opportunity: \".concat(item.title) : \"Define the market size, competitive landscape, and business opportunity.\"\n    },\n    // Technology-related items\n    technology: {\n        question: \"What technology stack and architecture will we use?\",\n        defaultAnswer: \"Outline the technical requirements, tools, platforms, and infrastructure needed.\",\n        getAnswer: (item)=>(item === null || item === void 0 ? void 0 : item.title) ? \"Technology approach: \".concat(item.title) : \"Outline the technical requirements, tools, platforms, and infrastructure needed.\"\n    },\n    // Timeline-related items\n    timeline: {\n        question: \"What is our project timeline and key milestones?\",\n        defaultAnswer: \"Define project phases, deadlines, dependencies, and critical path items.\",\n        getAnswer: (item)=>(item === null || item === void 0 ? void 0 : item.title) ? \"Timeline focus: \".concat(item.title) : \"Define project phases, deadlines, dependencies, and critical path items.\"\n    },\n    // Budget-related items\n    budget: {\n        question: \"What are the financial requirements and ROI expectations?\",\n        defaultAnswer: \"Specify budget allocation, cost breakdown, and expected return on investment.\",\n        getAnswer: (item)=>(item === null || item === void 0 ? void 0 : item.title) ? \"Budget consideration: \".concat(item.title) : \"Specify budget allocation, cost breakdown, and expected return on investment.\"\n    },\n    // Risk-related items\n    risk: {\n        question: \"What are the key risks and mitigation strategies?\",\n        defaultAnswer: \"Identify potential risks, their impact, probability, and mitigation plans.\",\n        getAnswer: (item)=>(item === null || item === void 0 ? void 0 : item.title) ? \"Risk factor: \".concat(item.title) : \"Identify potential risks, their impact, probability, and mitigation plans.\"\n    },\n    // Success metrics\n    metrics: {\n        question: \"How will we measure success and track progress?\",\n        defaultAnswer: \"Define KPIs, success criteria, measurement methods, and reporting frequency.\",\n        getAnswer: (item)=>(item === null || item === void 0 ? void 0 : item.title) ? \"Success metric: \".concat(item.title) : \"Define KPIs, success criteria, measurement methods, and reporting frequency.\"\n    },\n    // Team and resources\n    team: {\n        question: \"What team structure and resources do we need?\",\n        defaultAnswer: \"Specify roles, responsibilities, skill requirements, and resource allocation.\",\n        getAnswer: (item)=>(item === null || item === void 0 ? void 0 : item.title) ? \"Team focus: \".concat(item.title) : \"Specify roles, responsibilities, skill requirements, and resource allocation.\"\n    },\n    // Default fallback for any other item types\n    default: {\n        question: \"What is the main objective of this business item?\",\n        defaultAnswer: \"Define the core purpose and expected outcomes for this business initiative.\",\n        getAnswer: (item)=>(item === null || item === void 0 ? void 0 : item.title) ? \"This item focuses on: \".concat(item.title) : \"Define the core purpose and expected outcomes for this business initiative.\"\n    }\n};\n/**\n * Helper function to get question and answer for a business item\n * @param item - The business item object\n * @returns Object with question and answer\n */ function getBusinessItemQA(item) {\n    if (!item) {\n        return {\n            question: BUSINESS_ITEM_QUESTIONS.default.question,\n            answer: BUSINESS_ITEM_QUESTIONS.default.defaultAnswer\n        };\n    }\n    // First, try to get comprehensive data from businessItemsData\n    const comprehensiveData = businessItemsData.find((dataItem)=>dataItem.id === item.id);\n    if (comprehensiveData) {\n        var _comprehensiveData_question;\n        // Use the comprehensive data with project name replacement\n        const question = ((_comprehensiveData_question = comprehensiveData.question) === null || _comprehensiveData_question === void 0 ? void 0 : _comprehensiveData_question.replace(\"{PROJECT NAME}\", \"Siift\")) || comprehensiveData.title;\n        const answer = comprehensiveData.description || \"Working on: \".concat(comprehensiveData.title);\n        return {\n            question,\n            answer\n        };\n    }\n    // Fallback to the old logic if no comprehensive data found\n    const itemType = determineItemType(item);\n    const qaData = BUSINESS_ITEM_QUESTIONS[itemType] || BUSINESS_ITEM_QUESTIONS.default;\n    return {\n        question: qaData.question,\n        answer: qaData.getAnswer ? qaData.getAnswer(item) : qaData.defaultAnswer\n    };\n}\n/**\n * Determine the type of business item based on its properties\n * @param item - The business item object\n * @returns The item type string\n */ function determineItemType(item) {\n    if (!(item === null || item === void 0 ? void 0 : item.title)) return \"default\";\n    const title = item.title.toLowerCase();\n    // Check for keywords in the title to determine type\n    if (title.includes(\"problem\") || title.includes(\"issue\") || title.includes(\"challenge\")) {\n        return \"problem\";\n    }\n    if (title.includes(\"audience\") || title.includes(\"user\") || title.includes(\"customer\")) {\n        return \"audience\";\n    }\n    if (title.includes(\"alternative\") || title.includes(\"option\") || title.includes(\"approach\")) {\n        return \"alternatives\";\n    }\n    if (title.includes(\"solution\") || title.includes(\"resolve\") || title.includes(\"fix\")) {\n        return \"solution\";\n    }\n    if (title.includes(\"market\") || title.includes(\"opportunity\") || title.includes(\"competition\")) {\n        return \"market\";\n    }\n    if (title.includes(\"technology\") || title.includes(\"tech\") || title.includes(\"platform\")) {\n        return \"technology\";\n    }\n    if (title.includes(\"timeline\") || title.includes(\"schedule\") || title.includes(\"deadline\")) {\n        return \"timeline\";\n    }\n    if (title.includes(\"budget\") || title.includes(\"cost\") || title.includes(\"financial\")) {\n        return \"budget\";\n    }\n    if (title.includes(\"risk\") || title.includes(\"threat\") || title.includes(\"danger\")) {\n        return \"risk\";\n    }\n    if (title.includes(\"metric\") || title.includes(\"kpi\") || title.includes(\"measure\")) {\n        return \"metrics\";\n    }\n    if (title.includes(\"team\") || title.includes(\"resource\") || title.includes(\"staff\")) {\n        return \"team\";\n    }\n    return \"default\";\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/mockdata/businessItemQuestions.ts\n"));

/***/ })

});