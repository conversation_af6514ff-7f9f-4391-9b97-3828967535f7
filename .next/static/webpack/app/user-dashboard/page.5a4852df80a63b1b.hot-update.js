"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/user-dashboard/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/dashboard-stats-cards.tsx":
/*!************************************************************!*\
  !*** ./src/components/dashboard/dashboard-stats-cards.tsx ***!
  \************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DashboardStatsCards: () => (/* binding */ DashboardStatsCards)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Clock_FolderOpen_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Clock,FolderOpen,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/folder-open.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Clock_FolderOpen_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Clock,FolderOpen,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Clock_FolderOpen_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Clock,FolderOpen,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Clock_FolderOpen_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Clock,FolderOpen,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* __next_internal_client_entry_do_not_use__ DashboardStatsCards auto */ \n\n\nfunction DashboardStatsCards() {\n    const stats = [\n        {\n            title: \"Active Projects\",\n            value: \"4\",\n            description: \"Currently in progress\",\n            icon: _barrel_optimize_names_CheckCircle_Clock_FolderOpen_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n            color: \"text-blue-600\",\n            bgColor: \"bg-blue-100 dark:bg-blue-900/20\"\n        },\n        {\n            title: \"Completed Tasks\",\n            value: \"23\",\n            description: \"This month\",\n            icon: _barrel_optimize_names_CheckCircle_Clock_FolderOpen_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n            color: \"text-green-600\",\n            bgColor: \"bg-green-100 dark:bg-green-900/20\"\n        },\n        {\n            title: \"Hours Saved\",\n            value: \"47\",\n            description: \"Through automation\",\n            icon: _barrel_optimize_names_CheckCircle_Clock_FolderOpen_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            color: \"text-purple-600\",\n            bgColor: \"bg-purple-100 dark:bg-purple-900/20\"\n        },\n        {\n            title: \"Team Efficiency\",\n            value: \"94%\",\n            description: \"+12% from last month\",\n            icon: _barrel_optimize_names_CheckCircle_Clock_FolderOpen_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            color: \"text-indigo-600\",\n            bgColor: \"bg-indigo-100 dark:bg-indigo-900/20\"\n        }\n    ];\n    // const quickActions = [\n    //   {\n    //     title: \"Team Members\",\n    //     value: \"8\",\n    //     description: \"Active collaborators\",\n    //     icon: Users,\n    //     action: \"Manage Team\"\n    //   },\n    //   {\n    //     title: \"Upcoming Deadlines\",\n    //     value: \"3\",\n    //     description: \"Next 7 days\",\n    //     icon: Calendar,\n    //     action: \"View Calendar\"\n    //   },\n    //   {\n    //     title: \"Goals Progress\",\n    //     value: \"67%\",\n    //     description: \"Monthly targets\",\n    //     icon: Target,\n    //     action: \"View Goals\"\n    //   },\n    //   {\n    //     title: \"AI Insights\",\n    //     value: \"5\",\n    //     description: \"New recommendations\",\n    //     icon: Zap,\n    //     action: \"View Insights\"\n    //   }\n    // ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col gap-10\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid gap-4 md:grid-cols-2 lg:grid-cols-4\",\n                children: stats.map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.Card, {\n                        className: \"bg-gray-50 dark:bg-card hover:shadow-md hover:bg-gray-100 dark:hover:bg-accent/50 transition-all duration-200 cursor-pointer\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardHeader, {\n                                className: \"flex flex-row items-center justify-between space-y-0 pb-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardTitle, {\n                                        className: \"text-sm font-medium\",\n                                        children: stat.title\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/dashboard/dashboard-stats-cards.tsx\",\n                                        lineNumber: 84,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-2 rounded-lg \".concat(stat.bgColor),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(stat.icon, {\n                                            className: \"h-4 w-4 \".concat(stat.color)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/dashboard/dashboard-stats-cards.tsx\",\n                                            lineNumber: 88,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/dashboard/dashboard-stats-cards.tsx\",\n                                        lineNumber: 87,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/dashboard/dashboard-stats-cards.tsx\",\n                                lineNumber: 83,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardContent, {\n                                className: \"pt-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold\",\n                                        children: stat.value\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/dashboard/dashboard-stats-cards.tsx\",\n                                        lineNumber: 92,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-muted-foreground\",\n                                        children: stat.description\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/dashboard/dashboard-stats-cards.tsx\",\n                                        lineNumber: 93,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/dashboard/dashboard-stats-cards.tsx\",\n                                lineNumber: 91,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, index, true, {\n                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/dashboard/dashboard-stats-cards.tsx\",\n                        lineNumber: 79,\n                        columnNumber: 13\n                    }, this))\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/dashboard/dashboard-stats-cards.tsx\",\n                lineNumber: 77,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/dashboard/dashboard-stats-cards.tsx\",\n            lineNumber: 76,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/dashboard/dashboard-stats-cards.tsx\",\n        lineNumber: 74,\n        columnNumber: 5\n    }, this);\n}\n_c = DashboardStatsCards;\nvar _c;\n$RefreshReg$(_c, \"DashboardStatsCards\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/dashboard-stats-cards.tsx\n"));

/***/ })

});