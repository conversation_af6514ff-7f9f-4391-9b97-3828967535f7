"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { BusinessCategoryCards } from "./business-category-cards";
import { BusinessSectionsGridEnhanced } from "./business-sections/BusinessSectionsGridEnhanced";
import { mockBusinessSectionsNew } from "@/lib/businessSectionsDataNew";
import { getDetailedStatusInfo, getProgressSummary } from "@/lib/statusCountingSystem";
import { getAllCategoryOpacities } from "@/lib/cardOpacityUtils";
import { getCompletionStats, getSuggestedNextItems } from "@/lib/dependencyManager";
import type { BusinessItem } from "@/types/BusinessSection.types";

export function BusinessSectionsDemo() {
  const [selectedItem, setSelectedItem] = useState<BusinessItem | null>(null);
  const [activeTab, setActiveTab] = useState("overview");

  // Get all the analytics data
  const sections = mockBusinessSectionsNew;
  const statusInfo = getDetailedStatusInfo(sections);
  const progressSummary = getProgressSummary(sections);
  const categoryOpacities = getAllCategoryOpacities();
  const completionStats = getCompletionStats();
  const suggestedItems = getSuggestedNextItems(3);

  const handleItemClick = (item: BusinessItem) => {
    setSelectedItem(item);
    setActiveTab("details");
  };

  const handleCategoryClick = (category: string) => {
    setActiveTab("sections");
  };

  return (
    <div className="space-y-6">
      {/* Header with Overall Stats */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>Business Sections Overview</span>
            <Badge variant="outline">
              {statusInfo.overall.confirmed}/{statusInfo.overall.total} Complete
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">{statusInfo.overall.confirmed}</div>
              <div className="text-sm text-muted-foreground">Confirmed</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">{statusInfo.overall.actions}</div>
              <div className="text-sm text-muted-foreground">In Progress</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-yellow-600">{statusInfo.overall.ideas}</div>
              <div className="text-sm text-muted-foreground">Ideas</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-600">{statusInfo.overall.unproven}</div>
              <div className="text-sm text-muted-foreground">Not Started</div>
            </div>
          </div>
          
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>Overall Progress</span>
              <span>{progressSummary.totalProgress}%</span>
            </div>
            <Progress value={progressSummary.totalProgress} className="h-2" />
          </div>
        </CardContent>
      </Card>

      {/* Tabs for different views */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="categories">Categories</TabsTrigger>
          <TabsTrigger value="sections">Sections</TabsTrigger>
          <TabsTrigger value="details">Details</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          {/* Dependency Stats */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm">Ready to Progress</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-600">
                  {statusInfo.readyToProgress.length}
                </div>
                <p className="text-xs text-muted-foreground">
                  Items with satisfied dependencies
                </p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm">Blocked Items</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-red-600">
                  {statusInfo.blocked.length}
                </div>
                <p className="text-xs text-muted-foreground">
                  Items waiting for dependencies
                </p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm">Completion Rate</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-blue-600">
                  {completionStats.completionPercentage.toFixed(1)}%
                </div>
                <p className="text-xs text-muted-foreground">
                  Overall completion
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Suggested Next Items */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Suggested Next Items</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {suggestedItems.map((itemId, index) => {
                  const item = sections
                    .flatMap(s => s.items)
                    .find(i => i.id === itemId);
                  
                  if (!item) return null;
                  
                  return (
                    <div
                      key={itemId}
                      className="flex items-center justify-between p-2 rounded border cursor-pointer hover:bg-gray-50"
                      onClick={() => handleItemClick(item)}
                    >
                      <div>
                        <span className="font-medium">{item.title}</span>
                        <span className="text-sm text-muted-foreground ml-2">
                          ({item.category})
                        </span>
                      </div>
                      <Badge variant="outline">Priority {index + 1}</Badge>
                    </div>
                  );
                })}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="categories">
          <BusinessCategoryCards onCategoryClick={handleCategoryClick} />
        </TabsContent>

        <TabsContent value="sections">
          <BusinessSectionsGridEnhanced
            sections={sections}
            onItemClick={handleItemClick}
          />
        </TabsContent>

        <TabsContent value="details">
          {selectedItem ? (
            <Card>
              <CardHeader>
                <CardTitle>{selectedItem.title}</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h4 className="font-medium mb-2">Description</h4>
                  <p className="text-sm text-muted-foreground">
                    {selectedItem.description || "No description available"}
                  </p>
                </div>
                
                {selectedItem.question && (
                  <div>
                    <h4 className="font-medium mb-2">Question</h4>
                    <p className="text-sm text-muted-foreground">
                      {selectedItem.question}
                    </p>
                  </div>
                )}
                
                {selectedItem.guidance && (
                  <div>
                    <h4 className="font-medium mb-2">Guidance</h4>
                    <p className="text-sm text-muted-foreground">
                      {selectedItem.guidance}
                    </p>
                  </div>
                )}
                
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <h4 className="font-medium mb-2">Status</h4>
                    <Badge variant="outline">{selectedItem.status}</Badge>
                  </div>
                  
                  <div>
                    <h4 className="font-medium mb-2">Input Type</h4>
                    <Badge variant="outline">{selectedItem.inputType}</Badge>
                  </div>
                </div>
                
                {selectedItem.dependencies && selectedItem.dependencies.length > 0 && (
                  <div>
                    <h4 className="font-medium mb-2">Dependencies</h4>
                    <div className="space-y-1">
                      {selectedItem.dependencies.map((dep, index) => (
                        <Badge key={index} variant="secondary" className="mr-1">
                          {dep}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}
                
                {selectedItem.values && selectedItem.values.length > 0 && (
                  <div>
                    <h4 className="font-medium mb-2">Sample Values</h4>
                    <div className="space-y-2">
                      {selectedItem.values.slice(0, 3).map((value, index) => (
                        <div key={index} className="text-sm p-2 bg-gray-50 rounded">
                          {value}
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          ) : (
            <Card>
              <CardContent className="text-center py-8">
                <p className="text-muted-foreground">
                  Select an item to view details
                </p>
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}
