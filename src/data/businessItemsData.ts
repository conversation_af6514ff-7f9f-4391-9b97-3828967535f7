// Complete business items data structure based on the provided Excel data
import type { BusinessItemData } from "../types/BusinessItemData.types";
import { extendedBusinessItems } from "./businessItemsDataExtended";

export const businessItemsData: BusinessItemData[] = [
  // Market Category
  {
    id: "problem",
    title: "Problem",
    description:
      "The core challenge or pain points your target customers are experiencing that you are addressing, can be defined in terms of not important, nice to have, or urgent/willing to pay.",
    question: "What does {PROJECT NAME} solve?",
    guidance:
      "Dig into root causes, 5-Whys; quantify pain and frequency; avoid jumping to solutions.",
    category: "Market",
    order: 1,
    inputType: "Manual",
    dependencies: [],
    status: "confirmed",
    values: [
      "Small businesses struggle with manual inventory tracking leading to stockouts and overstock",
      "Remote teams lack effective real-time collaboration tools",
      "Consumers can't easily find sustainable product alternatives",
    ],
    actions: 2,
    ideas: 3,
    results: 1,
  },
  {
    id: "unique-value-proposition",
    title: "Unique Value Proposition",
    description:
      "The standout benefits or features that makes your offering distinct and compelling compared to alternatives.",
    question: "What's unique about what {PROJECT NAME} offers?",
    guidance:
      "State value delta in one crisp line; test uniqueness vs comps; anchor on key metric lift.",
    category: "Market",
    order: 2,
    inputType: "Suggest",
    dependencies: ["problem"],
    status: "action",
    values: [
      "AI-powered inventory prediction with 95% accuracy",
      "Real-time collaboration with zero-latency video and document sharing",
      "Sustainability score for every product with verified impact data",
    ],
    actions: 1,
    ideas: 2,
    results: 0,
  },
  {
    id: "audience",
    title: "Audience",
    description:
      "This is target customer market; can be defined in terms of behaviors, demographics, geography, industry, role, organization size or psychographics.",
    question: "Who is {PROJECT NAME} for?",
    guidance:
      "Probe segment, JTBD, personas, buying triggers; verify with data; mirror target jargon & maturity.",
    category: "Market",
    order: 3,
    inputType: "Suggest",
    dependencies: ["problem"],
    status: "confirmed",
    values: [
      "Small to medium retail businesses (10-500 employees) with physical inventory",
      "Remote-first companies with 20-200 employees in tech/creative industries",
      "Environmentally conscious consumers aged 25-45 with disposable income",
    ],
    actions: 0,
    ideas: 1,
    results: 1,
  },
  {
    id: "alternatives",
    title: "Alternatives",
    description:
      "Competitor products, services, or manual workarounds your target customers currently use to address their problems.",
    question: "What're current alternatives to {PROJECT NAME}?",
    guidance:
      "Benchmark top incumbents & DIY; map feature gaps, switching barriers, and status-quo cost.",
    category: "Market",
    order: 4,
    inputType: "Auto",
    dependencies: ["problem", "audience", "unique-value-proposition"],
    status: "idea",
    values: [
      "Excel spreadsheets, QuickBooks inventory, manual counting",
      "Slack + Zoom + Google Docs, Microsoft Teams, Notion",
      "Google search, sustainability blogs, brand websites",
    ],
    actions: 0,
    ideas: 1,
    results: 0,
  },
  {
    id: "market-size",
    title: "Market Size",
    description:
      "The estimated number of potential customers or the revenue opportunity available in your target market, definable in terms of TAM, SAM & SOM.",
    question: "How big is the market for {PROJECT NAME}?",
    guidance:
      "Walk through TAM/SAM/SOM math; cite sources; sanity-check with bottom-up calc.",
    category: "Market",
    order: 5,
    inputType: "Auto",
    dependencies: [
      "problem",
      "audience",
      "unique-value-proposition",
      "alternatives",
    ],
    status: "unproven",
    values: [
      "TAM: $50B retail inventory management, SAM: $5B SMB segment, SOM: $500M addressable",
      "TAM: $30B collaboration software, SAM: $8B remote team tools, SOM: $800M target segment",
      "TAM: $150B sustainable products, SAM: $15B conscious consumers, SOM: $1.5B early adopters",
    ],
    actions: 0,
    ideas: 0,
    results: 0,
  },
  {
    id: "trends",
    title: "Trends",
    description:
      "Emerging marco-economic or micro-economic shifts, technologies, or behaviors in your target audience or alternatives that could impact the viability of this business, categorizable in terms of headwinds and tailwinds.",
    question: "What trends should {PROJECT NAME} consider?",
    guidance:
      "Surface 3-5 macro/micro tailwinds/headwinds; time-box horizon; tie to urgency.",
    category: "Market",
    order: 6,
    inputType: "Auto",
    dependencies: [
      "problem",
      "audience",
      "unique-value-proposition",
      "alternatives",
    ],
    status: "unproven",
    values: [
      "AI adoption accelerating, supply chain digitization, labor shortage driving automation",
      "Remote work normalization, async communication preference, digital-first workflows",
      "ESG investing growth, Gen Z purchasing power, climate change awareness",
    ],
    actions: 0,
    ideas: 0,
    results: 0,
  },

  // Solution Category
  {
    id: "why",
    title: "Why",
    description:
      "The fundamental mission, vision and values of your business, the 'north stars' that drive your business and decisions.",
    question: "Why do you want to do {PROJECT NAME}?",
    guidance:
      "Articulate inspiring 'why' in <25 words; connect founder story & audience pain authentically.",
    category: "Solution",
    order: 7,
    inputType: "Suggest",
    dependencies: ["problem", "audience", "unique-value-proposition"],
    status: "idea",
    values: [
      "Eliminate inventory waste and help small businesses thrive through intelligent automation",
      "Enable seamless remote collaboration to unlock human potential regardless of location",
      "Accelerate sustainable consumption by making eco-friendly choices effortless and transparent",
    ],
    actions: 1,
    ideas: 2,
    results: 0,
  },
  {
    id: "advantages",
    title: "Advantages",
    description:
      "The specific strengths or assets that give you an edge over existing or potential competitors, like IP, relationships, reputations, networks, domain expertise, etc.",
    question: "Why are you the right team to make {PROJECT NAME}?",
    guidance:
      "Rank moats by copying difficulty & impact; quantify advantages; link to strategy.",
    category: "Solution",
    order: 8,
    inputType: "Suggest",
    dependencies: ["problem", "audience", "unique-value-proposition"],
    status: "action",
    values: [
      "10+ years retail operations experience, proprietary ML algorithms, exclusive supplier partnerships",
      "Former Google/Slack engineers, deep async communication research, enterprise sales network",
      "Sustainability certification expertise, verified supplier database, influencer partnerships",
    ],
    actions: 2,
    ideas: 1,
    results: 0,
  },
  {
    id: "product",
    title: "Product or Service",
    description:
      "The products or services you develop and offer to your audience to solve their problems, can be broken down into prioritized features or specifications",
    question: "What does {PROJECT NAME} offer?",
    guidance:
      "Outline MVP vs roadmap; map features to JTBD; validate feasibility & delight.",
    category: "Solution",
    order: 13,
    inputType: "Suggest",
    dependencies: [
      "problem",
      "audience",
      "unique-value-proposition",
      "alternatives",
      "trends",
      "why",
      "advantages",
    ],
    status: "unproven",
    values: [
      "AI inventory management platform: demand forecasting, automated reordering, analytics dashboard",
      "Unified collaboration suite: async video messaging, real-time co-editing, smart scheduling",
      "Sustainability marketplace: product scoring, impact tracking, eco-alternative recommendations",
    ],
    actions: 0,
    ideas: 0,
    results: 0,
  },
  {
    id: "tech",
    title: "Tech",
    description:
      "The technologies, platforms, tools & intellectual property that underpin and enable your product and unique value proposition.",
    question: "How does {PROJECT NAME} work?",
    guidance:
      "Explain stack choices for scale, security, moat; flag build-vs-buy; keep non-tech founders clear.",
    category: "Solution",
    order: 14,
    inputType: "Suggest",
    dependencies: [
      "problem",
      "audience",
      "unique-value-proposition",
      "alternatives",
      "trends",
      "why",
      "advantages",
      "product",
    ],
    status: "unproven",
    values: [
      "Python ML pipeline, React dashboard, PostgreSQL, AWS infrastructure, REST APIs",
      "WebRTC for video, React/Node.js, real-time sync engine, cloud storage integration",
      "Sustainability API, React Native app, blockchain verification, third-party integrations",
    ],
    actions: 0,
    ideas: 0,
    results: 0,
  },
  {
    id: "packages",
    title: "Packages",
    description:
      "Structured bundles or tiers of your product or service offerings, with deep consideration given to pricing and business model type suitability for the target market given the alternatives.",
    question: "What does {PROJECT NAME} sell?",
    guidance:
      "Design tiers aligned to segments & WTP; show anchor/decoy logic; outline upgrade paths.",
    category: "Solution",
    order: 16,
    inputType: "Suggest",
    dependencies: [
      "problem",
      "audience",
      "unique-value-proposition",
      "alternatives",
      "trends",
      "why",
      "advantages",
      "product",
      "tech",
      "business-model",
    ],
    status: "unproven",
    values: [
      "Starter ($49/mo): Basic forecasting, 1000 SKUs; Pro ($149/mo): Advanced analytics, 10K SKUs; Enterprise ($499/mo): Custom integrations, unlimited",
      "Team ($29/user/mo): Core features, 10 users; Business ($59/user/mo): Advanced tools, 50 users; Enterprise ($99/user/mo): Full suite, unlimited",
      "Basic ($9.99/mo): Product scores; Premium ($19.99/mo): Impact tracking; Pro ($39.99/mo): Marketplace access",
    ],
    actions: 0,
    ideas: 0,
    results: 0,
  },

  // Sales & Marketing Category
  {
    id: "positioning",
    title: "Positioning",
    description:
      "The strategic place your offering and company occupy your target markets' minds relative to alternatives, mainly informed by the trends, unique value proposition and problems.",
    question: "How is {PROJECT NAME} framed compared to others?",
    guidance:
      "Define category frame & quadrant; craft vivid tagline; stress emotional hook & credibility.",
    category: "Sales & Marketing",
    order: 9,
    inputType: "Auto",
    dependencies: [
      "problem",
      "audience",
      "unique-value-proposition",
      "alternatives",
      "trends",
      "why",
      "advantages",
    ],
    status: "unproven",
    values: [
      "The AI-first inventory platform for modern retailers who refuse to guess",
      "Async-first collaboration for teams that value deep work over constant meetings",
      "The sustainability compass for conscious consumers who want impact without compromise",
    ],
    actions: 0,
    ideas: 0,
    results: 0,
  },
  {
    id: "channels",
    title: "Channels",
    description:
      "The marketing, sales, and distribution paths you use to reach, engage, acquire, and convert your audience into paying customers.",
    question: "How is {PROJECT NAME} distributed?",
    guidance:
      "Use bullseye method, find 3 high-leverage channels; demand early traction data; model CAC payback.",
    category: "Sales & Marketing",
    order: 10,
    inputType: "Auto",
    dependencies: [
      "problem",
      "audience",
      "alternatives",
      "trends",
      "why",
      "advantages",
    ],
    status: "unproven",
    values: [
      "Direct sales to SMB retailers, retail trade shows, partner integrations with POS systems",
      "Product-led growth, content marketing to remote teams, enterprise sales to Fortune 500",
      "Influencer partnerships, sustainability conferences, B2B marketplace listings",
    ],
    actions: 0,
    ideas: 0,
    results: 0,
  },
  {
    id: "messaging",
    title: "Messaging",
    description:
      "The specific communication and words you use consistently to convey your unique valupe proposition, positioning and brand to your audience.",
    question: "How do we communicate {PROJECT NAME}'s value?",
    guidance:
      "Craft plain-language benefit first; tailor by funnel stage; maintain voice consistency.",
    category: "Sales & Marketing",
    order: 11,
    inputType: "Auto",
    dependencies: [
      "problem",
      "audience",
      "unique-value-proposition",
      "alternatives",
      "trends",
      "why",
      "advantages",
      "channels",
    ],
    status: "unproven",
    values: [
      "Stop inventory guesswork. Start profit certainty. Our AI predicts what you'll sell before you know you need it.",
      "Work together without being together. Deep collaboration that respects your focus time and delivers better results.",
      "Every purchase is a vote for the planet. Make yours count with verified sustainability scores for everything you buy.",
    ],
    actions: 0,
    ideas: 0,
    results: 0,
  },
  ...extendedBusinessItems,
];

// Helper functions for data manipulation
export const getItemsByCategory = (category: string) => {
  return businessItemsData.filter((item) => item.category === category);
};

export const getItemById = (id: string) => {
  return businessItemsData.find((item) => item.id === id);
};

export const checkDependencies = (itemId: string) => {
  const item = getItemById(itemId);
  if (!item) return false;

  return item.dependencies.every((depId) => {
    const depItem = getItemById(depId);
    return depItem?.status === "confirmed";
  });
};

export const getStatusCounts = () => {
  const counts = {
    idea: 0,
    action: 0,
    confirmed: 0,
    unproven: 0,
  };

  businessItemsData.forEach((item) => {
    counts[item.status]++;
  });

  return counts;
};
