"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/projects/[id]/page",{

/***/ "(app-pages-browser)/./src/services/browserStorageService.ts":
/*!***********************************************!*\
  !*** ./src/services/browserStorageService.ts ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addItemDetail: () => (/* binding */ addItemDetail),\n/* harmony export */   browserStorageService: () => (/* binding */ browserStorageService),\n/* harmony export */   clearAllData: () => (/* binding */ clearAllData),\n/* harmony export */   clearItemDetails: () => (/* binding */ clearItemDetails),\n/* harmony export */   deleteItemDetail: () => (/* binding */ deleteItemDetail),\n/* harmony export */   exportData: () => (/* binding */ exportData),\n/* harmony export */   getAllItemDetails: () => (/* binding */ getAllItemDetails),\n/* harmony export */   getItemDetails: () => (/* binding */ getItemDetails),\n/* harmony export */   getRecentlyModified: () => (/* binding */ getRecentlyModified),\n/* harmony export */   getStorageStats: () => (/* binding */ getStorageStats),\n/* harmony export */   hasItemDetails: () => (/* binding */ hasItemDetails),\n/* harmony export */   importData: () => (/* binding */ importData),\n/* harmony export */   saveItemDetails: () => (/* binding */ saveItemDetails),\n/* harmony export */   updateItemDetail: () => (/* binding */ updateItemDetail)\n/* harmony export */ });\n// Browser storage service for persisting business item data\nconst STORAGE_KEYS = {\n    BUSINESS_ITEMS: 'siift_business_items',\n    BUSINESS_ITEM_DETAILS: 'siift_business_item_details',\n    LAST_UPDATED: 'siift_last_updated'\n};\nclass BrowserStorageService {\n    // Get all stored business item details\n    getAllItemDetails() {\n        if (!this.isClient) return {};\n        try {\n            const stored = localStorage.getItem(STORAGE_KEYS.BUSINESS_ITEM_DETAILS);\n            return stored ? JSON.parse(stored) : {};\n        } catch (error) {\n            console.error('Error reading from localStorage:', error);\n            return {};\n        }\n    }\n    // Get details for a specific business item\n    getItemDetails(itemId, projectId) {\n        const allData = this.getAllItemDetails();\n        const key = projectId ? \"\".concat(projectId, \"_\").concat(itemId) : itemId;\n        return allData[key] || [];\n    }\n    // Save details for a specific business item\n    saveItemDetails(itemId, details, projectId) {\n        if (!this.isClient) return;\n        try {\n            const allData = this.getAllItemDetails();\n            const key = projectId ? \"\".concat(projectId, \"_\").concat(itemId) : itemId;\n            // Convert to stored format with timestamps\n            const storedDetails = details.map((detail)=>({\n                    ...detail,\n                    projectId,\n                    lastModified: new Date().toISOString()\n                }));\n            allData[key] = storedDetails;\n            localStorage.setItem(STORAGE_KEYS.BUSINESS_ITEM_DETAILS, JSON.stringify(allData));\n            localStorage.setItem(STORAGE_KEYS.LAST_UPDATED, new Date().toISOString());\n        } catch (error) {\n            console.error('Error saving to localStorage:', error);\n        }\n    }\n    // Add a new detail item\n    addItemDetail(itemId, detail, projectId) {\n        const newDetail = {\n            ...detail,\n            id: \"\".concat(itemId, \"_\").concat(Date.now(), \"_\").concat(Math.random().toString(36).substr(2, 9)),\n            createdAt: new Date().toISOString(),\n            updatedAt: new Date().toISOString()\n        };\n        const existingDetails = this.getItemDetails(itemId, projectId);\n        const updatedDetails = [\n            ...existingDetails,\n            newDetail\n        ];\n        this.saveItemDetails(itemId, updatedDetails, projectId);\n        return newDetail;\n    }\n    // Update an existing detail item\n    updateItemDetail(itemId, detailId, updates, projectId) {\n        const existingDetails = this.getItemDetails(itemId, projectId);\n        const detailIndex = existingDetails.findIndex((d)=>d.id === detailId);\n        if (detailIndex === -1) return false;\n        const updatedDetails = [\n            ...existingDetails\n        ];\n        updatedDetails[detailIndex] = {\n            ...updatedDetails[detailIndex],\n            ...updates,\n            updatedAt: new Date().toISOString()\n        };\n        this.saveItemDetails(itemId, updatedDetails, projectId);\n        return true;\n    }\n    // Delete a detail item\n    deleteItemDetail(itemId, detailId, projectId) {\n        const existingDetails = this.getItemDetails(itemId, projectId);\n        const filteredDetails = existingDetails.filter((d)=>d.id !== detailId);\n        if (filteredDetails.length === existingDetails.length) return false;\n        this.saveItemDetails(itemId, filteredDetails, projectId);\n        return true;\n    }\n    // Clear all data for a specific item\n    clearItemDetails(itemId, projectId) {\n        if (!this.isClient) return;\n        try {\n            const allData = this.getAllItemDetails();\n            const key = projectId ? \"\".concat(projectId, \"_\").concat(itemId) : itemId;\n            delete allData[key];\n            localStorage.setItem(STORAGE_KEYS.BUSINESS_ITEM_DETAILS, JSON.stringify(allData));\n            localStorage.setItem(STORAGE_KEYS.LAST_UPDATED, new Date().toISOString());\n        } catch (error) {\n            console.error('Error clearing localStorage:', error);\n        }\n    }\n    // Clear all stored data\n    clearAllData() {\n        if (!this.isClient) return;\n        try {\n            localStorage.removeItem(STORAGE_KEYS.BUSINESS_ITEM_DETAILS);\n            localStorage.removeItem(STORAGE_KEYS.LAST_UPDATED);\n        } catch (error) {\n            console.error('Error clearing all localStorage:', error);\n        }\n    }\n    // Get storage statistics\n    getStorageStats() {\n        if (!this.isClient) {\n            return {\n                totalItems: 0,\n                totalDetails: 0,\n                storageSize: 0,\n                lastUpdated: null\n            };\n        }\n        try {\n            const allData = this.getAllItemDetails();\n            const totalItems = Object.keys(allData).length;\n            const totalDetails = Object.values(allData).reduce((sum, details)=>sum + details.length, 0);\n            const dataString = localStorage.getItem(STORAGE_KEYS.BUSINESS_ITEM_DETAILS) || '';\n            const storageSize = new Blob([\n                dataString\n            ]).size;\n            const lastUpdated = localStorage.getItem(STORAGE_KEYS.LAST_UPDATED);\n            return {\n                totalItems,\n                totalDetails,\n                storageSize,\n                lastUpdated\n            };\n        } catch (error) {\n            console.error('Error getting storage stats:', error);\n            return {\n                totalItems: 0,\n                totalDetails: 0,\n                storageSize: 0,\n                lastUpdated: null\n            };\n        }\n    }\n    // Export data for backup\n    exportData() {\n        const allData = this.getAllItemDetails();\n        const stats = this.getStorageStats();\n        return JSON.stringify({\n            version: '1.0',\n            exportDate: new Date().toISOString(),\n            stats,\n            data: allData\n        }, null, 2);\n    }\n    // Import data from backup\n    importData(jsonData) {\n        if (!this.isClient) return false;\n        try {\n            const importedData = JSON.parse(jsonData);\n            if (!importedData.data || typeof importedData.data !== 'object') {\n                throw new Error('Invalid data format');\n            }\n            localStorage.setItem(STORAGE_KEYS.BUSINESS_ITEM_DETAILS, JSON.stringify(importedData.data));\n            localStorage.setItem(STORAGE_KEYS.LAST_UPDATED, new Date().toISOString());\n            return true;\n        } catch (error) {\n            console.error('Error importing data:', error);\n            return false;\n        }\n    }\n    // Check if data exists for an item\n    hasItemDetails(itemId, projectId) {\n        const details = this.getItemDetails(itemId, projectId);\n        return details.length > 0;\n    }\n    // Get items that have been modified recently\n    getRecentlyModified() {\n        let hours = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 24;\n        const allData = this.getAllItemDetails();\n        const cutoffTime = new Date(Date.now() - hours * 60 * 60 * 1000);\n        const recentItems = [];\n        Object.entries(allData).forEach((param)=>{\n            let [key, details] = param;\n            const latestDetail = details.reduce((latest, current)=>{\n                return new Date(current.lastModified) > new Date(latest.lastModified) ? current : latest;\n            });\n            if (new Date(latestDetail.lastModified) > cutoffTime) {\n                const [projectId, itemId] = key.includes('_') ? key.split('_') : [\n                    undefined,\n                    key\n                ];\n                recentItems.push({\n                    itemId: itemId || key,\n                    projectId,\n                    lastModified: latestDetail.lastModified\n                });\n            }\n        });\n        return recentItems.sort((a, b)=>new Date(b.lastModified).getTime() - new Date(a.lastModified).getTime());\n    }\n    constructor(){\n        this.isClient = \"object\" !== 'undefined';\n    }\n}\n// Export singleton instance\nconst browserStorageService = new BrowserStorageService();\n// Export convenience functions\nconst { getAllItemDetails, getItemDetails, saveItemDetails, addItemDetail, updateItemDetail, deleteItemDetail, clearItemDetails, clearAllData, getStorageStats, exportData, importData, hasItemDetails, getRecentlyModified } = browserStorageService;\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/services/browserStorageService.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/stores/businessItemStoreEnhanced.ts":
/*!*************************************************!*\
  !*** ./src/stores/businessItemStoreEnhanced.ts ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkItemDependencies: () => (/* binding */ checkItemDependencies),\n/* harmony export */   getItemData: () => (/* binding */ getItemData),\n/* harmony export */   getRelatedItems: () => (/* binding */ getRelatedItems),\n/* harmony export */   useBusinessItemStoreEnhanced: () => (/* binding */ useBusinessItemStoreEnhanced)\n/* harmony export */ });\n/* harmony import */ var _data_businessItemsData__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/data/businessItemsData */ \"(app-pages-browser)/./src/data/businessItemsData.ts\");\n/* harmony import */ var _services_businessSectionsApi__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/services/businessSectionsApi */ \"(app-pages-browser)/./src/services/businessSectionsApi.ts\");\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! zustand */ \"(app-pages-browser)/./node_modules/zustand/esm/react.mjs\");\n/* harmony import */ var zustand_middleware__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! zustand/middleware */ \"(app-pages-browser)/./node_modules/zustand/esm/middleware.mjs\");\n/* harmony import */ var _services_browserStorageService__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../services/browserStorageService */ \"(app-pages-browser)/./src/services/browserStorageService.ts\");\n// Enhanced business item store using the new comprehensive data structure\n\n\n\n\n\nconst useBusinessItemStoreEnhanced = (0,zustand__WEBPACK_IMPORTED_MODULE_3__.create)()((0,zustand_middleware__WEBPACK_IMPORTED_MODULE_4__.devtools)((set, get)=>({\n        selectedItem: null,\n        itemDetails: [],\n        isLoading: false,\n        error: null,\n        setSelectedItem: (item)=>set({\n                selectedItem: item\n            }),\n        setItemDetails: (details)=>set({\n                itemDetails: details\n            }),\n        setLoading: (isLoading)=>set({\n                isLoading\n            }),\n        setError: (error)=>set({\n                error\n            }),\n        // Fetch comprehensive item details using the new data structure\n        fetchItemDetails: async function(itemId) {\n            let projectId = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"default\";\n            try {\n                var _itemData_question;\n                set({\n                    isLoading: true,\n                    error: null\n                });\n                // First, try to get stored details from browser storage\n                // For now, always regenerate to see fresh data (remove this later)\n                // if (storedDetails.length > 0) {\n                //   // Use stored details if available\n                //   console.log(\"Using stored details for\", itemId, storedDetails);\n                //   set({ itemDetails: storedDetails, isLoading: false });\n                //   return;\n                // }\n                console.log(\"No stored details found, creating initial data for\", itemId);\n                // If no stored details, create initial details from comprehensive data\n                const itemData = _data_businessItemsData__WEBPACK_IMPORTED_MODULE_0__.businessItemsData.find((item)=>item.id === itemId);\n                if (!itemData) {\n                    throw new Error(\"Item with ID \".concat(itemId, \" not found\"));\n                }\n                // Create initial detail items from sample values\n                const initialDetails = [];\n                // Add sample values as editable detail items for the table\n                if (itemData.values && itemData.values.length > 0) {\n                    itemData.values.forEach((value, index)=>{\n                        var _itemData_question;\n                        initialDetails.push({\n                            id: \"\".concat(itemData.id, \"-value-\").concat(index),\n                            title: \"Response \".concat(index + 1),\n                            status: \"idea\",\n                            actions: \"Sample response - edit to customize\",\n                            result: value,\n                            description: \"Sample response for: \".concat(((_itemData_question = itemData.question) === null || _itemData_question === void 0 ? void 0 : _itemData_question.replace(\"{PROJECT NAME}\", \"your project\")) || itemData.title),\n                            createdAt: new Date().toISOString(),\n                            updatedAt: new Date().toISOString()\n                        });\n                    });\n                }\n                // Add a blank item for new entries\n                initialDetails.push({\n                    id: \"\".concat(itemData.id, \"-new-entry\"),\n                    title: \"Add your response\",\n                    status: \"unproven\",\n                    actions: \"Click to edit and add your own analysis\",\n                    result: \"Enter your findings here...\",\n                    description: \"Your analysis for: \".concat(((_itemData_question = itemData.question) === null || _itemData_question === void 0 ? void 0 : _itemData_question.replace(\"{PROJECT NAME}\", \"your project\")) || itemData.title),\n                    createdAt: new Date().toISOString(),\n                    updatedAt: new Date().toISOString()\n                });\n                // Save initial details to browser storage\n                _services_browserStorageService__WEBPACK_IMPORTED_MODULE_2__.browserStorageService.saveItemDetails(itemId, initialDetails, projectId);\n                console.log(\"Created initial details for\", itemId, initialDetails);\n                set({\n                    itemDetails: initialDetails,\n                    isLoading: false\n                });\n            } catch (error) {\n                console.error(\"Error fetching item details:\", error);\n                set({\n                    error: error instanceof Error ? error.message : \"Failed to fetch item details\",\n                    isLoading: false\n                });\n            }\n        },\n        // Update item status with dependency validation\n        updateItemStatus: async function(itemId, status) {\n            let projectId = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : \"default\";\n            try {\n                set({\n                    isLoading: true,\n                    error: null\n                });\n                // Check dependencies before allowing status change to \"confirmed\"\n                if (status === \"confirmed\") {\n                    const canProgress = get().checkDependencies(itemId);\n                    if (!canProgress) {\n                        throw new Error(\"Cannot mark as confirmed: dependencies not satisfied\");\n                    }\n                }\n                // Use the API to update the item\n                await _services_businessSectionsApi__WEBPACK_IMPORTED_MODULE_1__.BusinessSectionsApi.updateItem(projectId, itemId, {\n                    status\n                });\n                // Update local state if we have the item selected\n                const { selectedItem } = get();\n                if (selectedItem && selectedItem.id === itemId) {\n                    set({\n                        selectedItem: {\n                            ...selectedItem,\n                            status\n                        }\n                    });\n                }\n                set({\n                    isLoading: false\n                });\n            } catch (error) {\n                console.error(\"Error updating item status:\", error);\n                set({\n                    error: error instanceof Error ? error.message : \"Failed to update item status\",\n                    isLoading: false\n                });\n            }\n        },\n        addItemDetail: (detail)=>{\n            const { selectedItem } = get();\n            if (!selectedItem) return;\n            const updatedDetails = [\n                ...get().itemDetails,\n                detail\n            ];\n            set({\n                itemDetails: updatedDetails\n            });\n            // Save to browser storage\n            _services_browserStorageService__WEBPACK_IMPORTED_MODULE_2__.browserStorageService.saveItemDetails(selectedItem.id, updatedDetails, \"default\");\n        },\n        updateItemDetail: (id, updates)=>{\n            const { selectedItem } = get();\n            if (!selectedItem) return;\n            const updatedDetails = get().itemDetails.map((item)=>item.id === id ? {\n                    ...item,\n                    ...updates,\n                    updatedAt: new Date().toISOString()\n                } : item);\n            set({\n                itemDetails: updatedDetails\n            });\n            // Save to browser storage\n            _services_browserStorageService__WEBPACK_IMPORTED_MODULE_2__.browserStorageService.saveItemDetails(selectedItem.id, updatedDetails, \"default\");\n        },\n        removeItemDetail: (id)=>{\n            const { selectedItem } = get();\n            if (!selectedItem) return;\n            const updatedDetails = get().itemDetails.filter((item)=>item.id !== id);\n            set({\n                itemDetails: updatedDetails\n            });\n            // Save to browser storage\n            _services_browserStorageService__WEBPACK_IMPORTED_MODULE_2__.browserStorageService.saveItemDetails(selectedItem.id, updatedDetails, \"default\");\n        },\n        // Get comprehensive item data\n        getItemData: (itemId)=>{\n            return _data_businessItemsData__WEBPACK_IMPORTED_MODULE_0__.businessItemsData.find((item)=>item.id === itemId);\n        },\n        // Check if item dependencies are satisfied\n        checkDependencies: (itemId)=>{\n            const itemData = _data_businessItemsData__WEBPACK_IMPORTED_MODULE_0__.businessItemsData.find((item)=>item.id === itemId);\n            if (!itemData || !itemData.dependencies || itemData.dependencies.length === 0) {\n                return true;\n            }\n            return itemData.dependencies.every((depId)=>{\n                const depItem = _data_businessItemsData__WEBPACK_IMPORTED_MODULE_0__.businessItemsData.find((dep)=>dep.id === depId);\n                return (depItem === null || depItem === void 0 ? void 0 : depItem.status) === \"confirmed\";\n            });\n        },\n        // Get related items (dependencies and dependents)\n        getRelatedItems: (itemId)=>{\n            var _itemData_dependencies;\n            const itemData = _data_businessItemsData__WEBPACK_IMPORTED_MODULE_0__.businessItemsData.find((item)=>item.id === itemId);\n            // Get dependencies\n            const dependencies = (itemData === null || itemData === void 0 ? void 0 : (_itemData_dependencies = itemData.dependencies) === null || _itemData_dependencies === void 0 ? void 0 : _itemData_dependencies.map((depId)=>_data_businessItemsData__WEBPACK_IMPORTED_MODULE_0__.businessItemsData.find((item)=>item.id === depId)).filter(Boolean)) || [];\n            // Get dependents (items that depend on this one)\n            const dependents = _data_businessItemsData__WEBPACK_IMPORTED_MODULE_0__.businessItemsData.filter((item)=>{\n                var _item_dependencies;\n                return (_item_dependencies = item.dependencies) === null || _item_dependencies === void 0 ? void 0 : _item_dependencies.includes(itemId);\n            });\n            return {\n                dependencies,\n                dependents\n            };\n        }\n    }), {\n    name: \"business-item-store-enhanced\"\n}));\n// Helper functions for easy access\nconst getItemData = (itemId)=>{\n    return _data_businessItemsData__WEBPACK_IMPORTED_MODULE_0__.businessItemsData.find((item)=>item.id === itemId);\n};\nconst checkItemDependencies = (itemId)=>{\n    const itemData = _data_businessItemsData__WEBPACK_IMPORTED_MODULE_0__.businessItemsData.find((item)=>item.id === itemId);\n    if (!itemData || !itemData.dependencies || itemData.dependencies.length === 0) {\n        return true;\n    }\n    return itemData.dependencies.every((depId)=>{\n        const depItem = _data_businessItemsData__WEBPACK_IMPORTED_MODULE_0__.businessItemsData.find((dep)=>dep.id === depId);\n        return (depItem === null || depItem === void 0 ? void 0 : depItem.status) === \"confirmed\";\n    });\n};\nconst getRelatedItems = (itemId)=>{\n    var _itemData_dependencies;\n    const itemData = _data_businessItemsData__WEBPACK_IMPORTED_MODULE_0__.businessItemsData.find((item)=>item.id === itemId);\n    const dependencies = (itemData === null || itemData === void 0 ? void 0 : (_itemData_dependencies = itemData.dependencies) === null || _itemData_dependencies === void 0 ? void 0 : _itemData_dependencies.map((depId)=>_data_businessItemsData__WEBPACK_IMPORTED_MODULE_0__.businessItemsData.find((item)=>item.id === depId)).filter(Boolean)) || [];\n    const dependents = _data_businessItemsData__WEBPACK_IMPORTED_MODULE_0__.businessItemsData.filter((item)=>{\n        var _item_dependencies;\n        return (_item_dependencies = item.dependencies) === null || _item_dependencies === void 0 ? void 0 : _item_dependencies.includes(itemId);\n    });\n    return {\n        dependencies,\n        dependents\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/stores/businessItemStoreEnhanced.ts\n"));

/***/ })

});