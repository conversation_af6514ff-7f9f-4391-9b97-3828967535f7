"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/projects/[id]/page",{

/***/ "(app-pages-browser)/./src/components/project/ProjectMainContent.tsx":
/*!*******************************************************!*\
  !*** ./src/components/project/ProjectMainContent.tsx ***!
  \*******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ProjectMainContent: () => (/* binding */ ProjectMainContent)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _lib_constants__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/constants */ \"(app-pages-browser)/./src/lib/constants.ts\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _business_item_table__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../business-item-table */ \"(app-pages-browser)/./src/components/business-item-table.tsx\");\n/* harmony import */ var _ContentSections__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ContentSections */ \"(app-pages-browser)/./src/components/project/ContentSections.tsx\");\n/* __next_internal_client_entry_do_not_use__ ProjectMainContent auto */ \n\n\n\n\n\nfunction ProjectMainContent(param) {\n    let { activeContent, setActiveContent, mockDraftItems, mockFileItems, selectedItem, itemDetails, sections, isLoading, error, onBusinessItemClick, onBackToItems } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex-1 min-w-0 min-h-0 w-full max-w-full p-4 pb-20 overflow-y-auto bg-gray-50 dark:bg-gray-900 relative transition-all duration-300 ease-in-out main-content-bottom-fade\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 project-main-content pointer-events-none\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectMainContent.tsx\",\n                lineNumber: 39,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-30\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ContentSections__WEBPACK_IMPORTED_MODULE_4__.ContentSections, {\n                        activeContent: activeContent,\n                        setActiveContent: setActiveContent,\n                        mockDraftItems: mockDraftItems,\n                        mockFileItems: mockFileItems\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectMainContent.tsx\",\n                        lineNumber: 44,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.AnimatePresence, {\n                        mode: \"wait\",\n                        children: [\n                            !activeContent && !selectedItem && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                exit: {\n                                    opacity: 0,\n                                    y: -20\n                                },\n                                transition: {\n                                    duration: 0.3,\n                                    ease: \"easeInOut\"\n                                },\n                                children: [\n                                    isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center h-64\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"animate-spin rounded-full \".concat(_lib_constants__WEBPACK_IMPORTED_MODULE_2__.ICON_SIZES.lg, \" border-b-2 border-gray-900 mx-auto mb-4\")\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectMainContent.tsx\",\n                                                    lineNumber: 65,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600\",\n                                                    children: \"Loading business sections...\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectMainContent.tsx\",\n                                                    lineNumber: 68,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectMainContent.tsx\",\n                                            lineNumber: 64,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectMainContent.tsx\",\n                                        lineNumber: 63,\n                                        columnNumber: 17\n                                    }, this),\n                                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center h-64\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-red-600 mb-4\",\n                                                    children: error\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectMainContent.tsx\",\n                                                    lineNumber: 79,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                                    onClick: ()=>window.location.reload(),\n                                                    children: \"Try Again\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectMainContent.tsx\",\n                                                    lineNumber: 80,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectMainContent.tsx\",\n                                            lineNumber: 78,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectMainContent.tsx\",\n                                        lineNumber: 77,\n                                        columnNumber: 17\n                                    }, this),\n                                    !isLoading && !error && sections.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BusinessSectionsGrid, {\n                                        sections: sections,\n                                        onItemClick: onBusinessItemClick\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectMainContent.tsx\",\n                                        lineNumber: 89,\n                                        columnNumber: 17\n                                    }, this),\n                                    !isLoading && !error && sections.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center h-64\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600 mb-4\",\n                                                    children: \"No business sections found\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectMainContent.tsx\",\n                                                    lineNumber: 99,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                                    onClick: ()=>window.location.reload(),\n                                                    children: \"Reload\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectMainContent.tsx\",\n                                                    lineNumber: 102,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectMainContent.tsx\",\n                                            lineNumber: 98,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectMainContent.tsx\",\n                                        lineNumber: 97,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, \"business-sections\", true, {\n                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectMainContent.tsx\",\n                                lineNumber: 54,\n                                columnNumber: 13\n                            }, this),\n                            !activeContent && selectedItem && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    x: 30\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    x: 0\n                                },\n                                exit: {\n                                    opacity: 0,\n                                    x: -30\n                                },\n                                transition: {\n                                    duration: 0.3,\n                                    ease: \"easeInOut\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_business_item_table__WEBPACK_IMPORTED_MODULE_3__.BusinessItemTable, {\n                                    itemDetails: itemDetails,\n                                    selectedBusinessItem: selectedItem,\n                                    onBackToItems: onBackToItems\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectMainContent.tsx\",\n                                    lineNumber: 120,\n                                    columnNumber: 15\n                                }, this)\n                            }, \"business-detail\", false, {\n                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectMainContent.tsx\",\n                                lineNumber: 113,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectMainContent.tsx\",\n                        lineNumber: 51,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectMainContent.tsx\",\n                lineNumber: 42,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectMainContent.tsx\",\n        lineNumber: 37,\n        columnNumber: 5\n    }, this);\n}\n_c = ProjectMainContent;\nvar _c;\n$RefreshReg$(_c, \"ProjectMainContent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/project/ProjectMainContent.tsx\n"));

/***/ })

});