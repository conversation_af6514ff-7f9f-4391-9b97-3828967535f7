"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/projects/[id]/page",{

/***/ "(app-pages-browser)/./node_modules/@radix-ui/react-collapsible/dist/index.mjs":
/*!*****************************************************************!*\
  !*** ./node_modules/@radix-ui/react-collapsible/dist/index.mjs ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Collapsible: () => (/* binding */ Collapsible),\n/* harmony export */   CollapsibleContent: () => (/* binding */ CollapsibleContent),\n/* harmony export */   CollapsibleTrigger: () => (/* binding */ CollapsibleTrigger),\n/* harmony export */   Content: () => (/* binding */ Content),\n/* harmony export */   Root: () => (/* binding */ Root),\n/* harmony export */   Trigger: () => (/* binding */ Trigger),\n/* harmony export */   createCollapsibleScope: () => (/* binding */ createCollapsibleScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var _radix_ui_primitive__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/primitive */ \"(app-pages-browser)/./node_modules/@radix-ui/primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-context */ \"(app-pages-browser)/./node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-use-controllable-state */ \"(app-pages-browser)/./node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(app-pages-browser)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(app-pages-browser)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(app-pages-browser)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @radix-ui/react-presence */ \"(app-pages-browser)/./node_modules/@radix-ui/react-presence/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_id__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-id */ \"(app-pages-browser)/./node_modules/@radix-ui/react-id/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Collapsible,CollapsibleContent,CollapsibleTrigger,Content,Root,Trigger,createCollapsibleScope auto */ var _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$(), _s3 = $RefreshSig$();\n// src/collapsible.tsx\n\n\n\n\n\n\n\n\n\n\nvar COLLAPSIBLE_NAME = \"Collapsible\";\nvar [createCollapsibleContext, createCollapsibleScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__.createContextScope)(COLLAPSIBLE_NAME);\nvar [CollapsibleProvider, useCollapsibleContext] = createCollapsibleContext(COLLAPSIBLE_NAME);\nvar Collapsible = /*#__PURE__*/ _s(react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(_c = _s((props, forwardedRef)=>{\n    _s();\n    const { __scopeCollapsible, open: openProp, defaultOpen, disabled, onOpenChange, ...collapsibleProps } = props;\n    const [open, setOpen] = (0,_radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_3__.useControllableState)({\n        prop: openProp,\n        defaultProp: defaultOpen !== null && defaultOpen !== void 0 ? defaultOpen : false,\n        onChange: onOpenChange,\n        caller: COLLAPSIBLE_NAME\n    });\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(CollapsibleProvider, {\n        scope: __scopeCollapsible,\n        disabled,\n        contentId: (0,_radix_ui_react_id__WEBPACK_IMPORTED_MODULE_4__.useId)(),\n        open,\n        onOpenToggle: react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n            \"Collapsible.useCallback\": ()=>setOpen({\n                    \"Collapsible.useCallback\": (prevOpen)=>!prevOpen\n                }[\"Collapsible.useCallback\"])\n        }[\"Collapsible.useCallback\"], [\n            setOpen\n        ]),\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_5__.Primitive.div, {\n            \"data-state\": getState(open),\n            \"data-disabled\": disabled ? \"\" : void 0,\n            ...collapsibleProps,\n            ref: forwardedRef\n        })\n    });\n}, \"gtMdJnGe96gIg19W2/ulD54QnNg=\", false, function() {\n    return [\n        _radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_3__.useControllableState,\n        _radix_ui_react_id__WEBPACK_IMPORTED_MODULE_4__.useId\n    ];\n})), \"gtMdJnGe96gIg19W2/ulD54QnNg=\", false, function() {\n    return [\n        _radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_3__.useControllableState,\n        _radix_ui_react_id__WEBPACK_IMPORTED_MODULE_4__.useId\n    ];\n});\n_c1 = Collapsible;\nCollapsible.displayName = COLLAPSIBLE_NAME;\nvar TRIGGER_NAME = \"CollapsibleTrigger\";\nvar CollapsibleTrigger = /*#__PURE__*/ _s1(react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(_c2 = _s1((props, forwardedRef)=>{\n    _s1();\n    const { __scopeCollapsible, ...triggerProps } = props;\n    const context = useCollapsibleContext(TRIGGER_NAME, __scopeCollapsible);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_5__.Primitive.button, {\n        type: \"button\",\n        \"aria-controls\": context.contentId,\n        \"aria-expanded\": context.open || false,\n        \"data-state\": getState(context.open),\n        \"data-disabled\": context.disabled ? \"\" : void 0,\n        disabled: context.disabled,\n        ...triggerProps,\n        ref: forwardedRef,\n        onClick: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_6__.composeEventHandlers)(props.onClick, context.onOpenToggle)\n    });\n}, \"GEAea+U+cRVxeZRny55RTbktW6U=\", false, function() {\n    return [\n        useCollapsibleContext\n    ];\n})), \"GEAea+U+cRVxeZRny55RTbktW6U=\", false, function() {\n    return [\n        useCollapsibleContext\n    ];\n});\n_c3 = CollapsibleTrigger;\nCollapsibleTrigger.displayName = TRIGGER_NAME;\nvar CONTENT_NAME = \"CollapsibleContent\";\nvar CollapsibleContent = /*#__PURE__*/ _s2(react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(_c4 = _s2((props, forwardedRef)=>{\n    _s2();\n    const { forceMount, ...contentProps } = props;\n    const context = useCollapsibleContext(CONTENT_NAME, props.__scopeCollapsible);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_7__.Presence, {\n        present: forceMount || context.open,\n        children: (param)=>{\n            let { present } = param;\n            return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(CollapsibleContentImpl, {\n                ...contentProps,\n                ref: forwardedRef,\n                present\n            });\n        }\n    });\n}, \"GEAea+U+cRVxeZRny55RTbktW6U=\", false, function() {\n    return [\n        useCollapsibleContext\n    ];\n})), \"GEAea+U+cRVxeZRny55RTbktW6U=\", false, function() {\n    return [\n        useCollapsibleContext\n    ];\n});\n_c5 = CollapsibleContent;\nCollapsibleContent.displayName = CONTENT_NAME;\nvar CollapsibleContentImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(_s3((props, forwardedRef)=>{\n    _s3();\n    const { __scopeCollapsible, present, children, ...contentProps } = props;\n    const context = useCollapsibleContext(CONTENT_NAME, __scopeCollapsible);\n    const [isPresent, setIsPresent] = react__WEBPACK_IMPORTED_MODULE_0__.useState(present);\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_8__.useComposedRefs)(forwardedRef, ref);\n    const heightRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const height = heightRef.current;\n    const widthRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const width = widthRef.current;\n    const isOpen = context.open || isPresent;\n    const isMountAnimationPreventedRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(isOpen);\n    const originalStylesRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(void 0);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"CollapsibleContentImpl.useEffect\": ()=>{\n            const rAF = requestAnimationFrame({\n                \"CollapsibleContentImpl.useEffect.rAF\": ()=>isMountAnimationPreventedRef.current = false\n            }[\"CollapsibleContentImpl.useEffect.rAF\"]);\n            return ({\n                \"CollapsibleContentImpl.useEffect\": ()=>cancelAnimationFrame(rAF)\n            })[\"CollapsibleContentImpl.useEffect\"];\n        }\n    }[\"CollapsibleContentImpl.useEffect\"], []);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_9__.useLayoutEffect)({\n        \"CollapsibleContentImpl.useLayoutEffect\": ()=>{\n            const node = ref.current;\n            if (node) {\n                originalStylesRef.current = originalStylesRef.current || {\n                    transitionDuration: node.style.transitionDuration,\n                    animationName: node.style.animationName\n                };\n                node.style.transitionDuration = \"0s\";\n                node.style.animationName = \"none\";\n                const rect = node.getBoundingClientRect();\n                heightRef.current = rect.height;\n                widthRef.current = rect.width;\n                if (!isMountAnimationPreventedRef.current) {\n                    node.style.transitionDuration = originalStylesRef.current.transitionDuration;\n                    node.style.animationName = originalStylesRef.current.animationName;\n                }\n                setIsPresent(present);\n            }\n        }\n    }[\"CollapsibleContentImpl.useLayoutEffect\"], [\n        context.open,\n        present\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_5__.Primitive.div, {\n        \"data-state\": getState(context.open),\n        \"data-disabled\": context.disabled ? \"\" : void 0,\n        id: context.contentId,\n        hidden: !isOpen,\n        ...contentProps,\n        ref: composedRefs,\n        style: {\n            [\"--radix-collapsible-content-height\"]: height ? \"\".concat(height, \"px\") : void 0,\n            [\"--radix-collapsible-content-width\"]: width ? \"\".concat(width, \"px\") : void 0,\n            ...props.style\n        },\n        children: isOpen && children\n    });\n}, \"nbxjA2hsuwppSnC645Xn2RPMdps=\", false, function() {\n    return [\n        useCollapsibleContext,\n        _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_8__.useComposedRefs\n    ];\n}));\n_c6 = CollapsibleContentImpl;\nfunction getState(open) {\n    return open ? \"open\" : \"closed\";\n}\nvar Root = Collapsible;\nvar Trigger = CollapsibleTrigger;\nvar Content = CollapsibleContent;\n //# sourceMappingURL=index.mjs.map\nvar _c, _c1, _c2, _c3, _c4, _c5, _c6;\n$RefreshReg$(_c, \"Collapsible$React.forwardRef\");\n$RefreshReg$(_c1, \"Collapsible\");\n$RefreshReg$(_c2, \"CollapsibleTrigger$React.forwardRef\");\n$RefreshReg$(_c3, \"CollapsibleTrigger\");\n$RefreshReg$(_c4, \"CollapsibleContent$React.forwardRef\");\n$RefreshReg$(_c5, \"CollapsibleContent\");\n$RefreshReg$(_c6, \"CollapsibleContentImpl\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@radix-ui/react-collapsible/dist/index.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js":
/*!******************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/circle-alert.js ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ CircleAlert)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"circle\",\n        {\n            cx: \"12\",\n            cy: \"12\",\n            r: \"10\",\n            key: \"1mglay\"\n        }\n    ],\n    [\n        \"line\",\n        {\n            x1: \"12\",\n            x2: \"12\",\n            y1: \"8\",\n            y2: \"12\",\n            key: \"1pkeuh\"\n        }\n    ],\n    [\n        \"line\",\n        {\n            x1: \"12\",\n            x2: \"12.01\",\n            y1: \"16\",\n            y2: \"16\",\n            key: \"4dfq90\"\n        }\n    ]\n];\nconst CircleAlert = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"circle-alert\", __iconNode);\n //# sourceMappingURL=circle-alert.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js":
/*!**********************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/circle-check-big.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ CircleCheckBig)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M21.801 10A10 10 0 1 1 17 3.335\",\n            key: \"yps3ct\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"m9 11 3 3L22 4\",\n            key: \"1pflzl\"\n        }\n    ]\n];\nconst CircleCheckBig = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"circle-check-big\", __iconNode);\n //# sourceMappingURL=circle-check-big.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/info.js":
/*!**********************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/info.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ Info)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"circle\",\n        {\n            cx: \"12\",\n            cy: \"12\",\n            r: \"10\",\n            key: \"1mglay\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M12 16v-4\",\n            key: \"1dtifu\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M12 8h.01\",\n            key: \"e9boi3\"\n        }\n    ]\n];\nconst Info = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"info\", __iconNode);\n //# sourceMappingURL=info.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/info.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/lock.js":
/*!**********************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/lock.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ Lock)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"rect\",\n        {\n            width: \"18\",\n            height: \"11\",\n            x: \"3\",\n            y: \"11\",\n            rx: \"2\",\n            ry: \"2\",\n            key: \"1w4ew1\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M7 11V7a5 5 0 0 1 10 0v4\",\n            key: \"fwvmzm\"\n        }\n    ]\n];\nconst Lock = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"lock\", __iconNode);\n //# sourceMappingURL=lock.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/lock.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/business-sections/BusinessSectionsGridEnhanced.tsx":
/*!***************************************************************************!*\
  !*** ./src/components/business-sections/BusinessSectionsGridEnhanced.tsx ***!
  \***************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BusinessSectionsGridEnhanced: () => (/* binding */ BusinessSectionsGridEnhanced)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_collapsible__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/collapsible */ \"(app-pages-browser)/./src/components/ui/collapsible.tsx\");\n/* harmony import */ var _components_ui_tooltip__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/tooltip */ \"(app-pages-browser)/./src/components/ui/tooltip.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Check_CheckCircle_ChevronUp_Clock_Info_Lightbulb_Lock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Check,CheckCircle,ChevronUp,Clock,Info,Lightbulb,Lock,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Check_CheckCircle_ChevronUp_Clock_Info_Lightbulb_Lock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Check,CheckCircle,ChevronUp,Clock,Info,Lightbulb,Lock,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Check_CheckCircle_ChevronUp_Clock_Info_Lightbulb_Lock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Check,CheckCircle,ChevronUp,Clock,Info,Lightbulb,Lock,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/lightbulb.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Check_CheckCircle_ChevronUp_Clock_Info_Lightbulb_Lock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Check,CheckCircle,ChevronUp,Clock,Info,Lightbulb,Lock,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Check_CheckCircle_ChevronUp_Clock_Info_Lightbulb_Lock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Check,CheckCircle,ChevronUp,Clock,Info,Lightbulb,Lock,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/lock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Check_CheckCircle_ChevronUp_Clock_Info_Lightbulb_Lock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Check,CheckCircle,ChevronUp,Clock,Info,Lightbulb,Lock,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Check_CheckCircle_ChevronUp_Clock_Info_Lightbulb_Lock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Check,CheckCircle,ChevronUp,Clock,Info,Lightbulb,Lock,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Check_CheckCircle_ChevronUp_Clock_Info_Lightbulb_Lock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Check,CheckCircle,ChevronUp,Clock,Info,Lightbulb,Lock,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Check_CheckCircle_ChevronUp_Clock_Info_Lightbulb_Lock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Check,CheckCircle,ChevronUp,Clock,Info,Lightbulb,Lock,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-up.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _lib_dependencyManager__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/dependencyManager */ \"(app-pages-browser)/./src/lib/dependencyManager.ts\");\n/* __next_internal_client_entry_do_not_use__ BusinessSectionsGridEnhanced auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n// Enhanced Item Row Component with dependency checking\nconst EnhancedItemRow = (param)=>{\n    let { item, onItemClick } = param;\n    // Check dependencies if item has them\n    const dependencyCheck = item.dependencies ? (0,_lib_dependencyManager__WEBPACK_IMPORTED_MODULE_6__.checkItemDependencies)(item.id) : null;\n    const isBlocked = dependencyCheck && !dependencyCheck.isValid;\n    const canProgress = dependencyCheck ? dependencyCheck.readyToProgress : true;\n    const getStatusStyles = function(status) {\n        let blocked = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        if (blocked) {\n            return \"bg-gray-50 dark:bg-gray-800 text-gray-400 dark:text-gray-500 opacity-60\";\n        }\n        switch(status){\n            case \"confirmed\":\n                return \"bg-green-50 dark:bg-green-900/20 text-green-900 dark:text-green-100 border-green-200 dark:border-green-700\";\n            case \"action\":\n                return \"bg-blue-50 dark:bg-blue-900/20 text-blue-900 dark:text-blue-100 border-blue-200 dark:border-blue-700\";\n            case \"idea\":\n                return \"bg-yellow-50 dark:bg-yellow-900/20 text-yellow-900 dark:text-yellow-100 border-yellow-200 dark:border-yellow-700\";\n            case \"unproven\":\n                return \"bg-gray-50 dark:bg-gray-800 text-gray-600 dark:text-gray-300 border-gray-200 dark:border-gray-600\";\n            default:\n                return \"bg-gray-100 dark:bg-background text-gray-600 dark:text-gray-300\";\n        }\n    };\n    const getStatusIcon = (status)=>{\n        switch(status){\n            case \"confirmed\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Check_CheckCircle_ChevronUp_Clock_Info_Lightbulb_Lock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    className: \"h-4 w-4 text-green-600\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                    lineNumber: 61,\n                    columnNumber: 16\n                }, undefined);\n            case \"action\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Check_CheckCircle_ChevronUp_Clock_Info_Lightbulb_Lock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    className: \"h-4 w-4 text-blue-600\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                    lineNumber: 63,\n                    columnNumber: 16\n                }, undefined);\n            case \"idea\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Check_CheckCircle_ChevronUp_Clock_Info_Lightbulb_Lock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    className: \"h-4 w-4 text-yellow-600\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                    lineNumber: 65,\n                    columnNumber: 16\n                }, undefined);\n            case \"unproven\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Check_CheckCircle_ChevronUp_Clock_Info_Lightbulb_Lock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    className: \"h-4 w-4 text-gray-400\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                    lineNumber: 67,\n                    columnNumber: 16\n                }, undefined);\n            default:\n                return null;\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_4__.TooltipProvider, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-between p-3 rounded-lg mb-3 transition-all cursor-pointer hover:shadow-md hover:scale-[1.02] \".concat(getStatusStyles(item.status, isBlocked), \" hover:bg-primary/10 dark:hover:bg-primary/20 border relative z-40 \").concat(isBlocked ? \"cursor-not-allowed\" : \"\"),\n            onClick: ()=>{\n                if (!isBlocked) {\n                    onItemClick(item);\n                }\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-3 flex-1 min-w-0\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-shrink-0\",\n                            children: isBlocked ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_4__.Tooltip, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_4__.TooltipTrigger, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Check_CheckCircle_ChevronUp_Clock_Info_Lightbulb_Lock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"h-4 w-4 text-gray-400\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                                            lineNumber: 94,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                                        lineNumber: 93,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_4__.TooltipContent, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"Blocked by dependencies:\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                                                lineNumber: 97,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"text-xs mt-1\",\n                                                children: dependencyCheck === null || dependencyCheck === void 0 ? void 0 : dependencyCheck.missingDependencies.map((dep, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            \"• \",\n                                                            dep\n                                                        ]\n                                                    }, index, true, {\n                                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                                                        lineNumber: 100,\n                                                        columnNumber: 23\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                                                lineNumber: 98,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                                        lineNumber: 96,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                                lineNumber: 92,\n                                columnNumber: 15\n                            }, undefined) : getStatusIcon(item.status)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                            lineNumber: 90,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 min-w-0\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-medium text-sm truncate\",\n                                            children: item.title\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                                            lineNumber: 113,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        item.dependencies && item.dependencies.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_4__.Tooltip, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_4__.TooltipTrigger, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Check_CheckCircle_ChevronUp_Clock_Info_Lightbulb_Lock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"h-3 w-3 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                                                        lineNumber: 119,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                                                    lineNumber: 118,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_4__.TooltipContent, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs\",\n                                                            children: \"Depends on:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                                                            lineNumber: 122,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                            className: \"text-xs mt-1\",\n                                                            children: item.dependencies.map((dep, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    children: [\n                                                                        \"• \",\n                                                                        dep\n                                                                    ]\n                                                                }, index, true, {\n                                                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                                                                    lineNumber: 125,\n                                                                    columnNumber: 25\n                                                                }, undefined))\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                                                            lineNumber: 123,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                                                    lineNumber: 121,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                                            lineNumber: 117,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        item.inputType && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_1__.Badge, {\n                                            variant: \"outline\",\n                                            className: \"text-xs px-1 py-0 h-4\",\n                                            children: item.inputType\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                                            lineNumber: 134,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                                    lineNumber: 112,\n                                    columnNumber: 13\n                                }, undefined),\n                                item.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-muted-foreground mt-1 line-clamp-1\",\n                                    children: item.description\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                                    lineNumber: 142,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                            lineNumber: 111,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                    lineNumber: 88,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-1 flex-shrink-0\",\n                    children: [\n                        item.status !== \"unproven\" && !isBlocked && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                item.actions > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_1__.Badge, {\n                                    variant: \"secondary\",\n                                    className: \"bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 text-xs px-1.5 border border-blue-200 dark:border-blue-700 h-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Check_CheckCircle_ChevronUp_Clock_Info_Lightbulb_Lock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"h-3 w-3\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                                            lineNumber: 155,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        item.actions > 1 ? item.actions : ''\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                                    lineNumber: 154,\n                                    columnNumber: 17\n                                }, undefined),\n                                item.ideas > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_1__.Badge, {\n                                    variant: \"secondary\",\n                                    className: \"bg-yellow-50 dark:bg-yellow-500 text-black dark:text-white text-xs px-1.5 py-0.5 border border-yellow-300 dark:border-yellow-700 h-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Check_CheckCircle_ChevronUp_Clock_Info_Lightbulb_Lock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"h-3 w-3\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                                            lineNumber: 161,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        item.ideas > 1 ? item.ideas : ''\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                                    lineNumber: 160,\n                                    columnNumber: 17\n                                }, undefined),\n                                item.results > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_1__.Badge, {\n                                    variant: \"secondary\",\n                                    className: \"bg-green-100 dark:bg-green-900 text-green-900 dark:text-green-200 text-xs px-1.5 border border-green-700 dark:border-green-600 h-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Check_CheckCircle_ChevronUp_Clock_Info_Lightbulb_Lock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                                            lineNumber: 167,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        item.results > 1 ? item.results : ''\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                                    lineNumber: 166,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true),\n                        isBlocked && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_1__.Badge, {\n                            variant: \"secondary\",\n                            className: \"bg-red-50 dark:bg-red-900/20 text-red-600 dark:text-red-400 text-xs px-1.5 border border-red-200 dark:border-red-700 h-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Check_CheckCircle_ChevronUp_Clock_Info_Lightbulb_Lock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    className: \"h-3 w-3\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                                    lineNumber: 177,\n                                    columnNumber: 15\n                                }, undefined),\n                                \"Blocked\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                            lineNumber: 176,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                    lineNumber: 150,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n            lineNumber: 75,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n        lineNumber: 74,\n        columnNumber: 5\n    }, undefined);\n};\n_c = EnhancedItemRow;\n// Enhanced Expandable Card Component\nconst EnhancedExpandableCard = (param)=>{\n    let { section, onItemClick } = param;\n    _s();\n    const [isExpanded, setIsExpanded] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(true);\n    // Calculate section progress\n    const totalItems = section.items.length;\n    const confirmedItems = section.items.filter((item)=>item.status === \"confirmed\").length;\n    const progressPercentage = totalItems > 0 ? Math.round(confirmedItems / totalItems * 100) : 0;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n        className: \"bg-white dark:bg-card border border-gray-200 dark:border-border shadow-lg hover:shadow-xl transition-all duration-200 h-fit py-0 relative z-40\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_3__.Collapsible, {\n            open: isExpanded,\n            onOpenChange: setIsExpanded,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_3__.CollapsibleTrigger, {\n                    asChild: true,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        className: \"cursor-pointer hover:bg-gray-50 dark:hover:bg-background transition-colors pb-2 px-4 py-2 my-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                            className: \"text-lg font-semibold text-gray-900 dark:text-gray-100\",\n                                            children: section.title\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                                            lineNumber: 209,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_1__.Badge, {\n                                            variant: \"outline\",\n                                            className: \"text-xs\",\n                                            children: [\n                                                confirmedItems,\n                                                \"/\",\n                                                totalItems\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                                            lineNumber: 212,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                                    lineNumber: 208,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-muted-foreground\",\n                                            children: [\n                                                progressPercentage,\n                                                \"%\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                                            lineNumber: 217,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Check_CheckCircle_ChevronUp_Clock_Info_Lightbulb_Lock_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"h-5 w-5 text-gray-500 dark:text-gray-400 transition-transform \".concat(isExpanded ? \"rotate-0\" : \"rotate-180\")\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                                            lineNumber: 218,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                                    lineNumber: 216,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                            lineNumber: 207,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                        lineNumber: 206,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                    lineNumber: 205,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_3__.CollapsibleContent, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        className: \"py-0 px-3\",\n                        children: section.items.sort((a, b)=>(a.order || 0) - (b.order || 0)).map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(EnhancedItemRow, {\n                                item: item,\n                                onItemClick: onItemClick\n                            }, item.id, false, {\n                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                                lineNumber: 232,\n                                columnNumber: 17\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                        lineNumber: 228,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                    lineNumber: 227,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n            lineNumber: 204,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n        lineNumber: 203,\n        columnNumber: 5\n    }, undefined);\n};\n_s(EnhancedExpandableCard, \"MzqrZ0LJxgqPa6EOF1Vxw0pgYA4=\");\n_c1 = EnhancedExpandableCard;\nfunction BusinessSectionsGridEnhanced(param) {\n    let { sections, onItemClick } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full relative z-40\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"mb-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 gap-3 auto-rows-min\",\n                children: sections.map((section)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(EnhancedExpandableCard, {\n                        section: section,\n                        onItemClick: onItemClick\n                    }, section.id, false, {\n                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                        lineNumber: 255,\n                        columnNumber: 13\n                    }, this))\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                lineNumber: 253,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n            lineNumber: 252,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n        lineNumber: 251,\n        columnNumber: 5\n    }, this);\n}\n_c2 = BusinessSectionsGridEnhanced;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"EnhancedItemRow\");\n$RefreshReg$(_c1, \"EnhancedExpandableCard\");\n$RefreshReg$(_c2, \"BusinessSectionsGridEnhanced\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/business-sections/BusinessSectionsGridEnhanced.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/project/ProjectMainContent.tsx":
/*!*******************************************************!*\
  !*** ./src/components/project/ProjectMainContent.tsx ***!
  \*******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ProjectMainContent: () => (/* binding */ ProjectMainContent)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _lib_constants__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/constants */ \"(app-pages-browser)/./src/lib/constants.ts\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _business_item_table__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../business-item-table */ \"(app-pages-browser)/./src/components/business-item-table.tsx\");\n/* harmony import */ var _business_sections_BusinessSectionsGridEnhanced__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../business-sections/BusinessSectionsGridEnhanced */ \"(app-pages-browser)/./src/components/business-sections/BusinessSectionsGridEnhanced.tsx\");\n/* harmony import */ var _ContentSections__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./ContentSections */ \"(app-pages-browser)/./src/components/project/ContentSections.tsx\");\n/* __next_internal_client_entry_do_not_use__ ProjectMainContent auto */ \n\n\n\n\n\n\nfunction ProjectMainContent(param) {\n    let { activeContent, setActiveContent, mockDraftItems, mockFileItems, selectedItem, itemDetails, sections, isLoading, error, onBusinessItemClick, onBackToItems } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex-1 min-w-0 min-h-0 w-full max-w-full p-4 pb-20 overflow-y-auto bg-gray-50 dark:bg-gray-900 relative transition-all duration-300 ease-in-out main-content-bottom-fade\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 project-main-content pointer-events-none\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectMainContent.tsx\",\n                lineNumber: 40,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-30\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ContentSections__WEBPACK_IMPORTED_MODULE_5__.ContentSections, {\n                        activeContent: activeContent,\n                        setActiveContent: setActiveContent,\n                        mockDraftItems: mockDraftItems,\n                        mockFileItems: mockFileItems\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectMainContent.tsx\",\n                        lineNumber: 45,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.AnimatePresence, {\n                        mode: \"wait\",\n                        children: [\n                            !activeContent && !selectedItem && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                exit: {\n                                    opacity: 0,\n                                    y: -20\n                                },\n                                transition: {\n                                    duration: 0.3,\n                                    ease: \"easeInOut\"\n                                },\n                                children: [\n                                    isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center h-64\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"animate-spin rounded-full \".concat(_lib_constants__WEBPACK_IMPORTED_MODULE_2__.ICON_SIZES.lg, \" border-b-2 border-gray-900 mx-auto mb-4\")\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectMainContent.tsx\",\n                                                    lineNumber: 66,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600\",\n                                                    children: \"Loading business sections...\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectMainContent.tsx\",\n                                                    lineNumber: 69,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectMainContent.tsx\",\n                                            lineNumber: 65,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectMainContent.tsx\",\n                                        lineNumber: 64,\n                                        columnNumber: 17\n                                    }, this),\n                                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center h-64\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-red-600 mb-4\",\n                                                    children: error\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectMainContent.tsx\",\n                                                    lineNumber: 80,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                                    onClick: ()=>window.location.reload(),\n                                                    children: \"Try Again\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectMainContent.tsx\",\n                                                    lineNumber: 81,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectMainContent.tsx\",\n                                            lineNumber: 79,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectMainContent.tsx\",\n                                        lineNumber: 78,\n                                        columnNumber: 17\n                                    }, this),\n                                    !isLoading && !error && sections.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_business_sections_BusinessSectionsGridEnhanced__WEBPACK_IMPORTED_MODULE_4__.BusinessSectionsGridEnhanced, {\n                                        sections: sections,\n                                        onItemClick: onBusinessItemClick\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectMainContent.tsx\",\n                                        lineNumber: 90,\n                                        columnNumber: 17\n                                    }, this),\n                                    !isLoading && !error && sections.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center h-64\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600 mb-4\",\n                                                    children: \"No business sections found\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectMainContent.tsx\",\n                                                    lineNumber: 100,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                                    onClick: ()=>window.location.reload(),\n                                                    children: \"Reload\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectMainContent.tsx\",\n                                                    lineNumber: 103,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectMainContent.tsx\",\n                                            lineNumber: 99,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectMainContent.tsx\",\n                                        lineNumber: 98,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, \"business-sections\", true, {\n                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectMainContent.tsx\",\n                                lineNumber: 55,\n                                columnNumber: 13\n                            }, this),\n                            !activeContent && selectedItem && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    x: 30\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    x: 0\n                                },\n                                exit: {\n                                    opacity: 0,\n                                    x: -30\n                                },\n                                transition: {\n                                    duration: 0.3,\n                                    ease: \"easeInOut\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_business_item_table__WEBPACK_IMPORTED_MODULE_3__.BusinessItemTable, {\n                                    itemDetails: itemDetails,\n                                    selectedBusinessItem: selectedItem,\n                                    onBackToItems: onBackToItems\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectMainContent.tsx\",\n                                    lineNumber: 121,\n                                    columnNumber: 15\n                                }, this)\n                            }, \"business-detail\", false, {\n                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectMainContent.tsx\",\n                                lineNumber: 114,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectMainContent.tsx\",\n                        lineNumber: 52,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectMainContent.tsx\",\n                lineNumber: 43,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectMainContent.tsx\",\n        lineNumber: 38,\n        columnNumber: 5\n    }, this);\n}\n_c = ProjectMainContent;\nvar _c;\n$RefreshReg$(_c, \"ProjectMainContent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/project/ProjectMainContent.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ui/collapsible.tsx":
/*!*******************************************!*\
  !*** ./src/components/ui/collapsible.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Collapsible: () => (/* binding */ Collapsible),\n/* harmony export */   CollapsibleContent: () => (/* binding */ CollapsibleContent),\n/* harmony export */   CollapsibleTrigger: () => (/* binding */ CollapsibleTrigger)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _radix_ui_react_collapsible__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @radix-ui/react-collapsible */ \"(app-pages-browser)/./node_modules/@radix-ui/react-collapsible/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ Collapsible,CollapsibleTrigger,CollapsibleContent auto */ \n\nfunction Collapsible(param) {\n    let { ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_collapsible__WEBPACK_IMPORTED_MODULE_1__.Root, {\n        \"data-slot\": \"collapsible\",\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/ui/collapsible.tsx\",\n        lineNumber: 8,\n        columnNumber: 10\n    }, this);\n}\n_c = Collapsible;\nfunction CollapsibleTrigger(param) {\n    let { ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_collapsible__WEBPACK_IMPORTED_MODULE_1__.CollapsibleTrigger, {\n        \"data-slot\": \"collapsible-trigger\",\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/ui/collapsible.tsx\",\n        lineNumber: 15,\n        columnNumber: 5\n    }, this);\n}\n_c1 = CollapsibleTrigger;\nfunction CollapsibleContent(param) {\n    let { ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_collapsible__WEBPACK_IMPORTED_MODULE_1__.CollapsibleContent, {\n        \"data-slot\": \"collapsible-content\",\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/ui/collapsible.tsx\",\n        lineNumber: 26,\n        columnNumber: 5\n    }, this);\n}\n_c2 = CollapsibleContent;\n\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"Collapsible\");\n$RefreshReg$(_c1, \"CollapsibleTrigger\");\n$RefreshReg$(_c2, \"CollapsibleContent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/collapsible.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/dependencyManager.ts":
/*!**************************************!*\
  !*** ./src/lib/dependencyManager.ts ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DependencyManager: () => (/* binding */ DependencyManager),\n/* harmony export */   checkItemDependencies: () => (/* binding */ checkItemDependencies),\n/* harmony export */   dependencyManager: () => (/* binding */ dependencyManager),\n/* harmony export */   getBlockedItems: () => (/* binding */ getBlockedItems),\n/* harmony export */   getCompletionStats: () => (/* binding */ getCompletionStats),\n/* harmony export */   getReadyItems: () => (/* binding */ getReadyItems),\n/* harmony export */   getSuggestedNextItems: () => (/* binding */ getSuggestedNextItems)\n/* harmony export */ });\n/* harmony import */ var _data_businessItemsData__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/data/businessItemsData */ \"(app-pages-browser)/./src/data/businessItemsData.ts\");\n// Dependency management system for business items\n\nclass DependencyManager {\n    // Check if an item's dependencies are satisfied\n    checkDependencies(itemId) {\n        const item = this.getItem(itemId);\n        if (!item) {\n            return {\n                isValid: false,\n                missingDependencies: [],\n                readyToProgress: false,\n                message: \"Item not found\"\n            };\n        }\n        if (!item.dependencies || item.dependencies.length === 0) {\n            return {\n                isValid: true,\n                missingDependencies: [],\n                readyToProgress: true,\n                message: \"No dependencies required\"\n            };\n        }\n        const missingDependencies = item.dependencies.filter((depId)=>{\n            const depItem = this.getItem(depId);\n            return !depItem || depItem.status !== \"confirmed\";\n        });\n        const isValid = missingDependencies.length === 0;\n        return {\n            isValid,\n            missingDependencies,\n            readyToProgress: isValid && item.status !== \"confirmed\",\n            message: isValid ? \"All dependencies satisfied\" : \"Missing: \".concat(missingDependencies.map((id)=>{\n                var _this_getItem;\n                return ((_this_getItem = this.getItem(id)) === null || _this_getItem === void 0 ? void 0 : _this_getItem.title) || id;\n            }).join(\", \"))\n        };\n    }\n    // Get all items that depend on a specific item\n    getDependents(itemId) {\n        return this.items.filter((item)=>{\n            var _item_dependencies;\n            return (_item_dependencies = item.dependencies) === null || _item_dependencies === void 0 ? void 0 : _item_dependencies.includes(itemId);\n        }).map((item)=>item.id);\n    }\n    // Get dependency status for an item\n    getDependencyStatus(itemId) {\n        var _item_dependencies;\n        const item = this.getItem(itemId);\n        const validation = this.checkDependencies(itemId);\n        const dependents = this.getDependents(itemId);\n        if (!item) {\n            return {\n                itemId,\n                canProgress: false,\n                blockedBy: [],\n                dependents: [],\n                completionPercentage: 0\n            };\n        }\n        const completionPercentage = ((_item_dependencies = item.dependencies) === null || _item_dependencies === void 0 ? void 0 : _item_dependencies.length) ? (item.dependencies.length - validation.missingDependencies.length) / item.dependencies.length * 100 : 100;\n        return {\n            itemId,\n            canProgress: validation.readyToProgress,\n            blockedBy: validation.missingDependencies,\n            dependents,\n            completionPercentage\n        };\n    }\n    // Get all items ready to work on (dependencies satisfied, not completed)\n    getReadyItems() {\n        return this.items.filter((item)=>{\n            const validation = this.checkDependencies(item.id);\n            return validation.readyToProgress;\n        }).map((item)=>item.id);\n    }\n    // Get items blocked by dependencies\n    getBlockedItems() {\n        return this.items.filter((item)=>{\n            const validation = this.checkDependencies(item.id);\n            return !validation.isValid && item.status !== \"confirmed\";\n        }).map((item)=>item.id);\n    }\n    // Get dependency chain for an item (all items it depends on, recursively)\n    getDependencyChain(itemId) {\n        let visited = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : new Set();\n        if (visited.has(itemId)) return []; // Prevent circular dependencies\n        const item = this.getItem(itemId);\n        if (!item || !item.dependencies) return [];\n        visited.add(itemId);\n        const chain = [];\n        for (const depId of item.dependencies){\n            chain.push(depId);\n            const subChain = this.getDependencyChain(depId, visited);\n            chain.push(...subChain);\n        }\n        return [\n            ...new Set(chain)\n        ]; // Remove duplicates\n    }\n    // Get impact chain for an item (all items that depend on it, recursively)\n    getImpactChain(itemId) {\n        let visited = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : new Set();\n        if (visited.has(itemId)) return []; // Prevent circular dependencies\n        visited.add(itemId);\n        const dependents = this.getDependents(itemId);\n        const chain = [];\n        for (const depId of dependents){\n            chain.push(depId);\n            const subChain = this.getImpactChain(depId, visited);\n            chain.push(...subChain);\n        }\n        return [\n            ...new Set(chain)\n        ]; // Remove duplicates\n    }\n    // Validate if changing an item's status would break dependencies\n    validateStatusChange(itemId, newStatus) {\n        const item = this.getItem(itemId);\n        if (!item) {\n            return {\n                isValid: false,\n                missingDependencies: [],\n                readyToProgress: false,\n                message: \"Item not found\"\n            };\n        }\n        // If changing from confirmed to something else, check impact\n        if (item.status === \"confirmed\" && newStatus !== \"confirmed\") {\n            const impactChain = this.getImpactChain(itemId);\n            const affectedConfirmed = impactChain.filter((id)=>{\n                const affectedItem = this.getItem(id);\n                return (affectedItem === null || affectedItem === void 0 ? void 0 : affectedItem.status) === \"confirmed\";\n            });\n            if (affectedConfirmed.length > 0) {\n                return {\n                    isValid: false,\n                    missingDependencies: [],\n                    readyToProgress: false,\n                    message: \"Cannot change status: \".concat(affectedConfirmed.length, \" confirmed items depend on this\")\n                };\n            }\n        }\n        // If changing to confirmed, check dependencies\n        if (newStatus === \"confirmed\") {\n            return this.checkDependencies(itemId);\n        }\n        return {\n            isValid: true,\n            missingDependencies: [],\n            readyToProgress: true,\n            message: \"Status change is valid\"\n        };\n    }\n    // Get suggested next items to work on\n    getSuggestedNextItems() {\n        let limit = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 5;\n        const readyItems = this.getReadyItems();\n        // Sort by priority: items with more dependents should be prioritized\n        return readyItems.map((itemId)=>{\n            var _this_getItem;\n            return {\n                itemId,\n                dependentCount: this.getDependents(itemId).length,\n                order: ((_this_getItem = this.getItem(itemId)) === null || _this_getItem === void 0 ? void 0 : _this_getItem.order) || 999\n            };\n        }).sort((a, b)=>{\n            // First by dependent count (descending), then by order (ascending)\n            if (a.dependentCount !== b.dependentCount) {\n                return b.dependentCount - a.dependentCount;\n            }\n            return a.order - b.order;\n        }).slice(0, limit).map((item)=>item.itemId);\n    }\n    // Get completion statistics\n    getCompletionStats() {\n        const total = this.items.length;\n        const confirmed = this.items.filter((item)=>item.status === \"confirmed\").length;\n        const ready = this.getReadyItems().length;\n        const blocked = this.getBlockedItems().length;\n        return {\n            total,\n            confirmed,\n            ready,\n            blocked,\n            completionPercentage: confirmed / total * 100,\n            readyPercentage: ready / total * 100,\n            blockedPercentage: blocked / total * 100\n        };\n    }\n    getItem(itemId) {\n        return this.items.find((item)=>item.id === itemId);\n    }\n    constructor(items = _data_businessItemsData__WEBPACK_IMPORTED_MODULE_0__.businessItemsData){\n        this.items = items;\n    }\n}\n// Export singleton instance\nconst dependencyManager = new DependencyManager();\n// Helper functions for easy access\nconst checkItemDependencies = (itemId)=>dependencyManager.checkDependencies(itemId);\nconst getReadyItems = ()=>dependencyManager.getReadyItems();\nconst getBlockedItems = ()=>dependencyManager.getBlockedItems();\nconst getSuggestedNextItems = (limit)=>dependencyManager.getSuggestedNextItems(limit);\nconst getCompletionStats = ()=>dependencyManager.getCompletionStats();\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/dependencyManager.ts\n"));

/***/ })

});