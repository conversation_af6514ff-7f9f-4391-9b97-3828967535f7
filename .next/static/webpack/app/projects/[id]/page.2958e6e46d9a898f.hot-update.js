"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/projects/[id]/page",{

/***/ "(app-pages-browser)/./src/components/project/ProjectDetailHeader.tsx":
/*!********************************************************!*\
  !*** ./src/components/project/ProjectDetailHeader.tsx ***!
  \********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ProjectDetailHeader: () => (/* binding */ ProjectDetailHeader)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_ui_popover__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/popover */ \"(app-pages-browser)/./src/components/ui/popover.tsx\");\n/* harmony import */ var _components_ui_sidebar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/sidebar */ \"(app-pages-browser)/./src/components/ui/sidebar.tsx\");\n/* harmony import */ var _components_ui_sidebar_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/sidebar-button */ \"(app-pages-browser)/./src/components/ui/sidebar-button.tsx\");\n/* harmony import */ var _hooks_useAnalytics__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/useAnalytics */ \"(app-pages-browser)/./src/hooks/useAnalytics.ts\");\n/* harmony import */ var _lib_constants__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/constants */ \"(app-pages-browser)/./src/lib/constants.ts\");\n/* harmony import */ var _mockdata_businessItemQuestions__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/mockdata/businessItemQuestions */ \"(app-pages-browser)/./src/mockdata/businessItemQuestions.ts\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_HelpCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,HelpCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_HelpCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,HelpCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-question-mark.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_7__);\n/* __next_internal_client_entry_do_not_use__ ProjectDetailHeader auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction ProjectDetailHeader(param) {\n    let { selectedBusinessItem, onBackToItems } = param;\n    _s();\n    const { trackClick, trackCustomEvent } = (0,_hooks_useAnalytics__WEBPACK_IMPORTED_MODULE_4__.useAnalytics)();\n    const [isHelpOpen, setIsHelpOpen] = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(false);\n    const [, setIsInfoOpen] = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(false);\n    const { state, isMobile } = (0,_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_2__.useSidebar)();\n    // Get dynamic question and answer based on business item\n    const { question, answer } = (0,_mockdata_businessItemQuestions__WEBPACK_IMPORTED_MODULE_6__.getBusinessItemQA)(selectedBusinessItem);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"flex h-20 shrink-0 items-center gap-2 transition-[width] ease-linear border-b border-border\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-between w-full h-full px-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-4 h-full\",\n                    children: [\n                        isMobile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar_button__WEBPACK_IMPORTED_MODULE_3__.SidebarButton, {\n                            onClick: ()=>{\n                                trackClick(\"back-to-items\", \"project-detail-header\");\n                                trackCustomEvent(\"navigation_clicked\", {\n                                    destination: \"items\",\n                                    from_page: \"item-detail\",\n                                    location: \"header\"\n                                });\n                                onBackToItems();\n                            },\n                            icon: _barrel_optimize_names_ArrowLeft_HelpCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n                            variant: \"ghost\",\n                            size: \"lg\",\n                            layout: \"icon-only\",\n                            showBorder: true,\n                            hoverColor: \"grey\",\n                            hoverScale: true,\n                            iconClassName: _lib_constants__WEBPACK_IMPORTED_MODULE_5__.ICON_SIZES.lg\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                            lineNumber: 41,\n                            columnNumber: 13\n                        }, this),\n                        state === \"collapsed\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-xl font-semibold text-gray-900 dark:text-gray-100\",\n                            children: (selectedBusinessItem === null || selectedBusinessItem === void 0 ? void 0 : selectedBusinessItem.title) || \"Untitled Item\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                            lineNumber: 62,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                    lineNumber: 39,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 flex flex-col justify-center mr-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2 mb-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_1__.Popover, {\n                                open: isInfoOpen,\n                                onOpenChange: setIsInfoOpen,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_1__.PopoverTrigger, {\n                                        asChild: true,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"flex items-center justify-center w-4 h-4 rounded-full bg-blue-100 dark:bg-blue-900 hover:bg-blue-200 dark:hover:bg-blue-800 transition-colors\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-2.5 h-2.5 text-blue-600 dark:text-blue-400\",\n                                                fill: \"currentColor\",\n                                                viewBox: \"0 0 20 20\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    fillRule: \"evenodd\",\n                                                    d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z\",\n                                                    clipRule: \"evenodd\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                                                    lineNumber: 80,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                                                lineNumber: 75,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                                            lineNumber: 74,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                                        lineNumber: 73,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_1__.PopoverContent, {\n                                        align: \"start\",\n                                        side: \"bottom\",\n                                        className: \"w-80\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-semibold text-sm\",\n                                                    children: \"Description\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                                                    lineNumber: 90,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-600 dark:text-gray-400 leading-relaxed\",\n                                                    children: answer\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                                                    lineNumber: 91,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                                            lineNumber: 89,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                                        lineNumber: 88,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                                lineNumber: 72,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm font-bold text-[var(--brand-dark)] dark:text-[var(--primary)]\",\n                                children: question\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                                lineNumber: 97,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                        lineNumber: 71,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                    lineNumber: 69,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-2 h-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_1__.Popover, {\n                        open: isHelpOpen,\n                        onOpenChange: setIsHelpOpen,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_1__.PopoverTrigger, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar_button__WEBPACK_IMPORTED_MODULE_3__.SidebarButton, {\n                                    icon: _barrel_optimize_names_ArrowLeft_HelpCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n                                    variant: \"ghost\",\n                                    size: \"lg\",\n                                    layout: \"icon-only\",\n                                    showBorder: true,\n                                    hoverColor: \"grey\",\n                                    hoverScale: true,\n                                    onClick: ()=>{\n                                        trackClick(\"help-button\", \"project-header\");\n                                        trackCustomEvent(\"help_clicked\", {\n                                            from_item: selectedBusinessItem === null || selectedBusinessItem === void 0 ? void 0 : selectedBusinessItem.title,\n                                            location: \"header\"\n                                        });\n                                    },\n                                    iconClassName: _lib_constants__WEBPACK_IMPORTED_MODULE_5__.ICON_SIZES.lg\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                                    lineNumber: 107,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                                lineNumber: 106,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_1__.PopoverContent, {\n                                align: \"end\",\n                                side: \"bottom\",\n                                className: \"w-80\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold text-sm\",\n                                            children: \"Table Guide\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                                            lineNumber: 127,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2 text-xs\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2 p-2 border-gray-200 dark:border-gray-700 rounded-md border-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-blue-500 rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                                                            lineNumber: 130,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    children: \"Idea:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                                                                    lineNumber: 132,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \" What is the main idea of the item?\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                                                            lineNumber: 131,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                                                    lineNumber: 129,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2 p-2 border-gray-200 dark:border-gray-700 rounded-md border-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-yellow-500 rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                                                            lineNumber: 136,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    children: \"Action:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                                                                    lineNumber: 138,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \" What was done to achieve the idea?\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                                                            lineNumber: 137,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                                                    lineNumber: 135,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2 p-2 border-gray-200 dark:border-gray-700 rounded-md border-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-green-500 rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                                                            lineNumber: 143,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    children: \"Result:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                                                                    lineNumber: 145,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \" What was the outcome of the action?\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                                                            lineNumber: 144,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                                                    lineNumber: 142,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2 p-2 border-gray-200 dark:border-gray-700 rounded-md border-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-purple-500 rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                                                            lineNumber: 150,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    children: \"Status:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                                                                    lineNumber: 152,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \" Current state\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                                                            lineNumber: 151,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                                                    lineNumber: 149,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                                            lineNumber: 128,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"pt-2 border-t border-gray-200 dark:border-gray-700\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-gray-600 dark:text-gray-400\",\n                                                children: \"Click cells to edit • Drag rows to reorder • Use + to add items\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                                                lineNumber: 157,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                                            lineNumber: 156,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                                    lineNumber: 126,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                                lineNumber: 125,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                        lineNumber: 105,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                    lineNumber: 104,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n            lineNumber: 37,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n        lineNumber: 36,\n        columnNumber: 5\n    }, this);\n}\n_s(ProjectDetailHeader, \"7lZy3vBaZREpA2UBp3X4NQhC6jc=\", false, function() {\n    return [\n        _hooks_useAnalytics__WEBPACK_IMPORTED_MODULE_4__.useAnalytics,\n        _components_ui_sidebar__WEBPACK_IMPORTED_MODULE_2__.useSidebar\n    ];\n});\n_c = ProjectDetailHeader;\nvar _c;\n$RefreshReg$(_c, \"ProjectDetailHeader\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/project/ProjectDetailHeader.tsx\n"));

/***/ })

});