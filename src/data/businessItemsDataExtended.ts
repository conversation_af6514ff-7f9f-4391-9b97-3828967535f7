// Extended business items data - remaining items
import type { BusinessItemData } from "../types/BusinessItemData.types";

export const extendedBusinessItems: BusinessItemData[] = [
  // Sales & Marketing (continued)
  {
    id: "brand",
    title: "Brand",
    description:
      "The personality, tone, style, visual identity that shape how customers perceive and connect with your business, mainly informed by the positioning and why.",
    question: "What defines {PROJECT NAME}'s identity?",
    guidance:
      "Align identity, tone, visuals to audience values; embed chosen archetype; ensure coherence.",
    category: "Sales & Marketing",
    order: 17,
    inputType: "Suggest",
    dependencies: [
      "problem",
      "audience",
      "unique-value-proposition",
      "alternatives",
      "trends",
      "why",
      "advantages",
      "product",
      "channels",
      "tech",
      "business-model",
      "messaging",
    ],
    status: "unproven",
    values: [
      "Professional yet approachable, data-driven, reliability-focused with clean modern design",
      "Innovative, human-centered, productivity-focused with warm collaborative aesthetics",
      "Authentic, earth-conscious, transparency-focused with natural sustainable design",
    ],
    actions: 0,
    ideas: 0,
    results: 0,
  },
  {
    id: "assets",
    title: "Assets",
    description:
      "Tangible and intangible resources—like IP, partnerships, or design assets like graphics, logos, websites, videos, content and more—that you leverage to deliver your offering",
    question: "What is valuable in {PROJECT NAME}?",
    guidance:
      "List IP, data, partnerships; rate strategic value & defendability; plan leverage.",
    category: "Sales & Marketing",
    order: 18,
    inputType: "Suggest",
    dependencies: [
      "problem",
      "audience",
      "unique-value-proposition",
      "alternatives",
      "trends",
      "why",
      "advantages",
      "product",
      "channels",
      "tech",
      "brand",
      "messaging",
    ],
    status: "unproven",
    values: [
      "Proprietary ML algorithms, retail industry partnerships, comprehensive product database",
      "Collaboration research IP, enterprise customer testimonials, integration partnerships",
      "Sustainability certification database, influencer network, verified impact data",
    ],
    actions: 0,
    ideas: 0,
    results: 0,
  },
  {
    id: "sales-motion",
    title: "Sales Motion",
    description:
      "The sequence of steps, strategies, timelines, communications your team employs to convert prospects into paying customers.",
    question: "What're the steps to making a sale for {PROJECT NAME}?",
    guidance:
      "Map buyer journey steps, owners, SLAs; include feedback loops; aim for velocity & expansion.",
    category: "Sales & Marketing",
    order: 19,
    inputType: "Suggest",
    dependencies: [
      "problem",
      "audience",
      "unique-value-proposition",
      "alternatives",
      "trends",
      "why",
      "advantages",
      "product",
      "channels",
      "tech",
      "brand",
      "messaging",
      "assets",
    ],
    status: "unproven",
    values: [
      "Demo request → needs assessment → pilot program → ROI analysis → contract negotiation → onboarding",
      "Free trial signup → usage tracking → feature adoption → upgrade prompts → sales call → conversion",
      "Content engagement → email nurture → webinar attendance → consultation call → subscription signup",
    ],
    actions: 0,
    ideas: 0,
    results: 0,
  },
  {
    id: "metrics",
    title: "Metrics",
    description:
      "Key performance indicators and data points you monitor to track progress, performance, and business health.",
    question: "How would {PROJECT NAME} measure success?",
    guidance:
      "Pick north-star + 3 inputs; link to problem & revenue; set baselines and review cadence.",
    category: "Sales & Marketing",
    order: 20,
    inputType: "Suggest",
    dependencies: [
      "problem",
      "audience",
      "unique-value-proposition",
      "alternatives",
      "trends",
      "why",
      "advantages",
      "product",
      "channels",
      "tech",
      "brand",
      "messaging",
      "assets",
      "sales-motion",
    ],
    status: "unproven",
    values: [
      "Monthly recurring revenue, customer acquisition cost, inventory accuracy improvement, churn rate",
      "Daily active users, feature adoption rate, customer satisfaction score, expansion revenue",
      "Sustainability impact score, user engagement rate, marketplace transaction volume, retention rate",
    ],
    actions: 0,
    ideas: 0,
    results: 0,
  },

  // Company Category
  {
    id: "risks",
    title: "Risks",
    description:
      "Internal and external threats—trends, alternatives, financial, operational, business model—that could jeopardize your business success.",
    question: "What are the unique risks for {PROJECT NAME}?",
    guidance:
      "Run pre-mortem: list top 5 risks with likelihood/impact; propose mitigations & monitors.",
    category: "Company",
    order: 12,
    inputType: "Auto",
    dependencies: [
      "problem",
      "audience",
      "unique-value-proposition",
      "alternatives",
      "trends",
      "why",
      "advantages",
      "channels",
      "messaging",
    ],
    status: "unproven",
    values: [
      "Data privacy regulations, large competitor entry, economic downturn affecting SMB spending",
      "Remote work trend reversal, security breaches, enterprise sales cycle delays",
      "Greenwashing backlash, supply chain verification challenges, consumer behavior shifts",
    ],
    actions: 0,
    ideas: 0,
    results: 0,
  },
  {
    id: "business-model",
    title: "Business Model",
    description:
      "The financial model outlining how you create, deliver, and capture value—basic catogories are subscriptions, one-time fees, royalties, re-selling, usage-based, and consulting, deeply coupled with your packages and product",
    question: "What's {PROJECT NAME}'s business model?",
    guidance:
      "Clarify value capture & pricing logic; map cost drivers; run sensitivity scenarios.",
    category: "Company",
    order: 15,
    inputType: "Suggest",
    dependencies: [
      "problem",
      "audience",
      "unique-value-proposition",
      "alternatives",
      "trends",
      "why",
      "advantages",
      "channels",
      "product",
      "tech",
    ],
    status: "unproven",
    values: [
      "SaaS subscription model with tiered pricing based on SKU volume and feature access",
      "Freemium with usage-based pricing for advanced features and enterprise add-ons",
      "Subscription + transaction fees for marketplace purchases with sustainability verification",
    ],
    actions: 0,
    ideas: 0,
    results: 0,
  },
  {
    id: "revenue",
    title: "Revenue",
    description:
      "The various income streams generated from your product or service sales—typically estimated from your business model, audience, alternatives, packaging and channels.",
    question: "What's the revenue forecast for {PROJECT NAME}?",
    guidance:
      "Itemize streams, price drivers; model MRR/ARR & margins; flag dependencies.",
    category: "Company",
    order: 21,
    inputType: "Manual",
    dependencies: [
      "problem",
      "audience",
      "unique-value-proposition",
      "alternatives",
      "trends",
      "why",
      "advantages",
      "product",
      "channels",
      "tech",
      "brand",
      "messaging",
      "assets",
      "sales-motion",
    ],
    status: "unproven",
    values: [
      "Year 1: $500K ARR, Year 2: $2M ARR, Year 3: $8M ARR from subscription revenue",
      "Year 1: $300K ARR, Year 2: $1.5M ARR, Year 3: $6M ARR from freemium conversions",
      "Year 1: $200K ARR, Year 2: $1M ARR, Year 3: $4M ARR from subscriptions + transaction fees",
    ],
    actions: 0,
    ideas: 0,
    results: 0,
  },
  {
    id: "cost",
    title: "Cost",
    description:
      "The various expenditures—fixed and variable—required to develop, operate, and scale your business effectively, typically estimated from your business model, audience, alternatives, packaging and channels.",
    question: "What are {PROJECT NAME}'s typical costs?",
    guidance:
      "Split fixed/variable; highlight runway vs milestones; suggest lean cuts if needed.",
    category: "Company",
    order: 22,
    inputType: "Manual",
    dependencies: [
      "problem",
      "audience",
      "unique-value-proposition",
      "alternatives",
      "trends",
      "why",
      "advantages",
      "product",
      "channels",
      "tech",
      "brand",
      "messaging",
      "assets",
      "sales-motion",
      "metrics",
    ],
    status: "unproven",
    values: [
      "Fixed: $50K/mo (team, infrastructure), Variable: $20K/mo (customer acquisition, hosting)",
      "Fixed: $40K/mo (development, operations), Variable: $15K/mo (marketing, support)",
      "Fixed: $30K/mo (team, platform), Variable: $10K/mo (partnerships, verification)",
    ],
    actions: 0,
    ideas: 0,
    results: 0,
  },
  {
    id: "team",
    title: "Team",
    description:
      "The group of founders or individuals whose skills, experience, strengths, weaknesses & characters collectively drive your business.",
    question: "Who is working on {PROJECT NAME}?",
    guidance:
      "Assess skill gaps, equity fairness; define hiring roadmap; weave narrative to mission.",
    category: "Company",
    order: 23,
    inputType: "Suggest",
    dependencies: [
      "problem",
      "audience",
      "unique-value-proposition",
      "alternatives",
      "trends",
      "why",
      "advantages",
      "product",
      "channels",
      "tech",
      "brand",
      "messaging",
      "assets",
      "sales-motion",
      "metrics",
    ],
    status: "unproven",
    values: [
      "CEO (retail ops), CTO (ML/AI), Head of Sales (B2B), 2 engineers, 1 designer",
      "CEO (product), CTO (distributed systems), Head of Growth (PLG), 3 engineers, 1 designer",
      "CEO (sustainability), CTO (marketplace), Head of Partnerships, 2 engineers, 1 content creator",
    ],
    actions: 0,
    ideas: 0,
    results: 0,
  },
];
