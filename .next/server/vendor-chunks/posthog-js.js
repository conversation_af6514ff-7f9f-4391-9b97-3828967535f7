"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/posthog-js";
exports.ids = ["vendor-chunks/posthog-js"];
exports.modules = {

/***/ "(ssr)/./node_modules/posthog-js/dist/module.js":
/*!************************************************!*\
  !*** ./node_modules/posthog-js/dist/module.js ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   COPY_AUTOCAPTURE_EVENT: () => (/* binding */ f),\n/* harmony export */   Compression: () => (/* binding */ _),\n/* harmony export */   PostHog: () => (/* binding */ ca),\n/* harmony export */   SurveyEventName: () => (/* binding */ ko),\n/* harmony export */   SurveyEventProperties: () => (/* binding */ Eo),\n/* harmony export */   SurveyPosition: () => (/* binding */ yo),\n/* harmony export */   SurveyQuestionBranchingType: () => (/* binding */ $o),\n/* harmony export */   SurveyQuestionType: () => (/* binding */ So),\n/* harmony export */   SurveySchedule: () => (/* binding */ xo),\n/* harmony export */   SurveyType: () => (/* binding */ wo),\n/* harmony export */   SurveyWidgetType: () => (/* binding */ bo),\n/* harmony export */   \"default\": () => (/* binding */ pa),\n/* harmony export */   knownUnsafeEditableEvent: () => (/* binding */ p),\n/* harmony export */   posthog: () => (/* binding */ pa),\n/* harmony export */   severityLevels: () => (/* binding */ g)\n/* harmony export */ });\nvar t=\"undefined\"!=typeof window?window:void 0,i=\"undefined\"!=typeof globalThis?globalThis:t,e=Array.prototype,r=e.forEach,s=e.indexOf,n=null==i?void 0:i.navigator,o=null==i?void 0:i.document,a=null==i?void 0:i.location,l=null==i?void 0:i.fetch,u=null!=i&&i.XMLHttpRequest&&\"withCredentials\"in new i.XMLHttpRequest?i.XMLHttpRequest:void 0,h=null==i?void 0:i.AbortController,d=null==n?void 0:n.userAgent,v=null!=t?t:{},c={DEBUG:!1,LIB_VERSION:\"1.258.6\"},f=\"$copy_autocapture\",p=[\"$snapshot\",\"$pageview\",\"$pageleave\",\"$set\",\"survey dismissed\",\"survey sent\",\"survey shown\",\"$identify\",\"$groupidentify\",\"$create_alias\",\"$$client_ingestion_warning\",\"$web_experiment_applied\",\"$feature_enrollment_update\",\"$feature_flag_called\"],_=function(t){return t.GZipJS=\"gzip-js\",t.Base64=\"base64\",t}({}),g=[\"fatal\",\"error\",\"warning\",\"log\",\"info\",\"debug\"];function m(t,i){return-1!==t.indexOf(i)}var b=function(t){return t.trim()},y=function(t){return t.replace(/^\\$/,\"\")};var w=Array.isArray,S=Object.prototype,$=S.hasOwnProperty,x=S.toString,k=w||function(t){return\"[object Array]\"===x.call(t)},E=t=>\"function\"==typeof t,I=t=>t===Object(t)&&!k(t),P=t=>{if(I(t)){for(var i in t)if($.call(t,i))return!1;return!0}return!1},R=t=>void 0===t,T=t=>\"[object String]\"==x.call(t),M=t=>T(t)&&0===t.trim().length,C=t=>null===t,F=t=>R(t)||C(t),O=t=>\"[object Number]\"==x.call(t),A=t=>\"[object Boolean]\"===x.call(t),D=t=>t instanceof FormData,L=t=>m(p,t),j=i=>{var e={t:function(e){if(t&&(c.DEBUG||v.POSTHOG_DEBUG)&&!R(t.console)&&t.console){for(var r=(\"__rrweb_original__\"in t.console[e]?t.console[e].__rrweb_original__:t.console[e]),s=arguments.length,n=new Array(s>1?s-1:0),o=1;o<s;o++)n[o-1]=arguments[o];r(i,...n)}},info:function(){for(var t=arguments.length,i=new Array(t),r=0;r<t;r++)i[r]=arguments[r];e.t(\"log\",...i)},warn:function(){for(var t=arguments.length,i=new Array(t),r=0;r<t;r++)i[r]=arguments[r];e.t(\"warn\",...i)},error:function(){for(var t=arguments.length,i=new Array(t),r=0;r<t;r++)i[r]=arguments[r];e.t(\"error\",...i)},critical:function(){for(var t=arguments.length,e=new Array(t),r=0;r<t;r++)e[r]=arguments[r];console.error(i,...e)},uninitializedWarning:t=>{e.error(\"You must initialize PostHog before calling \"+t)},createLogger:t=>j(i+\" \"+t)};return e},N=j(\"[PostHog.js]\"),z=N.createLogger,U=z(\"[ExternalScriptsLoader]\"),q=(t,i,e)=>{if(t.config.disable_external_dependency_loading)return U.warn(i+\" was requested but loading of external scripts is disabled.\"),e(\"Loading of external scripts is disabled\");var r=null==o?void 0:o.querySelectorAll(\"script\");if(r)for(var s=0;s<r.length;s++)if(r[s].src===i)return e();var n=()=>{if(!o)return e(\"document not found\");var r=o.createElement(\"script\");if(r.type=\"text/javascript\",r.crossOrigin=\"anonymous\",r.src=i,r.onload=t=>e(void 0,t),r.onerror=t=>e(t),t.config.prepare_external_dependency_script&&(r=t.config.prepare_external_dependency_script(r)),!r)return e(\"prepare_external_dependency_script returned null\");var s,n=o.querySelectorAll(\"body > script\");n.length>0?null==(s=n[0].parentNode)||s.insertBefore(r,n[0]):o.body.appendChild(r)};null!=o&&o.body?n():null==o||o.addEventListener(\"DOMContentLoaded\",n)};function B(){return B=Object.assign?Object.assign.bind():function(t){for(var i=1;i<arguments.length;i++){var e=arguments[i];for(var r in e)({}).hasOwnProperty.call(e,r)&&(t[r]=e[r])}return t},B.apply(null,arguments)}function H(t,i){if(null==t)return{};var e={};for(var r in t)if({}.hasOwnProperty.call(t,r)){if(-1!==i.indexOf(r))continue;e[r]=t[r]}return e}v.__PosthogExtensions__=v.__PosthogExtensions__||{},v.__PosthogExtensions__.loadExternalDependency=(t,i,e)=>{var r=\"/static/\"+i+\".js?v=\"+t.version;if(\"remote-config\"===i&&(r=\"/array/\"+t.config.token+\"/config.js\"),\"toolbar\"===i){var s=3e5;r=r+\"&t=\"+Math.floor(Date.now()/s)*s}var n=t.requestRouter.endpointFor(\"assets\",r);q(t,n,e)},v.__PosthogExtensions__.loadSiteApp=(t,i,e)=>{var r=t.requestRouter.endpointFor(\"api\",i);q(t,r,e)};var W={};function G(t,i,e){if(k(t))if(r&&t.forEach===r)t.forEach(i,e);else if(\"length\"in t&&t.length===+t.length)for(var s=0,n=t.length;s<n;s++)if(s in t&&i.call(e,t[s],s)===W)return}function J(t,i,e){if(!F(t)){if(k(t))return G(t,i,e);if(D(t)){for(var r of t.entries())if(i.call(e,r[1],r[0])===W)return}else for(var s in t)if($.call(t,s)&&i.call(e,t[s],s)===W)return}}var V=function(t){for(var i=arguments.length,e=new Array(i>1?i-1:0),r=1;r<i;r++)e[r-1]=arguments[r];return G(e,(function(i){for(var e in i)void 0!==i[e]&&(t[e]=i[e])})),t},K=function(t){for(var i=arguments.length,e=new Array(i>1?i-1:0),r=1;r<i;r++)e[r-1]=arguments[r];return G(e,(function(i){G(i,(function(i){t.push(i)}))})),t};function Y(t){for(var i=Object.keys(t),e=i.length,r=new Array(e);e--;)r[e]=[i[e],t[i[e]]];return r}var X=function(t){try{return t()}catch(t){return}},Q=function(t){return function(){try{for(var i=arguments.length,e=new Array(i),r=0;r<i;r++)e[r]=arguments[r];return t.apply(this,e)}catch(t){N.critical(\"Implementation error. Please turn on debug mode and open a ticket on https://app.posthog.com/home#panel=support%3Asupport%3A.\"),N.critical(t)}}},Z=function(t){var i={};return J(t,(function(t,e){(T(t)&&t.length>0||O(t))&&(i[e]=t)})),i};function tt(t,i){return e=t,r=t=>T(t)&&!C(i)?t.slice(0,i):t,s=new Set,function t(i,e){return i!==Object(i)?r?r(i,e):i:s.has(i)?void 0:(s.add(i),k(i)?(n=[],G(i,(i=>{n.push(t(i))}))):(n={},J(i,((i,e)=>{s.has(i)||(n[e]=t(i,e))}))),n);var n}(e);var e,r,s}var it=[\"herokuapp.com\",\"vercel.app\",\"netlify.app\"];function et(t){var i=null==t?void 0:t.hostname;if(!T(i))return!1;var e=i.split(\".\").slice(-2).join(\".\");for(var r of it)if(e===r)return!1;return!0}function rt(t,i){for(var e=0;e<t.length;e++)if(i(t[e]))return t[e]}function st(t,i,e,r){var{capture:s=!1,passive:n=!0}=null!=r?r:{};null==t||t.addEventListener(i,e,{capture:s,passive:n})}var nt=\"$people_distinct_id\",ot=\"__alias\",at=\"__timers\",lt=\"$autocapture_disabled_server_side\",ut=\"$heatmaps_enabled_server_side\",ht=\"$exception_capture_enabled_server_side\",dt=\"$error_tracking_suppression_rules\",vt=\"$error_tracking_capture_extension_exceptions\",ct=\"$web_vitals_enabled_server_side\",ft=\"$dead_clicks_enabled_server_side\",pt=\"$web_vitals_allowed_metrics\",_t=\"$session_recording_enabled_server_side\",gt=\"$console_log_recording_enabled_server_side\",mt=\"$session_recording_network_payload_capture\",bt=\"$session_recording_masking\",yt=\"$session_recording_canvas_recording\",wt=\"$replay_sample_rate\",St=\"$replay_minimum_duration\",$t=\"$replay_script_config\",xt=\"$sesid\",kt=\"$session_is_sampled\",Et=\"$session_recording_url_trigger_activated_session\",It=\"$session_recording_event_trigger_activated_session\",Pt=\"$enabled_feature_flags\",Rt=\"$early_access_features\",Tt=\"$feature_flag_details\",Mt=\"$stored_person_properties\",Ct=\"$stored_group_properties\",Ft=\"$surveys\",Ot=\"$surveys_activated\",At=\"$flag_call_reported\",Dt=\"$user_state\",Lt=\"$client_session_props\",jt=\"$capture_rate_limit\",Nt=\"$initial_campaign_params\",zt=\"$initial_referrer_info\",Ut=\"$initial_person_info\",qt=\"$epp\",Bt=\"__POSTHOG_TOOLBAR__\",Ht=\"$posthog_cookieless\",Wt=[nt,ot,\"__cmpns\",at,_t,ut,xt,Pt,dt,Dt,Rt,Tt,Ct,Mt,Ft,At,Lt,jt,Nt,zt,qt,Ut];function Gt(t){return t instanceof Element&&(t.id===Bt||!(null==t.closest||!t.closest(\".toolbar-global-fade-container\")))}function Jt(t){return!!t&&1===t.nodeType}function Vt(t,i){return!!t&&!!t.tagName&&t.tagName.toLowerCase()===i.toLowerCase()}function Kt(t){return!!t&&3===t.nodeType}function Yt(t){return!!t&&11===t.nodeType}function Xt(t){return t?b(t).split(/\\s+/):[]}function Qt(i){var e=null==t?void 0:t.location.href;return!!(e&&i&&i.some((t=>e.match(t))))}function Zt(t){var i=\"\";switch(typeof t.className){case\"string\":i=t.className;break;case\"object\":i=(t.className&&\"baseVal\"in t.className?t.className.baseVal:null)||t.getAttribute(\"class\")||\"\";break;default:i=\"\"}return Xt(i)}function ti(t){return F(t)?null:b(t).split(/(\\s+)/).filter((t=>fi(t))).join(\"\").replace(/[\\r\\n]/g,\" \").replace(/[ ]+/g,\" \").substring(0,255)}function ii(t){var i=\"\";return oi(t)&&!ai(t)&&t.childNodes&&t.childNodes.length&&J(t.childNodes,(function(t){var e;Kt(t)&&t.textContent&&(i+=null!==(e=ti(t.textContent))&&void 0!==e?e:\"\")})),b(i)}function ei(t){return R(t.target)?t.srcElement||null:null!=(i=t.target)&&i.shadowRoot?t.composedPath()[0]||null:t.target||null;var i}var ri=[\"a\",\"button\",\"form\",\"input\",\"select\",\"textarea\",\"label\"];function si(t){var i=t.parentNode;return!(!i||!Jt(i))&&i}function ni(i,e,r,s,n){var o,a,l;if(void 0===r&&(r=void 0),!t||!i||Vt(i,\"html\")||!Jt(i))return!1;if(null!=(o=r)&&o.url_allowlist&&!Qt(r.url_allowlist))return!1;if(null!=(a=r)&&a.url_ignorelist&&Qt(r.url_ignorelist))return!1;if(null!=(l=r)&&l.dom_event_allowlist){var u=r.dom_event_allowlist;if(u&&!u.some((t=>e.type===t)))return!1}for(var h=!1,d=[i],v=!0,c=i;c.parentNode&&!Vt(c,\"body\");)if(Yt(c.parentNode))d.push(c.parentNode.host),c=c.parentNode.host;else{if(!(v=si(c)))break;if(s||ri.indexOf(v.tagName.toLowerCase())>-1)h=!0;else{var f=t.getComputedStyle(v);f&&\"pointer\"===f.getPropertyValue(\"cursor\")&&(h=!0)}d.push(v),c=v}if(!function(t,i){var e=null==i?void 0:i.element_allowlist;if(R(e))return!0;var r,s=function(t){if(e.some((i=>t.tagName.toLowerCase()===i)))return{v:!0}};for(var n of t)if(r=s(n))return r.v;return!1}(d,r))return!1;if(!function(t,i){var e=null==i?void 0:i.css_selector_allowlist;if(R(e))return!0;var r,s=function(t){if(e.some((i=>t.matches(i))))return{v:!0}};for(var n of t)if(r=s(n))return r.v;return!1}(d,r))return!1;var p=t.getComputedStyle(i);if(p&&\"pointer\"===p.getPropertyValue(\"cursor\")&&\"click\"===e.type)return!0;var _=i.tagName.toLowerCase();switch(_){case\"html\":return!1;case\"form\":return(n||[\"submit\"]).indexOf(e.type)>=0;case\"input\":case\"select\":case\"textarea\":return(n||[\"change\",\"click\"]).indexOf(e.type)>=0;default:return h?(n||[\"click\"]).indexOf(e.type)>=0:(n||[\"click\"]).indexOf(e.type)>=0&&(ri.indexOf(_)>-1||\"true\"===i.getAttribute(\"contenteditable\"))}}function oi(t){for(var i=t;i.parentNode&&!Vt(i,\"body\");i=i.parentNode){var e=Zt(i);if(m(e,\"ph-sensitive\")||m(e,\"ph-no-capture\"))return!1}if(m(Zt(t),\"ph-include\"))return!0;var r=t.type||\"\";if(T(r))switch(r.toLowerCase()){case\"hidden\":case\"password\":return!1}var s=t.name||t.id||\"\";if(T(s)){if(/^cc|cardnum|ccnum|creditcard|csc|cvc|cvv|exp|pass|pwd|routing|seccode|securitycode|securitynum|socialsec|socsec|ssn/i.test(s.replace(/[^a-zA-Z0-9]/g,\"\")))return!1}return!0}function ai(t){return!!(Vt(t,\"input\")&&![\"button\",\"checkbox\",\"submit\",\"reset\"].includes(t.type)||Vt(t,\"select\")||Vt(t,\"textarea\")||\"true\"===t.getAttribute(\"contenteditable\"))}var li=\"(4[0-9]{12}(?:[0-9]{3})?)|(5[1-5][0-9]{14})|(6(?:011|5[0-9]{2})[0-9]{12})|(3[47][0-9]{13})|(3(?:0[0-5]|[68][0-9])[0-9]{11})|((?:2131|1800|35[0-9]{3})[0-9]{11})\",ui=new RegExp(\"^(?:\"+li+\")$\"),hi=new RegExp(li),di=\"\\\\d{3}-?\\\\d{2}-?\\\\d{4}\",vi=new RegExp(\"^(\"+di+\")$\"),ci=new RegExp(\"(\"+di+\")\");function fi(t,i){if(void 0===i&&(i=!0),F(t))return!1;if(T(t)){if(t=b(t),(i?ui:hi).test((t||\"\").replace(/[- ]/g,\"\")))return!1;if((i?vi:ci).test(t))return!1}return!0}function pi(t){var i=ii(t);return fi(i=(i+\" \"+_i(t)).trim())?i:\"\"}function _i(t){var i=\"\";return t&&t.childNodes&&t.childNodes.length&&J(t.childNodes,(function(t){var e;if(t&&\"span\"===(null==(e=t.tagName)?void 0:e.toLowerCase()))try{var r=ii(t);i=(i+\" \"+r).trim(),t.childNodes&&t.childNodes.length&&(i=(i+\" \"+_i(t)).trim())}catch(t){N.error(\"[AutoCapture]\",t)}})),i}function gi(t){return function(t){var i=t.map((t=>{var i,e,r=\"\";if(t.tag_name&&(r+=t.tag_name),t.attr_class)for(var s of(t.attr_class.sort(),t.attr_class))r+=\".\"+s.replace(/\"/g,\"\");var n=B({},t.text?{text:t.text}:{},{\"nth-child\":null!==(i=t.nth_child)&&void 0!==i?i:0,\"nth-of-type\":null!==(e=t.nth_of_type)&&void 0!==e?e:0},t.href?{href:t.href}:{},t.attr_id?{attr_id:t.attr_id}:{},t.attributes),o={};return Y(n).sort(((t,i)=>{var[e]=t,[r]=i;return e.localeCompare(r)})).forEach((t=>{var[i,e]=t;return o[mi(i.toString())]=mi(e.toString())})),r+=\":\",r+=Y(o).map((t=>{var[i,e]=t;return i+'=\"'+e+'\"'})).join(\"\")}));return i.join(\";\")}(function(t){return t.map((t=>{var i,e,r={text:null==(i=t.$el_text)?void 0:i.slice(0,400),tag_name:t.tag_name,href:null==(e=t.attr__href)?void 0:e.slice(0,2048),attr_class:bi(t),attr_id:t.attr__id,nth_child:t.nth_child,nth_of_type:t.nth_of_type,attributes:{}};return Y(t).filter((t=>{var[i]=t;return 0===i.indexOf(\"attr__\")})).forEach((t=>{var[i,e]=t;return r.attributes[i]=e})),r}))}(t))}function mi(t){return t.replace(/\"|\\\\\"/g,'\\\\\"')}function bi(t){var i=t.attr__class;return i?k(i)?i:Xt(i):void 0}class yi{constructor(){this.clicks=[]}isRageClick(t,i,e){var r=this.clicks[this.clicks.length-1];if(r&&Math.abs(t-r.x)+Math.abs(i-r.y)<30&&e-r.timestamp<1e3){if(this.clicks.push({x:t,y:i,timestamp:e}),3===this.clicks.length)return!0}else this.clicks=[{x:t,y:i,timestamp:e}];return!1}}var wi=[\"localhost\",\"127.0.0.1\"],Si=t=>{var i=null==o?void 0:o.createElement(\"a\");return R(i)?null:(i.href=t,i)},$i=function(t,i){var e,r;void 0===i&&(i=\"&\");var s=[];return J(t,(function(t,i){R(t)||R(i)||\"undefined\"===i||(e=encodeURIComponent((t=>t instanceof File)(t)?t.name:t.toString()),r=encodeURIComponent(i),s[s.length]=r+\"=\"+e)})),s.join(i)},xi=function(t,i){for(var e,r=((t.split(\"#\")[0]||\"\").split(/\\?(.*)/)[1]||\"\").replace(/^\\?+/g,\"\").split(\"&\"),s=0;s<r.length;s++){var n=r[s].split(\"=\");if(n[0]===i){e=n;break}}if(!k(e)||e.length<2)return\"\";var o=e[1];try{o=decodeURIComponent(o)}catch(t){N.error(\"Skipping decoding for malformed query param: \"+o)}return o.replace(/\\+/g,\" \")},ki=function(t,i,e){if(!t||!i||!i.length)return t;for(var r=t.split(\"#\"),s=r[0]||\"\",n=r[1],o=s.split(\"?\"),a=o[1],l=o[0],u=(a||\"\").split(\"&\"),h=[],d=0;d<u.length;d++){var v=u[d].split(\"=\");k(v)&&(i.includes(v[0])?h.push(v[0]+\"=\"+e):h.push(u[d]))}var c=l;return null!=a&&(c+=\"?\"+h.join(\"&\")),null!=n&&(c+=\"#\"+n),c},Ei=function(t,i){var e=t.match(new RegExp(i+\"=([^&]*)\"));return e?e[1]:null},Ii=z(\"[AutoCapture]\");function Pi(t,i){return i.length>t?i.slice(0,t)+\"...\":i}function Ri(t){if(t.previousElementSibling)return t.previousElementSibling;var i=t;do{i=i.previousSibling}while(i&&!Jt(i));return i}function Ti(t,i,e,r){var s=t.tagName.toLowerCase(),n={tag_name:s};ri.indexOf(s)>-1&&!e&&(\"a\"===s.toLowerCase()||\"button\"===s.toLowerCase()?n.$el_text=Pi(1024,pi(t)):n.$el_text=Pi(1024,ii(t)));var o=Zt(t);o.length>0&&(n.classes=o.filter((function(t){return\"\"!==t}))),J(t.attributes,(function(e){var s;if((!ai(t)||-1!==[\"name\",\"id\",\"class\",\"aria-label\"].indexOf(e.name))&&((null==r||!r.includes(e.name))&&!i&&fi(e.value)&&(s=e.name,!T(s)||\"_ngcontent\"!==s.substring(0,10)&&\"_nghost\"!==s.substring(0,7)))){var o=e.value;\"class\"===e.name&&(o=Xt(o).join(\" \")),n[\"attr__\"+e.name]=Pi(1024,o)}}));for(var a=1,l=1,u=t;u=Ri(u);)a++,u.tagName===t.tagName&&l++;return n.nth_child=a,n.nth_of_type=l,n}function Mi(i,e){for(var r,s,{e:n,maskAllElementAttributes:o,maskAllText:a,elementAttributeIgnoreList:l,elementsChainAsString:u}=e,h=[i],d=i;d.parentNode&&!Vt(d,\"body\");)Yt(d.parentNode)?(h.push(d.parentNode.host),d=d.parentNode.host):(h.push(d.parentNode),d=d.parentNode);var v,c=[],f={},p=!1,_=!1;if(J(h,(t=>{var i=oi(t);\"a\"===t.tagName.toLowerCase()&&(p=t.getAttribute(\"href\"),p=i&&p&&fi(p)&&p),m(Zt(t),\"ph-no-capture\")&&(_=!0),c.push(Ti(t,o,a,l));var e=function(t){if(!oi(t))return{};var i={};return J(t.attributes,(function(t){if(t.name&&0===t.name.indexOf(\"data-ph-capture-attribute\")){var e=t.name.replace(\"data-ph-capture-attribute-\",\"\"),r=t.value;e&&r&&fi(r)&&(i[e]=r)}})),i}(t);V(f,e)})),_)return{props:{},explicitNoCapture:_};if(a||(\"a\"===i.tagName.toLowerCase()||\"button\"===i.tagName.toLowerCase()?c[0].$el_text=pi(i):c[0].$el_text=ii(i)),p){var g,b;c[0].attr__href=p;var y=null==(g=Si(p))?void 0:g.host,w=null==t||null==(b=t.location)?void 0:b.host;y&&w&&y!==w&&(v=p)}return{props:V({$event_type:n.type,$ce_version:1},u?{}:{$elements:c},{$elements_chain:gi(c)},null!=(r=c[0])&&r.$el_text?{$el_text:null==(s=c[0])?void 0:s.$el_text}:{},v&&\"click\"===n.type?{$external_click_url:v}:{},f)}}class Ci{constructor(t){this.i=!1,this.o=null,this.rageclicks=new yi,this.h=!1,this.instance=t,this.m=null}get S(){var t,i,e=I(this.instance.config.autocapture)?this.instance.config.autocapture:{};return e.url_allowlist=null==(t=e.url_allowlist)?void 0:t.map((t=>new RegExp(t))),e.url_ignorelist=null==(i=e.url_ignorelist)?void 0:i.map((t=>new RegExp(t))),e}$(){if(this.isBrowserSupported()){if(t&&o){var i=i=>{i=i||(null==t?void 0:t.event);try{this.k(i)}catch(t){Ii.error(\"Failed to capture event\",t)}};if(st(o,\"submit\",i,{capture:!0}),st(o,\"change\",i,{capture:!0}),st(o,\"click\",i,{capture:!0}),this.S.capture_copied_text){var e=i=>{i=i||(null==t?void 0:t.event),this.k(i,f)};st(o,\"copy\",e,{capture:!0}),st(o,\"cut\",e,{capture:!0})}}}else Ii.info(\"Disabling Automatic Event Collection because this browser is not supported\")}startIfEnabled(){this.isEnabled&&!this.i&&(this.$(),this.i=!0)}onRemoteConfig(t){t.elementsChainAsString&&(this.h=t.elementsChainAsString),this.instance.persistence&&this.instance.persistence.register({[lt]:!!t.autocapture_opt_out}),this.o=!!t.autocapture_opt_out,this.startIfEnabled()}setElementSelectors(t){this.m=t}getElementSelectors(t){var i,e=[];return null==(i=this.m)||i.forEach((i=>{var r=null==o?void 0:o.querySelectorAll(i);null==r||r.forEach((r=>{t===r&&e.push(i)}))})),e}get isEnabled(){var t,i,e=null==(t=this.instance.persistence)?void 0:t.props[lt],r=this.o;if(C(r)&&!A(e)&&!this.instance.I())return!1;var s=null!==(i=this.o)&&void 0!==i?i:!!e;return!!this.instance.config.autocapture&&!s}k(i,e){if(void 0===e&&(e=\"$autocapture\"),this.isEnabled){var r,s=ei(i);if(Kt(s)&&(s=s.parentNode||null),\"$autocapture\"===e&&\"click\"===i.type&&i instanceof MouseEvent)this.instance.config.rageclick&&null!=(r=this.rageclicks)&&r.isRageClick(i.clientX,i.clientY,(new Date).getTime())&&this.k(i,\"$rageclick\");var n=e===f;if(s&&ni(s,i,this.S,n,n?[\"copy\",\"cut\"]:void 0)){var{props:o,explicitNoCapture:a}=Mi(s,{e:i,maskAllElementAttributes:this.instance.config.mask_all_element_attributes,maskAllText:this.instance.config.mask_all_text,elementAttributeIgnoreList:this.S.element_attribute_ignorelist,elementsChainAsString:this.h});if(a)return!1;var l=this.getElementSelectors(s);if(l&&l.length>0&&(o.$element_selectors=l),e===f){var u,h=ti(null==t||null==(u=t.getSelection())?void 0:u.toString()),d=i.type||\"clipboard\";if(!h)return!1;o.$selected_content=h,o.$copy_type=d}return this.instance.capture(e,o),!0}}}isBrowserSupported(){return E(null==o?void 0:o.querySelectorAll)}}Math.trunc||(Math.trunc=function(t){return t<0?Math.ceil(t):Math.floor(t)}),Number.isInteger||(Number.isInteger=function(t){return O(t)&&isFinite(t)&&Math.floor(t)===t});var Fi=\"0123456789abcdef\";class Oi{constructor(t){if(this.bytes=t,16!==t.length)throw new TypeError(\"not 128-bit length\")}static fromFieldsV7(t,i,e,r){if(!Number.isInteger(t)||!Number.isInteger(i)||!Number.isInteger(e)||!Number.isInteger(r)||t<0||i<0||e<0||r<0||t>0xffffffffffff||i>4095||e>1073741823||r>4294967295)throw new RangeError(\"invalid field value\");var s=new Uint8Array(16);return s[0]=t/Math.pow(2,40),s[1]=t/Math.pow(2,32),s[2]=t/Math.pow(2,24),s[3]=t/Math.pow(2,16),s[4]=t/Math.pow(2,8),s[5]=t,s[6]=112|i>>>8,s[7]=i,s[8]=128|e>>>24,s[9]=e>>>16,s[10]=e>>>8,s[11]=e,s[12]=r>>>24,s[13]=r>>>16,s[14]=r>>>8,s[15]=r,new Oi(s)}toString(){for(var t=\"\",i=0;i<this.bytes.length;i++)t=t+Fi.charAt(this.bytes[i]>>>4)+Fi.charAt(15&this.bytes[i]),3!==i&&5!==i&&7!==i&&9!==i||(t+=\"-\");if(36!==t.length)throw new Error(\"Invalid UUIDv7 was generated\");return t}clone(){return new Oi(this.bytes.slice(0))}equals(t){return 0===this.compareTo(t)}compareTo(t){for(var i=0;i<16;i++){var e=this.bytes[i]-t.bytes[i];if(0!==e)return Math.sign(e)}return 0}}class Ai{constructor(){this.P=0,this.R=0,this.T=new ji}generate(){var t=this.generateOrAbort();if(R(t)){this.P=0;var i=this.generateOrAbort();if(R(i))throw new Error(\"Could not generate UUID after timestamp reset\");return i}return t}generateOrAbort(){var t=Date.now();if(t>this.P)this.P=t,this.M();else{if(!(t+1e4>this.P))return;this.R++,this.R>4398046511103&&(this.P++,this.M())}return Oi.fromFieldsV7(this.P,Math.trunc(this.R/Math.pow(2,30)),this.R&Math.pow(2,30)-1,this.T.nextUint32())}M(){this.R=1024*this.T.nextUint32()+(1023&this.T.nextUint32())}}var Di,Li=t=>{if(\"undefined\"!=typeof UUIDV7_DENY_WEAK_RNG&&UUIDV7_DENY_WEAK_RNG)throw new Error(\"no cryptographically strong RNG available\");for(var i=0;i<t.length;i++)t[i]=65536*Math.trunc(65536*Math.random())+Math.trunc(65536*Math.random());return t};t&&!R(t.crypto)&&crypto.getRandomValues&&(Li=t=>crypto.getRandomValues(t));class ji{constructor(){this.C=new Uint32Array(8),this.F=1/0}nextUint32(){return this.F>=this.C.length&&(Li(this.C),this.F=0),this.C[this.F++]}}var Ni=()=>zi().toString(),zi=()=>(Di||(Di=new Ai)).generate(),Ui=\"\";var qi=/[a-z0-9][a-z0-9-]+\\.[a-z]{2,}$/i;function Bi(t,i){if(i){var e=function(t,i){if(void 0===i&&(i=o),Ui)return Ui;if(!i)return\"\";if([\"localhost\",\"127.0.0.1\"].includes(t))return\"\";for(var e=t.split(\".\"),r=Math.min(e.length,8),s=\"dmn_chk_\"+Ni();!Ui&&r--;){var n=e.slice(r).join(\".\"),a=s+\"=1;domain=.\"+n+\";path=/\";i.cookie=a+\";max-age=3\",i.cookie.includes(s)&&(i.cookie=a+\";max-age=0\",Ui=n)}return Ui}(t);if(!e){var r=(t=>{var i=t.match(qi);return i?i[0]:\"\"})(t);r!==e&&N.info(\"Warning: cookie subdomain discovery mismatch\",r,e),e=r}return e?\"; domain=.\"+e:\"\"}return\"\"}var Hi={O:()=>!!o,A:function(t){N.error(\"cookieStore error: \"+t)},D:function(t){if(o){try{for(var i=t+\"=\",e=o.cookie.split(\";\").filter((t=>t.length)),r=0;r<e.length;r++){for(var s=e[r];\" \"==s.charAt(0);)s=s.substring(1,s.length);if(0===s.indexOf(i))return decodeURIComponent(s.substring(i.length,s.length))}}catch(t){}return null}},L:function(t){var i;try{i=JSON.parse(Hi.D(t))||{}}catch(t){}return i},j:function(t,i,e,r,s){if(o)try{var n=\"\",a=\"\",l=Bi(o.location.hostname,r);if(e){var u=new Date;u.setTime(u.getTime()+24*e*60*60*1e3),n=\"; expires=\"+u.toUTCString()}s&&(a=\"; secure\");var h=t+\"=\"+encodeURIComponent(JSON.stringify(i))+n+\"; SameSite=Lax; path=/\"+l+a;return h.length>3686.4&&N.warn(\"cookieStore warning: large cookie, len=\"+h.length),o.cookie=h,h}catch(t){return}},N:function(t,i){try{Hi.j(t,\"\",-1,i)}catch(t){return}}},Wi=null,Gi={O:function(){if(!C(Wi))return Wi;var i=!0;if(R(t))i=!1;else try{var e=\"__mplssupport__\";Gi.j(e,\"xyz\"),'\"xyz\"'!==Gi.D(e)&&(i=!1),Gi.N(e)}catch(t){i=!1}return i||N.error(\"localStorage unsupported; falling back to cookie store\"),Wi=i,i},A:function(t){N.error(\"localStorage error: \"+t)},D:function(i){try{return null==t?void 0:t.localStorage.getItem(i)}catch(t){Gi.A(t)}return null},L:function(t){try{return JSON.parse(Gi.D(t))||{}}catch(t){}return null},j:function(i,e){try{null==t||t.localStorage.setItem(i,JSON.stringify(e))}catch(t){Gi.A(t)}},N:function(i){try{null==t||t.localStorage.removeItem(i)}catch(t){Gi.A(t)}}},Ji=[\"distinct_id\",xt,kt,qt,Ut],Vi=B({},Gi,{L:function(t){try{var i={};try{i=Hi.L(t)||{}}catch(t){}var e=V(i,JSON.parse(Gi.D(t)||\"{}\"));return Gi.j(t,e),e}catch(t){}return null},j:function(t,i,e,r,s,n){try{Gi.j(t,i,void 0,void 0,n);var o={};Ji.forEach((t=>{i[t]&&(o[t]=i[t])})),Object.keys(o).length&&Hi.j(t,o,e,r,s,n)}catch(t){Gi.A(t)}},N:function(i,e){try{null==t||t.localStorage.removeItem(i),Hi.N(i,e)}catch(t){Gi.A(t)}}}),Ki={},Yi={O:function(){return!0},A:function(t){N.error(\"memoryStorage error: \"+t)},D:function(t){return Ki[t]||null},L:function(t){return Ki[t]||null},j:function(t,i){Ki[t]=i},N:function(t){delete Ki[t]}},Xi=null,Qi={O:function(){if(!C(Xi))return Xi;if(Xi=!0,R(t))Xi=!1;else try{var i=\"__support__\";Qi.j(i,\"xyz\"),'\"xyz\"'!==Qi.D(i)&&(Xi=!1),Qi.N(i)}catch(t){Xi=!1}return Xi},A:function(t){N.error(\"sessionStorage error: \",t)},D:function(i){try{return null==t?void 0:t.sessionStorage.getItem(i)}catch(t){Qi.A(t)}return null},L:function(t){try{return JSON.parse(Qi.D(t))||null}catch(t){}return null},j:function(i,e){try{null==t||t.sessionStorage.setItem(i,JSON.stringify(e))}catch(t){Qi.A(t)}},N:function(i){try{null==t||t.sessionStorage.removeItem(i)}catch(t){Qi.A(t)}}},Zi=function(t){return t[t.PENDING=-1]=\"PENDING\",t[t.DENIED=0]=\"DENIED\",t[t.GRANTED=1]=\"GRANTED\",t}({});class te{constructor(t){this._instance=t}get S(){return this._instance.config}get consent(){return this.U()?Zi.DENIED:this.q}isOptedOut(){return this.consent===Zi.DENIED||this.consent===Zi.PENDING&&this.S.opt_out_capturing_by_default}isOptedIn(){return!this.isOptedOut()}optInOut(t){this.B.j(this.H,t?1:0,this.S.cookie_expiration,this.S.cross_subdomain_cookie,this.S.secure_cookie)}reset(){this.B.N(this.H,this.S.cross_subdomain_cookie)}get H(){var{token:t,opt_out_capturing_cookie_prefix:i}=this._instance.config;return(i||\"__ph_opt_in_out_\")+t}get q(){var t=this.B.D(this.H);return\"1\"===t?Zi.GRANTED:\"0\"===t?Zi.DENIED:Zi.PENDING}get B(){if(!this.W){var t=this.S.opt_out_capturing_persistence_type;this.W=\"localStorage\"===t?Gi:Hi;var i=\"localStorage\"===t?Hi:Gi;i.D(this.H)&&(this.W.D(this.H)||this.optInOut(\"1\"===i.D(this.H)),i.N(this.H,this.S.cross_subdomain_cookie))}return this.W}U(){return!!this.S.respect_dnt&&!!rt([null==n?void 0:n.doNotTrack,null==n?void 0:n.msDoNotTrack,v.doNotTrack],(t=>m([!0,1,\"1\",\"yes\"],t)))}}var ie=z(\"[Dead Clicks]\"),ee=()=>!0,re=t=>{var i,e=!(null==(i=t.instance.persistence)||!i.get_property(ft)),r=t.instance.config.capture_dead_clicks;return A(r)?r:e};class se{get lazyLoadedDeadClicksAutocapture(){return this.G}constructor(t,i,e){this.instance=t,this.isEnabled=i,this.onCapture=e,this.startIfEnabled()}onRemoteConfig(t){this.instance.persistence&&this.instance.persistence.register({[ft]:null==t?void 0:t.captureDeadClicks}),this.startIfEnabled()}startIfEnabled(){this.isEnabled(this)&&this.J((()=>{this.V()}))}J(t){var i,e;null!=(i=v.__PosthogExtensions__)&&i.initDeadClicksAutocapture&&t(),null==(e=v.__PosthogExtensions__)||null==e.loadExternalDependency||e.loadExternalDependency(this.instance,\"dead-clicks-autocapture\",(i=>{i?ie.error(\"failed to load script\",i):t()}))}V(){var t;if(o){if(!this.G&&null!=(t=v.__PosthogExtensions__)&&t.initDeadClicksAutocapture){var i=I(this.instance.config.capture_dead_clicks)?this.instance.config.capture_dead_clicks:{};i.__onCapture=this.onCapture,this.G=v.__PosthogExtensions__.initDeadClicksAutocapture(this.instance,i),this.G.start(o),ie.info(\"starting...\")}}else ie.error(\"`document` not found. Cannot start.\")}stop(){this.G&&(this.G.stop(),this.G=void 0,ie.info(\"stopping...\"))}}function ne(t,i,e,r,s){return i>e&&(N.warn(\"min cannot be greater than max.\"),i=e),O(t)?t>e?(r&&N.warn(r+\" cannot be  greater than max: \"+e+\". Using max value instead.\"),e):t<i?(r&&N.warn(r+\" cannot be less than min: \"+i+\". Using min value instead.\"),i):t:(r&&N.warn(r+\" must be a number. using max or fallback. max: \"+e+\", fallback: \"+s),ne(s||e,i,e,r))}class oe{constructor(t){this.K={},this.Y=()=>{Object.keys(this.K).forEach((t=>{var i=this.X(t)+this.Z;i>=this.tt?delete this.K[t]:this.it(t,i)}))},this.X=t=>this.K[String(t)],this.it=(t,i)=>{this.K[String(t)]=i},this.consumeRateLimit=t=>{var i,e=null!==(i=this.X(t))&&void 0!==i?i:this.tt;if(0===(e=Math.max(e-1,0)))return!0;this.it(t,e);var r,s=0===e;s&&(null==(r=this.et)||r.call(this,t));return s},this.rt=t,this.et=this.rt.et,this.tt=ne(this.rt.bucketSize,0,100,\"rate limiter bucket size\"),this.Z=ne(this.rt.refillRate,0,this.tt,\"rate limiter refill rate\"),this.st=ne(this.rt.refillInterval,0,864e5,\"rate limiter refill interval\"),setInterval((()=>{this.Y()}),this.st)}}var ae=z(\"[ExceptionAutocapture]\");class le{constructor(i){var e,r,s;this.nt=()=>{var i;if(t&&this.isEnabled&&null!=(i=v.__PosthogExtensions__)&&i.errorWrappingFunctions){var e=v.__PosthogExtensions__.errorWrappingFunctions.wrapOnError,r=v.__PosthogExtensions__.errorWrappingFunctions.wrapUnhandledRejection,s=v.__PosthogExtensions__.errorWrappingFunctions.wrapConsoleError;try{!this.ot&&this.S.capture_unhandled_errors&&(this.ot=e(this.captureException.bind(this))),!this.lt&&this.S.capture_unhandled_rejections&&(this.lt=r(this.captureException.bind(this))),!this.ut&&this.S.capture_console_errors&&(this.ut=s(this.captureException.bind(this)))}catch(t){ae.error(\"failed to start\",t),this.ht()}}},this._instance=i,this.dt=!(null==(e=this._instance.persistence)||!e.props[ht]),this.S=this.vt(),this.ct=new oe({refillRate:null!==(r=this._instance.config.error_tracking.__exceptionRateLimiterRefillRate)&&void 0!==r?r:1,bucketSize:null!==(s=this._instance.config.error_tracking.__exceptionRateLimiterBucketSize)&&void 0!==s?s:10,refillInterval:1e4}),this.startIfEnabled()}vt(){var t=this._instance.config.capture_exceptions,i={capture_unhandled_errors:!1,capture_unhandled_rejections:!1,capture_console_errors:!1};return I(t)?i=B({},i,t):(R(t)?this.dt:t)&&(i=B({},i,{capture_unhandled_errors:!0,capture_unhandled_rejections:!0})),i}get isEnabled(){return this.S.capture_console_errors||this.S.capture_unhandled_errors||this.S.capture_unhandled_rejections}startIfEnabled(){this.isEnabled&&(ae.info(\"enabled\"),this.J(this.nt))}J(t){var i,e;null!=(i=v.__PosthogExtensions__)&&i.errorWrappingFunctions&&t(),null==(e=v.__PosthogExtensions__)||null==e.loadExternalDependency||e.loadExternalDependency(this._instance,\"exception-autocapture\",(i=>{if(i)return ae.error(\"failed to load script\",i);t()}))}ht(){var t,i,e;null==(t=this.ot)||t.call(this),this.ot=void 0,null==(i=this.lt)||i.call(this),this.lt=void 0,null==(e=this.ut)||e.call(this),this.ut=void 0}onRemoteConfig(t){var i=t.autocaptureExceptions;this.dt=!!i||!1,this.S=this.vt(),this._instance.persistence&&this._instance.persistence.register({[ht]:this.dt}),this.startIfEnabled()}captureException(t){var i,e=this._instance.requestRouter.endpointFor(\"ui\");t.$exception_personURL=e+\"/project/\"+this._instance.config.token+\"/person/\"+this._instance.get_distinct_id();var r=null!==(i=t.$exception_list[0].type)&&void 0!==i?i:\"Exception\";this.ct.consumeRateLimit(r)?ae.info(\"Skipping exception capture because of client rate limiting.\",{exception:t.$exception_list[0].type}):this._instance.exceptions.sendExceptionEvent(t)}}function ue(t){return!R(Event)&&he(t,Event)}function he(t,i){try{return t instanceof i}catch(t){return!1}}function de(t){switch(Object.prototype.toString.call(t)){case\"[object Error]\":case\"[object Exception]\":case\"[object DOMException]\":case\"[object DOMError]\":return!0;default:return he(t,Error)}}function ve(t,i){return Object.prototype.toString.call(t)===\"[object \"+i+\"]\"}function ce(t){return ve(t,\"DOMError\")}var fe=/\\(error: (.*)\\)/,pe=50,_e=\"?\";function ge(t,i,e,r){var s={platform:\"web:javascript\",filename:t,function:\"<anonymous>\"===i?_e:i,in_app:!0};return R(e)||(s.lineno=e),R(r)||(s.colno=r),s}var me=/^\\s*at (\\S+?)(?::(\\d+))(?::(\\d+))\\s*$/i,be=/^\\s*at (?:(.+?\\)(?: \\[.+\\])?|.*?) ?\\((?:address at )?)?(?:async )?((?:<anonymous>|[-a-z]+:|.*bundle|\\/)?.*?)(?::(\\d+))?(?::(\\d+))?\\)?\\s*$/i,ye=/\\((\\S*)(?::(\\d+))(?::(\\d+))\\)/,we=/^\\s*(.*?)(?:\\((.*?)\\))?(?:^|@)?((?:[-a-z]+)?:\\/.*?|\\[native code\\]|[^@]*(?:bundle|\\d+\\.js)|\\/[\\w\\-. /=]+)(?::(\\d+))?(?::(\\d+))?\\s*$/i,Se=/(\\S+) line (\\d+)(?: > eval line \\d+)* > eval/i,$e=function(){for(var t=arguments.length,i=new Array(t),e=0;e<t;e++)i[e]=arguments[e];var r=i.sort(((t,i)=>t[0]-i[0])).map((t=>t[1]));return function(t,i){void 0===i&&(i=0);for(var e=[],s=t.split(\"\\n\"),n=i;n<s.length;n++){var o=s[n];if(!(o.length>1024)){var a=fe.test(o)?o.replace(fe,\"$1\"):o;if(!a.match(/\\S*Error: /)){for(var l of r){var u=l(a);if(u){e.push(u);break}}if(e.length>=pe)break}}}return function(t){if(!t.length)return[];var i=Array.from(t);return i.reverse(),i.slice(0,pe).map((t=>B({},t,{filename:t.filename||xe(i).filename,function:t.function||_e})))}(e)}}(...[[30,t=>{var i=me.exec(t);if(i){var[,e,r,s]=i;return ge(e,_e,+r,+s)}var n=be.exec(t);if(n){if(n[2]&&0===n[2].indexOf(\"eval\")){var o=ye.exec(n[2]);o&&(n[2]=o[1],n[3]=o[2],n[4]=o[3])}var[a,l]=Pe(n[1]||_e,n[2]);return ge(l,a,n[3]?+n[3]:void 0,n[4]?+n[4]:void 0)}}],[50,t=>{var i=we.exec(t);if(i){if(i[3]&&i[3].indexOf(\" > eval\")>-1){var e=Se.exec(i[3]);e&&(i[1]=i[1]||\"eval\",i[3]=e[1],i[4]=e[2],i[5]=\"\")}var r=i[3],s=i[1]||_e;return[s,r]=Pe(s,r),ge(r,s,i[4]?+i[4]:void 0,i[5]?+i[5]:void 0)}}]]);function xe(t){return t[t.length-1]||{}}var ke,Ee,Ie,Pe=(t,i)=>{var e=-1!==t.indexOf(\"safari-extension\"),r=-1!==t.indexOf(\"safari-web-extension\");return e||r?[-1!==t.indexOf(\"@\")?t.split(\"@\")[0]:_e,e?\"safari-extension:\"+i:\"safari-web-extension:\"+i]:[t,i]};var Re=/^(?:[Uu]ncaught (?:exception: )?)?(?:((?:Eval|Internal|Range|Reference|Syntax|Type|URI|)Error): )?(.*)$/i;function Te(t,i){void 0===i&&(i=0);var e=t.stacktrace||t.stack||\"\",r=function(t){if(t&&Me.test(t.message))return 1;return 0}(t);try{var s=$e,n=function(t,i){var e=function(t){var i=globalThis._posthogChunkIds;if(!i)return{};var e=Object.keys(i);return Ie&&e.length===Ee||(Ee=e.length,Ie=e.reduce(((e,r)=>{ke||(ke={});var s=ke[r];if(s)e[s[0]]=s[1];else for(var n=t(r),o=n.length-1;o>=0;o--){var a=n[o],l=null==a?void 0:a.filename,u=i[r];if(l&&u){e[l]=u,ke[r]=[l,u];break}}return e}),{})),Ie}(i);return t.forEach((t=>{t.filename&&(t.chunk_id=e[t.filename])})),t}(s(e,r),s);return n.slice(0,n.length-i)}catch(t){}return[]}var Me=/Minified React error #\\d+;/i;function Ce(t,i){var e,r,s=Te(t),n=null===(e=null==i?void 0:i.handled)||void 0===e||e,o=null!==(r=null==i?void 0:i.synthetic)&&void 0!==r&&r;return{type:null!=i&&i.overrideExceptionType?i.overrideExceptionType:t.name,value:function(t){var i=t.message;if(i.error&&\"string\"==typeof i.error.message)return String(i.error.message);return String(i)}(t),stacktrace:{frames:s,type:\"raw\"},mechanism:{handled:n,synthetic:o}}}function Fe(t,i){var e=Ce(t,i);return t.cause&&de(t.cause)&&t.cause!==t?[e,...Fe(t.cause,{handled:null==i?void 0:i.handled,synthetic:null==i?void 0:i.synthetic})]:[e]}function Oe(t,i){return{$exception_list:Fe(t,i),$exception_level:\"error\"}}function Ae(t,i){var e,r,s,n=null===(e=null==i?void 0:i.handled)||void 0===e||e,o=null===(r=null==i?void 0:i.synthetic)||void 0===r||r,a={type:null!=i&&i.overrideExceptionType?i.overrideExceptionType:null!==(s=null==i?void 0:i.defaultExceptionType)&&void 0!==s?s:\"Error\",value:t||(null==i?void 0:i.defaultExceptionMessage),mechanism:{handled:n,synthetic:o}};if(null!=i&&i.syntheticException){var l=Te(i.syntheticException,1);l.length&&(a.stacktrace={frames:l,type:\"raw\"})}return{$exception_list:[a],$exception_level:\"error\"}}function De(t){return T(t)&&!M(t)&&g.indexOf(t)>=0}function Le(t,i){var e,r,s=null===(e=null==i?void 0:i.handled)||void 0===e||e,n=null===(r=null==i?void 0:i.synthetic)||void 0===r||r,o=null!=i&&i.overrideExceptionType?i.overrideExceptionType:ue(t)?t.constructor.name:\"Error\",a=\"Non-Error 'exception' captured with keys: \"+function(t,i){void 0===i&&(i=40);var e=Object.keys(t);if(e.sort(),!e.length)return\"[object has no keys]\";for(var r=e.length;r>0;r--){var s=e.slice(0,r).join(\", \");if(!(s.length>i))return r===e.length||s.length<=i?s:s.slice(0,i)+\"...\"}return\"\"}(t),l={type:o,value:a,mechanism:{handled:s,synthetic:n}};if(null!=i&&i.syntheticException){var u=Te(null==i?void 0:i.syntheticException,1);u.length&&(l.stacktrace={frames:u,type:\"raw\"})}return{$exception_list:[l],$exception_level:De(t.level)?t.level:\"error\"}}function je(t,i){var{error:e,event:r}=t,s={$exception_list:[]},n=e||r;if(ce(n)||function(t){return ve(t,\"DOMException\")}(n)){var o=n;if(function(t){return\"stack\"in t}(n))s=Oe(n,i);else{var a=o.name||(ce(o)?\"DOMError\":\"DOMException\"),l=o.message?a+\": \"+o.message:a;s=Ae(l,B({},i,{overrideExceptionType:ce(o)?\"DOMError\":\"DOMException\",defaultExceptionMessage:l}))}return\"code\"in o&&(s.$exception_DOMException_code=\"\"+o.code),s}if(function(t){return ve(t,\"ErrorEvent\")}(n)&&n.error)return Oe(n.error,i);if(de(n))return Oe(n,i);if(function(t){return ve(t,\"Object\")}(n)||ue(n))return Le(n,i);if(R(e)&&T(r)){var u=\"Error\",h=r,d=r.match(Re);return d&&(u=d[1],h=d[2]),Ae(h,B({},i,{overrideExceptionType:u,defaultExceptionMessage:h}))}return Ae(n,i)}function Ne(t,i,e){try{if(!(i in t))return()=>{};var r=t[i],s=e(r);return E(s)&&(s.prototype=s.prototype||{},Object.defineProperties(s,{__posthog_wrapped__:{enumerable:!1,value:!0}})),t[i]=s,()=>{t[i]=r}}catch(t){return()=>{}}}class ze{constructor(i){var e;this._instance=i,this.ft=(null==t||null==(e=t.location)?void 0:e.pathname)||\"\"}get isEnabled(){return\"history_change\"===this._instance.config.capture_pageview}startIfEnabled(){this.isEnabled&&(N.info(\"History API monitoring enabled, starting...\"),this.monitorHistoryChanges())}stop(){this._t&&this._t(),this._t=void 0,N.info(\"History API monitoring stopped\")}monitorHistoryChanges(){var i,e;if(t&&t.history){var r=this;null!=(i=t.history.pushState)&&i.__posthog_wrapped__||Ne(t.history,\"pushState\",(t=>function(i,e,s){t.call(this,i,e,s),r.gt(\"pushState\")})),null!=(e=t.history.replaceState)&&e.__posthog_wrapped__||Ne(t.history,\"replaceState\",(t=>function(i,e,s){t.call(this,i,e,s),r.gt(\"replaceState\")})),this.bt()}}gt(i){try{var e,r=null==t||null==(e=t.location)?void 0:e.pathname;if(!r)return;r!==this.ft&&this.isEnabled&&this._instance.capture(\"$pageview\",{navigation_type:i}),this.ft=r}catch(t){N.error(\"Error capturing \"+i+\" pageview\",t)}}bt(){if(!this._t){var i=()=>{this.gt(\"popstate\")};st(t,\"popstate\",i),this._t=()=>{t&&t.removeEventListener(\"popstate\",i)}}}}function Ue(t){var i,e;return(null==(i=JSON.stringify(t,(e=[],function(t,i){if(I(i)){for(;e.length>0&&e[e.length-1]!==this;)e.pop();return e.includes(i)?\"[Circular]\":(e.push(i),i)}return i})))?void 0:i.length)||0}function qe(t,i){if(void 0===i&&(i=6606028.8),t.size>=i&&t.data.length>1){var e=Math.floor(t.data.length/2),r=t.data.slice(0,e),s=t.data.slice(e);return[qe({size:Ue(r),data:r,sessionId:t.sessionId,windowId:t.windowId}),qe({size:Ue(s),data:s,sessionId:t.sessionId,windowId:t.windowId})].flatMap((t=>t))}return[t]}var Be=(t=>(t[t.DomContentLoaded=0]=\"DomContentLoaded\",t[t.Load=1]=\"Load\",t[t.FullSnapshot=2]=\"FullSnapshot\",t[t.IncrementalSnapshot=3]=\"IncrementalSnapshot\",t[t.Meta=4]=\"Meta\",t[t.Custom=5]=\"Custom\",t[t.Plugin=6]=\"Plugin\",t))(Be||{}),He=(t=>(t[t.Mutation=0]=\"Mutation\",t[t.MouseMove=1]=\"MouseMove\",t[t.MouseInteraction=2]=\"MouseInteraction\",t[t.Scroll=3]=\"Scroll\",t[t.ViewportResize=4]=\"ViewportResize\",t[t.Input=5]=\"Input\",t[t.TouchMove=6]=\"TouchMove\",t[t.MediaInteraction=7]=\"MediaInteraction\",t[t.StyleSheetRule=8]=\"StyleSheetRule\",t[t.CanvasMutation=9]=\"CanvasMutation\",t[t.Font=10]=\"Font\",t[t.Log=11]=\"Log\",t[t.Drag=12]=\"Drag\",t[t.StyleDeclaration=13]=\"StyleDeclaration\",t[t.Selection=14]=\"Selection\",t[t.AdoptedStyleSheet=15]=\"AdoptedStyleSheet\",t[t.CustomElement=16]=\"CustomElement\",t))(He||{}),We=\"[SessionRecording]\",Ge=\"redacted\",Je={initiatorTypes:[\"audio\",\"beacon\",\"body\",\"css\",\"early-hint\",\"embed\",\"fetch\",\"frame\",\"iframe\",\"icon\",\"image\",\"img\",\"input\",\"link\",\"navigation\",\"object\",\"ping\",\"script\",\"track\",\"video\",\"xmlhttprequest\"],maskRequestFn:t=>t,recordHeaders:!1,recordBody:!1,recordInitialRequests:!1,recordPerformance:!1,performanceEntryTypeToObserve:[\"first-input\",\"navigation\",\"paint\",\"resource\"],payloadSizeLimitBytes:1e6,payloadHostDenyList:[\".lr-ingest.io\",\".ingest.sentry.io\",\".clarity.ms\",\"analytics.google.com\",\"bam.nr-data.net\"]},Ve=[\"authorization\",\"x-forwarded-for\",\"authorization\",\"cookie\",\"set-cookie\",\"x-api-key\",\"x-real-ip\",\"remote-addr\",\"forwarded\",\"proxy-authorization\",\"x-csrf-token\",\"x-csrftoken\",\"x-xsrf-token\"],Ke=[\"password\",\"secret\",\"passwd\",\"api_key\",\"apikey\",\"auth\",\"credentials\",\"mysql_pwd\",\"privatekey\",\"private_key\",\"token\"],Ye=[\"/s/\",\"/e/\",\"/i/\"];function Xe(t,i,e,r){if(F(t))return t;var s=(null==i?void 0:i[\"content-length\"])||function(t){return new Blob([t]).size}(t);return T(s)&&(s=parseInt(s)),s>e?We+\" \"+r+\" body too large to record (\"+s+\" bytes)\":t}function Qe(t,i){if(F(t))return t;var e=t;return fi(e,!1)||(e=We+\" \"+i+\" body \"+Ge),J(Ke,(t=>{var r,s;null!=(r=e)&&r.length&&-1!==(null==(s=e)?void 0:s.indexOf(t))&&(e=We+\" \"+i+\" body \"+Ge+\" as might contain: \"+t)})),e}var Ze=(t,i)=>{var e,r,s,n={payloadSizeLimitBytes:Je.payloadSizeLimitBytes,performanceEntryTypeToObserve:[...Je.performanceEntryTypeToObserve],payloadHostDenyList:[...i.payloadHostDenyList||[],...Je.payloadHostDenyList]},o=!1!==t.session_recording.recordHeaders&&i.recordHeaders,a=!1!==t.session_recording.recordBody&&i.recordBody,l=!1!==t.capture_performance&&i.recordPerformance,u=(e=n,s=Math.min(1e6,null!==(r=e.payloadSizeLimitBytes)&&void 0!==r?r:1e6),t=>(null!=t&&t.requestBody&&(t.requestBody=Xe(t.requestBody,t.requestHeaders,s,\"Request\")),null!=t&&t.responseBody&&(t.responseBody=Xe(t.responseBody,t.responseHeaders,s,\"Response\")),t)),h=i=>{return u(((t,i)=>{var e,r=Si(t.name),s=0===i.indexOf(\"http\")?null==(e=Si(i))?void 0:e.pathname:i;\"/\"===s&&(s=\"\");var n=null==r?void 0:r.pathname.replace(s||\"\",\"\");if(!(r&&n&&Ye.some((t=>0===n.indexOf(t)))))return t})((r=(e=i).requestHeaders,F(r)||J(Object.keys(null!=r?r:{}),(t=>{Ve.includes(t.toLowerCase())&&(r[t]=Ge)})),e),t.api_host));var e,r},d=E(t.session_recording.maskNetworkRequestFn);return d&&E(t.session_recording.maskCapturedNetworkRequestFn)&&N.warn(\"Both `maskNetworkRequestFn` and `maskCapturedNetworkRequestFn` are defined. `maskNetworkRequestFn` will be ignored.\"),d&&(t.session_recording.maskCapturedNetworkRequestFn=i=>{var e=t.session_recording.maskNetworkRequestFn({url:i.name});return B({},i,{name:null==e?void 0:e.url})}),n.maskRequestFn=E(t.session_recording.maskCapturedNetworkRequestFn)?i=>{var e,r=h(i);return r&&null!==(e=null==t.session_recording.maskCapturedNetworkRequestFn?void 0:t.session_recording.maskCapturedNetworkRequestFn(r))&&void 0!==e?e:void 0}:t=>function(t){if(!R(t))return t.requestBody=Qe(t.requestBody,\"Request\"),t.responseBody=Qe(t.responseBody,\"Response\"),t}(h(t)),B({},Je,n,{recordHeaders:o,recordBody:a,recordPerformance:l,recordInitialRequests:l})};class tr{constructor(t,i){var e,r;void 0===i&&(i={}),this.yt={},this.wt=t=>{if(!this.yt[t]){var i,e;this.yt[t]=!0;var r=this.St(t);null==(i=(e=this.rt).onBlockedNode)||i.call(e,t,r)}},this.$t=t=>{var i=this.St(t);if(\"svg\"!==(null==i?void 0:i.nodeName)&&i instanceof Element){var e=i.closest(\"svg\");if(e)return[this._rrweb.mirror.getId(e),e]}return[t,i]},this.St=t=>this._rrweb.mirror.getNode(t),this.xt=t=>{var i,e,r,s,n,o,a,l;return(null!==(i=null==(e=t.removes)?void 0:e.length)&&void 0!==i?i:0)+(null!==(r=null==(s=t.attributes)?void 0:s.length)&&void 0!==r?r:0)+(null!==(n=null==(o=t.texts)?void 0:o.length)&&void 0!==n?n:0)+(null!==(a=null==(l=t.adds)?void 0:l.length)&&void 0!==a?a:0)},this.throttleMutations=t=>{if(3!==t.type||0!==t.data.source)return t;var i=t.data,e=this.xt(i);i.attributes&&(i.attributes=i.attributes.filter((t=>{var[i]=this.$t(t.id);return!this.ct.consumeRateLimit(i)&&t})));var r=this.xt(i);return 0!==r||e===r?t:void 0},this._rrweb=t,this.rt=i,this.ct=new oe({bucketSize:null!==(e=this.rt.bucketSize)&&void 0!==e?e:100,refillRate:null!==(r=this.rt.refillRate)&&void 0!==r?r:10,refillInterval:1e3,et:this.wt})}}var ir=Uint8Array,er=Uint16Array,rr=Uint32Array,sr=new ir([0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0,0,0,0]),nr=new ir([0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13,0,0]),or=new ir([16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15]),ar=function(t,i){for(var e=new er(31),r=0;r<31;++r)e[r]=i+=1<<t[r-1];var s=new rr(e[30]);for(r=1;r<30;++r)for(var n=e[r];n<e[r+1];++n)s[n]=n-e[r]<<5|r;return[e,s]},lr=ar(sr,2),ur=lr[0],hr=lr[1];ur[28]=258,hr[258]=28;for(var dr=ar(nr,0)[1],vr=new er(32768),cr=0;cr<32768;++cr){var fr=(43690&cr)>>>1|(21845&cr)<<1;fr=(61680&(fr=(52428&fr)>>>2|(13107&fr)<<2))>>>4|(3855&fr)<<4,vr[cr]=((65280&fr)>>>8|(255&fr)<<8)>>>1}var pr=function(t,i,e){for(var r=t.length,s=0,n=new er(i);s<r;++s)++n[t[s]-1];var o,a=new er(i);for(s=0;s<i;++s)a[s]=a[s-1]+n[s-1]<<1;if(e){o=new er(1<<i);var l=15-i;for(s=0;s<r;++s)if(t[s])for(var u=s<<4|t[s],h=i-t[s],d=a[t[s]-1]++<<h,v=d|(1<<h)-1;d<=v;++d)o[vr[d]>>>l]=u}else for(o=new er(r),s=0;s<r;++s)o[s]=vr[a[t[s]-1]++]>>>15-t[s];return o},_r=new ir(288);for(cr=0;cr<144;++cr)_r[cr]=8;for(cr=144;cr<256;++cr)_r[cr]=9;for(cr=256;cr<280;++cr)_r[cr]=7;for(cr=280;cr<288;++cr)_r[cr]=8;var gr=new ir(32);for(cr=0;cr<32;++cr)gr[cr]=5;var mr=pr(_r,9,0),br=pr(gr,5,0),yr=function(t){return(t/8>>0)+(7&t&&1)},wr=function(t,i,e){(null==e||e>t.length)&&(e=t.length);var r=new(t instanceof er?er:t instanceof rr?rr:ir)(e-i);return r.set(t.subarray(i,e)),r},Sr=function(t,i,e){e<<=7&i;var r=i/8>>0;t[r]|=e,t[r+1]|=e>>>8},$r=function(t,i,e){e<<=7&i;var r=i/8>>0;t[r]|=e,t[r+1]|=e>>>8,t[r+2]|=e>>>16},xr=function(t,i){for(var e=[],r=0;r<t.length;++r)t[r]&&e.push({s:r,f:t[r]});var s=e.length,n=e.slice();if(!s)return[new ir(0),0];if(1==s){var o=new ir(e[0].s+1);return o[e[0].s]=1,[o,1]}e.sort((function(t,i){return t.f-i.f})),e.push({s:-1,f:25001});var a=e[0],l=e[1],u=0,h=1,d=2;for(e[0]={s:-1,f:a.f+l.f,l:a,r:l};h!=s-1;)a=e[e[u].f<e[d].f?u++:d++],l=e[u!=h&&e[u].f<e[d].f?u++:d++],e[h++]={s:-1,f:a.f+l.f,l:a,r:l};var v=n[0].s;for(r=1;r<s;++r)n[r].s>v&&(v=n[r].s);var c=new er(v+1),f=kr(e[h-1],c,0);if(f>i){r=0;var p=0,_=f-i,g=1<<_;for(n.sort((function(t,i){return c[i.s]-c[t.s]||t.f-i.f}));r<s;++r){var m=n[r].s;if(!(c[m]>i))break;p+=g-(1<<f-c[m]),c[m]=i}for(p>>>=_;p>0;){var b=n[r].s;c[b]<i?p-=1<<i-c[b]++-1:++r}for(;r>=0&&p;--r){var y=n[r].s;c[y]==i&&(--c[y],++p)}f=i}return[new ir(c),f]},kr=function(t,i,e){return-1==t.s?Math.max(kr(t.l,i,e+1),kr(t.r,i,e+1)):i[t.s]=e},Er=function(t){for(var i=t.length;i&&!t[--i];);for(var e=new er(++i),r=0,s=t[0],n=1,o=function(t){e[r++]=t},a=1;a<=i;++a)if(t[a]==s&&a!=i)++n;else{if(!s&&n>2){for(;n>138;n-=138)o(32754);n>2&&(o(n>10?n-11<<5|28690:n-3<<5|12305),n=0)}else if(n>3){for(o(s),--n;n>6;n-=6)o(8304);n>2&&(o(n-3<<5|8208),n=0)}for(;n--;)o(s);n=1,s=t[a]}return[e.subarray(0,r),i]},Ir=function(t,i){for(var e=0,r=0;r<i.length;++r)e+=t[r]*i[r];return e},Pr=function(t,i,e){var r=e.length,s=yr(i+2);t[s]=255&r,t[s+1]=r>>>8,t[s+2]=255^t[s],t[s+3]=255^t[s+1];for(var n=0;n<r;++n)t[s+n+4]=e[n];return 8*(s+4+r)},Rr=function(t,i,e,r,s,n,o,a,l,u,h){Sr(i,h++,e),++s[256];for(var d=xr(s,15),v=d[0],c=d[1],f=xr(n,15),p=f[0],_=f[1],g=Er(v),m=g[0],b=g[1],y=Er(p),w=y[0],S=y[1],$=new er(19),x=0;x<m.length;++x)$[31&m[x]]++;for(x=0;x<w.length;++x)$[31&w[x]]++;for(var k=xr($,7),E=k[0],I=k[1],P=19;P>4&&!E[or[P-1]];--P);var R,T,M,C,F=u+5<<3,O=Ir(s,_r)+Ir(n,gr)+o,A=Ir(s,v)+Ir(n,p)+o+14+3*P+Ir($,E)+(2*$[16]+3*$[17]+7*$[18]);if(F<=O&&F<=A)return Pr(i,h,t.subarray(l,l+u));if(Sr(i,h,1+(A<O)),h+=2,A<O){R=pr(v,c,0),T=v,M=pr(p,_,0),C=p;var D=pr(E,I,0);Sr(i,h,b-257),Sr(i,h+5,S-1),Sr(i,h+10,P-4),h+=14;for(x=0;x<P;++x)Sr(i,h+3*x,E[or[x]]);h+=3*P;for(var L=[m,w],j=0;j<2;++j){var N=L[j];for(x=0;x<N.length;++x){var z=31&N[x];Sr(i,h,D[z]),h+=E[z],z>15&&(Sr(i,h,N[x]>>>5&127),h+=N[x]>>>12)}}}else R=mr,T=_r,M=br,C=gr;for(x=0;x<a;++x)if(r[x]>255){z=r[x]>>>18&31;$r(i,h,R[z+257]),h+=T[z+257],z>7&&(Sr(i,h,r[x]>>>23&31),h+=sr[z]);var U=31&r[x];$r(i,h,M[U]),h+=C[U],U>3&&($r(i,h,r[x]>>>5&8191),h+=nr[U])}else $r(i,h,R[r[x]]),h+=T[r[x]];return $r(i,h,R[256]),h+T[256]},Tr=new rr([65540,131080,131088,131104,262176,1048704,1048832,2114560,2117632]),Mr=function(){for(var t=new rr(256),i=0;i<256;++i){for(var e=i,r=9;--r;)e=(1&e&&3988292384)^e>>>1;t[i]=e}return t}(),Cr=function(){var t=4294967295;return{p:function(i){for(var e=t,r=0;r<i.length;++r)e=Mr[255&e^i[r]]^e>>>8;t=e},d:function(){return 4294967295^t}}},Fr=function(t,i,e,r,s){return function(t,i,e,r,s,n){var o=t.length,a=new ir(r+o+5*(1+Math.floor(o/7e3))+s),l=a.subarray(r,a.length-s),u=0;if(!i||o<8)for(var h=0;h<=o;h+=65535){var d=h+65535;d<o?u=Pr(l,u,t.subarray(h,d)):(l[h]=n,u=Pr(l,u,t.subarray(h,o)))}else{for(var v=Tr[i-1],c=v>>>13,f=8191&v,p=(1<<e)-1,_=new er(32768),g=new er(p+1),m=Math.ceil(e/3),b=2*m,y=function(i){return(t[i]^t[i+1]<<m^t[i+2]<<b)&p},w=new rr(25e3),S=new er(288),$=new er(32),x=0,k=0,E=(h=0,0),I=0,P=0;h<o;++h){var R=y(h),T=32767&h,M=g[R];if(_[T]=M,g[R]=T,I<=h){var C=o-h;if((x>7e3||E>24576)&&C>423){u=Rr(t,l,0,w,S,$,k,E,P,h-P,u),E=x=k=0,P=h;for(var F=0;F<286;++F)S[F]=0;for(F=0;F<30;++F)$[F]=0}var O=2,A=0,D=f,L=T-M&32767;if(C>2&&R==y(h-L))for(var j=Math.min(c,C)-1,N=Math.min(32767,h),z=Math.min(258,C);L<=N&&--D&&T!=M;){if(t[h+O]==t[h+O-L]){for(var U=0;U<z&&t[h+U]==t[h+U-L];++U);if(U>O){if(O=U,A=L,U>j)break;var q=Math.min(L,U-2),B=0;for(F=0;F<q;++F){var H=h-L+F+32768&32767,W=H-_[H]+32768&32767;W>B&&(B=W,M=H)}}}L+=(T=M)-(M=_[T])+32768&32767}if(A){w[E++]=268435456|hr[O]<<18|dr[A];var G=31&hr[O],J=31&dr[A];k+=sr[G]+nr[J],++S[257+G],++$[J],I=h+O,++x}else w[E++]=t[h],++S[t[h]]}}u=Rr(t,l,n,w,S,$,k,E,P,h-P,u)}return wr(a,0,r+yr(u)+s)}(t,null==i.level?6:i.level,null==i.mem?Math.ceil(1.5*Math.max(8,Math.min(13,Math.log(t.length)))):12+i.mem,e,r,!s)},Or=function(t,i,e){for(;e;++i)t[i]=e,e>>>=8},Ar=function(t,i){var e=i.filename;if(t[0]=31,t[1]=139,t[2]=8,t[8]=i.level<2?4:9==i.level?2:0,t[9]=3,0!=i.mtime&&Or(t,4,Math.floor(new Date(i.mtime||Date.now())/1e3)),e){t[3]=8;for(var r=0;r<=e.length;++r)t[r+10]=e.charCodeAt(r)}},Dr=function(t){return 10+(t.filename&&t.filename.length+1||0)};function Lr(t,i){void 0===i&&(i={});var e=Cr(),r=t.length;e.p(t);var s=Fr(t,i,Dr(i),8),n=s.length;return Ar(s,i),Or(s,n-8,e.d()),Or(s,n-4,r),s}function jr(t,i){var e=t.length;if(\"undefined\"!=typeof TextEncoder)return(new TextEncoder).encode(t);for(var r=new ir(t.length+(t.length>>>1)),s=0,n=function(t){r[s++]=t},o=0;o<e;++o){if(s+5>r.length){var a=new ir(s+8+(e-o<<1));a.set(r),r=a}var l=t.charCodeAt(o);l<128||i?n(l):l<2048?(n(192|l>>>6),n(128|63&l)):l>55295&&l<57344?(n(240|(l=65536+(1047552&l)|1023&t.charCodeAt(++o))>>>18),n(128|l>>>12&63),n(128|l>>>6&63),n(128|63&l)):(n(224|l>>>12),n(128|l>>>6&63),n(128|63&l))}return wr(r,0,s)}function Nr(t,i){return function(t){for(var i=0,e=0;e<t.length;e++)i=(i<<5)-i+t.charCodeAt(e),i|=0;return Math.abs(i)}(t)%100<ne(100*i,0,100)}var zr=\"disabled\",Ur=\"sampled\",qr=\"active\",Br=\"buffering\",Hr=\"paused\",Wr=\"trigger\",Gr=Wr+\"_activated\",Jr=Wr+\"_pending\",Vr=Wr+\"_\"+zr;function Kr(t,i){return i.some((i=>\"regex\"===i.matching&&new RegExp(i.url).test(t)))}class Yr{constructor(t){this.kt=t}triggerStatus(t){var i=this.kt.map((i=>i.triggerStatus(t)));return i.includes(Gr)?Gr:i.includes(Jr)?Jr:Vr}stop(){this.kt.forEach((t=>t.stop()))}}class Xr{constructor(t){this.kt=t}triggerStatus(t){var i=new Set;for(var e of this.kt)i.add(e.triggerStatus(t));switch(i.delete(Vr),i.size){case 0:return Vr;case 1:return Array.from(i)[0];default:return Jr}}stop(){this.kt.forEach((t=>t.stop()))}}class Qr{triggerStatus(){return Jr}stop(){}}class Zr{constructor(t){this.Et=[],this.It=[],this.urlBlocked=!1,this._instance=t}onRemoteConfig(t){var i,e;this.Et=(null==(i=t.sessionRecording)?void 0:i.urlTriggers)||[],this.It=(null==(e=t.sessionRecording)?void 0:e.urlBlocklist)||[]}Pt(t){var i;return 0===this.Et.length?Vr:(null==(i=this._instance)?void 0:i.get_property(Et))===t?Gr:Jr}triggerStatus(t){var i=this.Pt(t),e=i===Gr?Gr:i===Jr?Jr:Vr;return this._instance.register_for_session({$sdk_debug_replay_url_trigger_status:e}),e}checkUrlTriggerConditions(i,e,r){if(void 0!==t&&t.location.href){var s=t.location.href,n=this.urlBlocked,o=Kr(s,this.It);n&&o||(o&&!n?i():!o&&n&&e(),Kr(s,this.Et)&&r(\"url\"))}}stop(){}}class ts{constructor(t){this.linkedFlag=null,this.linkedFlagSeen=!1,this.Rt=()=>{},this._instance=t}triggerStatus(){var t=Jr;return F(this.linkedFlag)&&(t=Vr),this.linkedFlagSeen&&(t=Gr),this._instance.register_for_session({$sdk_debug_replay_linked_flag_trigger_status:t}),t}onRemoteConfig(t,i){var e;if(this.linkedFlag=(null==(e=t.sessionRecording)?void 0:e.linkedFlag)||null,!F(this.linkedFlag)&&!this.linkedFlagSeen){var r=T(this.linkedFlag)?this.linkedFlag:this.linkedFlag.flag,s=T(this.linkedFlag)?null:this.linkedFlag.variant;this.Rt=this._instance.onFeatureFlags(((t,e)=>{var n=!1;if(I(e)&&r in e){var o=e[r];n=A(o)?!0===o:s?o===s:!!o}this.linkedFlagSeen=n,n&&i(r,s)}))}}stop(){this.Rt()}}class is{constructor(t){this.Tt=[],this._instance=t}onRemoteConfig(t){var i;this.Tt=(null==(i=t.sessionRecording)?void 0:i.eventTriggers)||[]}Mt(t){var i;return 0===this.Tt.length?Vr:(null==(i=this._instance)?void 0:i.get_property(It))===t?Gr:Jr}triggerStatus(t){var i=this.Mt(t),e=i===Gr?Gr:i===Jr?Jr:Vr;return this._instance.register_for_session({$sdk_debug_replay_event_trigger_status:e}),e}stop(){}}function es(t){return t.isRecordingEnabled?Br:zr}function rs(t){if(!t.receivedFlags)return Br;if(!t.isRecordingEnabled)return zr;if(t.urlTriggerMatching.urlBlocked)return Hr;var i=!0===t.isSampled,e=new Yr([t.eventTriggerMatching,t.urlTriggerMatching,t.linkedFlagMatching]).triggerStatus(t.sessionId);return i?Ur:e===Gr?qr:e===Jr?Br:!1===t.isSampled?zr:qr}function ss(t){if(!t.receivedFlags)return Br;if(!t.isRecordingEnabled)return zr;if(t.urlTriggerMatching.urlBlocked)return Hr;var i=new Xr([t.eventTriggerMatching,t.urlTriggerMatching,t.linkedFlagMatching]).triggerStatus(t.sessionId),e=i!==Vr,r=A(t.isSampled);return e&&i===Jr?Br:e&&i===Vr||r&&!t.isSampled?zr:!0===t.isSampled?Ur:qr}var ns=\"[SessionRecording]\",os=z(ns);function as(){var t;return null==v||null==(t=v.__PosthogExtensions__)||null==(t=t.rrweb)?void 0:t.record}var ls=3e5,us=[He.MouseMove,He.MouseInteraction,He.Scroll,He.ViewportResize,He.Input,He.TouchMove,He.MediaInteraction,He.Drag],hs=t=>({rrwebMethod:t,enqueuedAt:Date.now(),attempt:1});function ds(t){return function(t,i){for(var e=\"\",r=0;r<t.length;){var s=t[r++];s<128||i?e+=String.fromCharCode(s):s<224?e+=String.fromCharCode((31&s)<<6|63&t[r++]):s<240?e+=String.fromCharCode((15&s)<<12|(63&t[r++])<<6|63&t[r++]):(s=((15&s)<<18|(63&t[r++])<<12|(63&t[r++])<<6|63&t[r++])-65536,e+=String.fromCharCode(55296|s>>10,56320|1023&s))}return e}(Lr(jr(JSON.stringify(t))),!0)}function vs(t){return t.type===Be.Custom&&\"sessionIdle\"===t.data.tag}class cs{get sessionId(){return this.Ct}get Ft(){return this._instance.config.session_recording.session_idle_threshold_ms||3e5}get started(){return this.Ot}get At(){if(!this._instance.sessionManager)throw new Error(ns+\" must be started with a valid sessionManager.\");return this._instance.sessionManager}get Dt(){var t,i;return this.Lt.triggerStatus(this.sessionId)===Jr?6e4:null!==(t=null==(i=this._instance.config.session_recording)?void 0:i.full_snapshot_interval_millis)&&void 0!==t?t:ls}get jt(){var t=this._instance.get_property(kt);return A(t)?t:null}get Nt(){var t,i,e=null==(t=this.C)?void 0:t.data[(null==(i=this.C)?void 0:i.data.length)-1],{sessionStartTimestamp:r}=this.At.checkAndGetSessionAndWindowId(!0);return e?e.timestamp-r:null}get zt(){var i=!!this._instance.get_property(_t),e=!this._instance.config.disable_session_recording;return t&&i&&e}get Ut(){var t=!!this._instance.get_property(gt),i=this._instance.config.enable_recording_console_log;return null!=i?i:t}get qt(){var t,i,e,r,s,n,o=this._instance.config.session_recording.captureCanvas,a=this._instance.get_property(yt),l=null!==(t=null!==(i=null==o?void 0:o.recordCanvas)&&void 0!==i?i:null==a?void 0:a.enabled)&&void 0!==t&&t,u=null!==(e=null!==(r=null==o?void 0:o.canvasFps)&&void 0!==r?r:null==a?void 0:a.fps)&&void 0!==e?e:4,h=null!==(s=null!==(n=null==o?void 0:o.canvasQuality)&&void 0!==n?n:null==a?void 0:a.quality)&&void 0!==s?s:.4;if(\"string\"==typeof h){var d=parseFloat(h);h=isNaN(d)?.4:d}return{enabled:l,fps:ne(u,0,12,\"canvas recording fps\",4),quality:ne(h,0,1,\"canvas recording quality\",.4)}}get Bt(){var t,i,e=this._instance.get_property(mt),r={recordHeaders:null==(t=this._instance.config.session_recording)?void 0:t.recordHeaders,recordBody:null==(i=this._instance.config.session_recording)?void 0:i.recordBody},s=(null==r?void 0:r.recordHeaders)||(null==e?void 0:e.recordHeaders),n=(null==r?void 0:r.recordBody)||(null==e?void 0:e.recordBody),o=I(this._instance.config.capture_performance)?this._instance.config.capture_performance.network_timing:this._instance.config.capture_performance,a=!!(A(o)?o:null==e?void 0:e.capturePerformance);return s||n||a?{recordHeaders:s,recordBody:n,recordPerformance:a}:void 0}get Ht(){var t,i,e,r,s,n,o=this._instance.get_property(bt),a={maskAllInputs:null==(t=this._instance.config.session_recording)?void 0:t.maskAllInputs,maskTextSelector:null==(i=this._instance.config.session_recording)?void 0:i.maskTextSelector,blockSelector:null==(e=this._instance.config.session_recording)?void 0:e.blockSelector},l=null!==(r=null==a?void 0:a.maskAllInputs)&&void 0!==r?r:null==o?void 0:o.maskAllInputs,u=null!==(s=null==a?void 0:a.maskTextSelector)&&void 0!==s?s:null==o?void 0:o.maskTextSelector,h=null!==(n=null==a?void 0:a.blockSelector)&&void 0!==n?n:null==o?void 0:o.blockSelector;return R(l)&&R(u)&&R(h)?void 0:{maskAllInputs:null==l||l,maskTextSelector:u,blockSelector:h}}get Wt(){var t=this._instance.get_property(wt);return O(t)?t:null}get Gt(){var t=this._instance.get_property(St);return O(t)?t:null}get status(){return this.Jt?this.Vt({receivedFlags:this.Jt,isRecordingEnabled:this.zt,isSampled:this.jt,urlTriggerMatching:this.Kt,eventTriggerMatching:this.Yt,linkedFlagMatching:this.Xt,sessionId:this.sessionId}):Br}constructor(t){if(this.Vt=es,this.Jt=!1,this.Qt=[],this.Zt=\"unknown\",this.ti=Date.now(),this.Lt=new Qr,this.ii=void 0,this.ei=void 0,this.ri=void 0,this.si=void 0,this.ni=void 0,this._forceAllowLocalhostNetworkCapture=!1,this.oi=()=>{this.ai()},this.li=()=>{this.ui(\"browser offline\",{})},this.hi=()=>{this.ui(\"browser online\",{})},this.di=()=>{if(null!=o&&o.visibilityState){var t=\"window \"+o.visibilityState;this.ui(t,{})}},this._instance=t,this.Ot=!1,this.vi=\"/s/\",this.ci=void 0,this.Jt=!1,!this._instance.sessionManager)throw os.error(\"started without valid sessionManager\"),new Error(ns+\" started without valid sessionManager. This is a bug.\");if(this._instance.config.__preview_experimental_cookieless_mode)throw new Error(ns+\" cannot be used with __preview_experimental_cookieless_mode.\");this.Xt=new ts(this._instance),this.Kt=new Zr(this._instance),this.Yt=new is(this._instance);var{sessionId:i,windowId:e}=this.At.checkAndGetSessionAndWindowId();this.Ct=i,this.fi=e,this.C=this.pi(),this.Ft>=this.At.sessionTimeoutMs&&os.warn(\"session_idle_threshold_ms (\"+this.Ft+\") is greater than the session timeout (\"+this.At.sessionTimeoutMs+\"). Session will never be detected as idle\")}startIfEnabledOrStop(i){this.zt?(this.gi(i),st(t,\"beforeunload\",this.oi),st(t,\"offline\",this.li),st(t,\"online\",this.hi),st(t,\"visibilitychange\",this.di),this.mi(),this.bi(),F(this.ii)&&(this.ii=this._instance.on(\"eventCaptured\",(t=>{try{if(\"$pageview\"===t.event){var i=null!=t&&t.properties.$current_url?this.yi(null==t?void 0:t.properties.$current_url):\"\";if(!i)return;this.ui(\"$pageview\",{href:i})}}catch(t){os.error(\"Could not add $pageview to rrweb session\",t)}}))),this.ei||(this.ei=this.At.onSessionId(((t,i,e)=>{var r,s;e&&(this.ui(\"$session_id_change\",{sessionId:t,windowId:i,changeReason:e}),null==(r=this._instance)||null==(r=r.persistence)||r.unregister(It),null==(s=this._instance)||null==(s=s.persistence)||s.unregister(Et))})))):this.stopRecording()}stopRecording(){var i,e,r,s;this.Ot&&this.ci&&(this.ci(),this.ci=void 0,this.Ot=!1,null==t||t.removeEventListener(\"beforeunload\",this.oi),null==t||t.removeEventListener(\"offline\",this.li),null==t||t.removeEventListener(\"online\",this.hi),null==t||t.removeEventListener(\"visibilitychange\",this.di),this.pi(),clearInterval(this.wi),null==(i=this.ii)||i.call(this),this.ii=void 0,null==(e=this.ni)||e.call(this),this.ni=void 0,null==(r=this.ei)||r.call(this),this.ei=void 0,null==(s=this.si)||s.call(this),this.si=void 0,this.Yt.stop(),this.Kt.stop(),this.Xt.stop(),os.info(\"stopped\"))}Si(){var t;null==(t=this._instance.persistence)||t.unregister(kt)}$i(t){var i,e=this.Ct!==t,r=this.Wt;if(O(r)){var s=this.jt,n=e||!A(s),o=n?Nr(t,r):s;n&&(o?this.xi(Ur):os.warn(\"Sample rate (\"+r+\") has determined that this sessionId (\"+t+\") will not be sent to the server.\"),this.ui(\"samplingDecisionMade\",{sampleRate:r,isSampled:o})),null==(i=this._instance.persistence)||i.register({[kt]:o})}else this.Si()}onRemoteConfig(t){var i,e,r,s;(this.ui(\"$remote_config_received\",t),this.ki(t),null!=(i=t.sessionRecording)&&i.endpoint)&&(this.vi=null==(s=t.sessionRecording)?void 0:s.endpoint);this.mi(),\"any\"===(null==(e=t.sessionRecording)?void 0:e.triggerMatchType)?(this.Vt=rs,this.Lt=new Yr([this.Yt,this.Kt])):(this.Vt=ss,this.Lt=new Xr([this.Yt,this.Kt])),this._instance.register_for_session({$sdk_debug_replay_remote_trigger_matching_config:null==(r=t.sessionRecording)?void 0:r.triggerMatchType}),this.Kt.onRemoteConfig(t),this.Yt.onRemoteConfig(t),this.Xt.onRemoteConfig(t,((t,i)=>{this.xi(\"linked_flag_matched\",{flag:t,variant:i})})),this.Jt=!0,this.startIfEnabledOrStop()}mi(){O(this.Wt)&&F(this.si)&&(this.si=this.At.onSessionId((t=>{this.$i(t)})))}ki(t){if(this._instance.persistence){var i,e=this._instance.persistence,r=()=>{var i,r,s,n,o,a,l,u,h,d=null==(i=t.sessionRecording)?void 0:i.sampleRate,v=F(d)?null:parseFloat(d);F(v)&&this.Si();var c=null==(r=t.sessionRecording)?void 0:r.minimumDurationMilliseconds;e.register({[_t]:!!t.sessionRecording,[gt]:null==(s=t.sessionRecording)?void 0:s.consoleLogRecordingEnabled,[mt]:B({capturePerformance:t.capturePerformance},null==(n=t.sessionRecording)?void 0:n.networkPayloadCapture),[bt]:null==(o=t.sessionRecording)?void 0:o.masking,[yt]:{enabled:null==(a=t.sessionRecording)?void 0:a.recordCanvas,fps:null==(l=t.sessionRecording)?void 0:l.canvasFps,quality:null==(u=t.sessionRecording)?void 0:u.canvasQuality},[wt]:v,[St]:R(c)?null:c,[$t]:null==(h=t.sessionRecording)?void 0:h.scriptConfig})};r(),null==(i=this.ri)||i.call(this),this.ri=this.At.onSessionId(r)}}log(t,i){var e;void 0===i&&(i=\"log\"),null==(e=this._instance.sessionRecording)||e.onRRwebEmit({type:6,data:{plugin:\"rrweb/console@1\",payload:{level:i,trace:[],payload:[JSON.stringify(t)]}},timestamp:Date.now()})}gi(t){if(!R(Object.assign)&&!R(Array.from)&&!(this.Ot||this._instance.config.disable_session_recording||this._instance.consent.isOptedOut())){var i;if(this.Ot=!0,this.At.checkAndGetSessionAndWindowId(),as())this.Ei();else null==(i=v.__PosthogExtensions__)||null==i.loadExternalDependency||i.loadExternalDependency(this._instance,this.Ii,(t=>{if(t)return os.error(\"could not load recorder\",t);this.Ei()}));os.info(\"starting\"),this.status===qr&&this.xi(t||\"recording_initialized\")}}get Ii(){var t;return(null==(t=this._instance)||null==(t=t.persistence)||null==(t=t.get_property($t))?void 0:t.script)||\"recorder\"}Pi(t){var i;return 3===t.type&&-1!==us.indexOf(null==(i=t.data)?void 0:i.source)}Ri(t){var i=this.Pi(t);i||this.Zt||t.timestamp-this.ti>this.Ft&&(this.Zt=!0,clearInterval(this.wi),this.ui(\"sessionIdle\",{eventTimestamp:t.timestamp,lastActivityTimestamp:this.ti,threshold:this.Ft,bufferLength:this.C.data.length,bufferSize:this.C.size}),this.ai());var e=!1;if(i&&(this.ti=t.timestamp,this.Zt)){var r=\"unknown\"===this.Zt;this.Zt=!1,r||(this.ui(\"sessionNoLongerIdle\",{reason:\"user activity\",type:t.type}),e=!0)}if(!this.Zt){var{windowId:s,sessionId:n}=this.At.checkAndGetSessionAndWindowId(!i,t.timestamp),o=this.Ct!==n,a=this.fi!==s;this.fi=s,this.Ct=n,o||a?(this.stopRecording(),this.startIfEnabledOrStop(\"session_id_changed\")):e&&this.Ti()}}Mi(t){try{return t.rrwebMethod(),!0}catch(i){return this.Qt.length<10?this.Qt.push({enqueuedAt:t.enqueuedAt||Date.now(),attempt:t.attempt++,rrwebMethod:t.rrwebMethod}):os.warn(\"could not emit queued rrweb event.\",i,t),!1}}ui(t,i){return this.Mi(hs((()=>as().addCustomEvent(t,i))))}Ci(){return this.Mi(hs((()=>as().takeFullSnapshot())))}Ei(){var t,i,e,r,s={blockClass:\"ph-no-capture\",blockSelector:void 0,ignoreClass:\"ph-ignore-input\",maskTextClass:\"ph-mask\",maskTextSelector:void 0,maskTextFn:void 0,maskAllInputs:!0,maskInputOptions:{password:!0},maskInputFn:void 0,slimDOMOptions:{},collectFonts:!1,inlineStylesheet:!0,recordCrossOriginIframes:!1},n=this._instance.config.session_recording;for(var[o,a]of Object.entries(n||{}))o in s&&(\"maskInputOptions\"===o?s.maskInputOptions=B({password:!0},a):s[o]=a);(this.qt&&this.qt.enabled&&(s.recordCanvas=!0,s.sampling={canvas:this.qt.fps},s.dataURLOptions={type:\"image/webp\",quality:this.qt.quality}),this.Ht)&&(s.maskAllInputs=null===(i=this.Ht.maskAllInputs)||void 0===i||i,s.maskTextSelector=null!==(e=this.Ht.maskTextSelector)&&void 0!==e?e:void 0,s.blockSelector=null!==(r=this.Ht.blockSelector)&&void 0!==r?r:void 0);var l=as();if(l){this.Fi=null!==(t=this.Fi)&&void 0!==t?t:new tr(l,{refillRate:this._instance.config.session_recording.__mutationThrottlerRefillRate,bucketSize:this._instance.config.session_recording.__mutationThrottlerBucketSize,onBlockedNode:(t,i)=>{var e=\"Too many mutations on node '\"+t+\"'. Rate limiting. This could be due to SVG animations or something similar\";os.info(e,{node:i}),this.log(ns+\" \"+e,\"warn\")}});var u=this.Oi();this.ci=l(B({emit:t=>{this.onRRwebEmit(t)},plugins:u},s)),this.ti=Date.now(),this.Zt=A(this.Zt)?this.Zt:\"unknown\",this.ui(\"$session_options\",{sessionRecordingOptions:s,activePlugins:u.map((t=>null==t?void 0:t.name))}),this.ui(\"$posthog_config\",{config:this._instance.config})}else os.error(\"onScriptLoaded was called but rrwebRecord is not available. This indicates something has gone wrong.\")}Ti(){if(this.wi&&clearInterval(this.wi),!0!==this.Zt){var t=this.Dt;t&&(this.wi=setInterval((()=>{this.Ci()}),t))}}Oi(){var t,i,e=[],r=null==(t=v.__PosthogExtensions__)||null==(t=t.rrwebPlugins)?void 0:t.getRecordConsolePlugin;r&&this.Ut&&e.push(r());var s=null==(i=v.__PosthogExtensions__)||null==(i=i.rrwebPlugins)?void 0:i.getRecordNetworkPlugin;this.Bt&&E(s)&&(!wi.includes(location.hostname)||this._forceAllowLocalhostNetworkCapture?e.push(s(Ze(this._instance.config,this.Bt))):os.info(\"NetworkCapture not started because we are on localhost.\"));return e}onRRwebEmit(t){var i;if(this.Ai(),t&&I(t)){if(t.type===Be.Meta){var e=this.yi(t.data.href);if(this.Di=e,!e)return;t.data.href=e}else this.Li();if(this.Kt.checkUrlTriggerConditions((()=>this.ji()),(()=>this.Ni()),(t=>this.zi(t))),!this.Kt.urlBlocked||(r=t).type===Be.Custom&&\"recording paused\"===r.data.tag){var r;t.type===Be.FullSnapshot&&this.Ti(),t.type===Be.FullSnapshot&&this.Jt&&this.Lt.triggerStatus(this.sessionId)===Jr&&this.pi();var s=this.Fi?this.Fi.throttleMutations(t):t;if(s){var n=function(t){var i=t;if(i&&I(i)&&6===i.type&&I(i.data)&&\"rrweb/console@1\"===i.data.plugin){i.data.payload.payload.length>10&&(i.data.payload.payload=i.data.payload.payload.slice(0,10),i.data.payload.payload.push(\"...[truncated]\"));for(var e=[],r=0;r<i.data.payload.payload.length;r++)i.data.payload.payload[r]&&i.data.payload.payload[r].length>2e3?e.push(i.data.payload.payload[r].slice(0,2e3)+\"...[truncated]\"):e.push(i.data.payload.payload[r]);return i.data.payload.payload=e,t}return t}(s);if(this.Ri(n),!0!==this.Zt||vs(n)){if(vs(n)){var o=n.data.payload;if(o){var a=o.lastActivityTimestamp,l=o.threshold;n.timestamp=a+l}}var u=null===(i=this._instance.config.session_recording.compress_events)||void 0===i||i?function(t){if(Ue(t)<1024)return t;try{if(t.type===Be.FullSnapshot)return B({},t,{data:ds(t.data),cv:\"2024-10\"});if(t.type===Be.IncrementalSnapshot&&t.data.source===He.Mutation)return B({},t,{cv:\"2024-10\",data:B({},t.data,{texts:ds(t.data.texts),attributes:ds(t.data.attributes),removes:ds(t.data.removes),adds:ds(t.data.adds)})});if(t.type===Be.IncrementalSnapshot&&t.data.source===He.StyleSheetRule)return B({},t,{cv:\"2024-10\",data:B({},t.data,{adds:t.data.adds?ds(t.data.adds):void 0,removes:t.data.removes?ds(t.data.removes):void 0})})}catch(t){os.error(\"could not compress event - will use uncompressed event\",t)}return t}(n):n,h={$snapshot_bytes:Ue(u),$snapshot_data:u,$session_id:this.Ct,$window_id:this.fi};this.status!==zr?this.Ui(h):this.pi()}}}}}Li(){if(!this._instance.config.capture_pageview&&t){var i=this.yi(t.location.href);this.Di!==i&&(this.ui(\"$url_changed\",{href:i}),this.Di=i)}}Ai(){if(this.Qt.length){var t=[...this.Qt];this.Qt=[],t.forEach((t=>{Date.now()-t.enqueuedAt<=2e3&&this.Mi(t)}))}}yi(t){var i=this._instance.config.session_recording;if(i.maskNetworkRequestFn){var e,r={url:t};return null==(e=r=i.maskNetworkRequestFn(r))?void 0:e.url}return t}pi(){return this.C={size:0,data:[],sessionId:this.Ct,windowId:this.fi},this.C}ai(){this.qi&&(clearTimeout(this.qi),this.qi=void 0);var t=this.Gt,i=this.Nt,e=O(i)&&i>=0,r=O(t)&&e&&i<t;if(this.status===Br||this.status===Hr||this.status===zr||r)return this.qi=setTimeout((()=>{this.ai()}),2e3),this.C;this.C.data.length>0&&qe(this.C).forEach((t=>{this.Bi({$snapshot_bytes:t.size,$snapshot_data:t.data,$session_id:t.sessionId,$window_id:t.windowId,$lib:\"web\",$lib_version:c.LIB_VERSION})}));return this.pi()}Ui(t){var i,e=2+((null==(i=this.C)?void 0:i.data.length)||0);!this.Zt&&(this.C.size+t.$snapshot_bytes+e>943718.4||this.C.sessionId!==this.Ct)&&(this.C=this.ai()),this.C.size+=t.$snapshot_bytes,this.C.data.push(t.$snapshot_data),this.qi||this.Zt||(this.qi=setTimeout((()=>{this.ai()}),2e3))}Bi(t){this._instance.capture(\"$snapshot\",t,{_url:this._instance.requestRouter.endpointFor(\"api\",this.vi),_noTruncate:!0,_batchKey:\"recordings\",skip_client_rate_limiting:!0})}zi(t){var i;this.Lt.triggerStatus(this.sessionId)===Jr&&(null==(i=this._instance)||null==(i=i.persistence)||i.register({[\"url\"===t?Et:It]:this.Ct}),this.ai(),this.xi(t+\"_trigger_matched\"))}ji(){this.Kt.urlBlocked||(this.Kt.urlBlocked=!0,clearInterval(this.wi),os.info(\"recording paused due to URL blocker\"),this.ui(\"recording paused\",{reason:\"url blocker\"}))}Ni(){this.Kt.urlBlocked&&(this.Kt.urlBlocked=!1,this.Ci(),this.Ti(),this.ui(\"recording resumed\",{reason:\"left blocked url\"}),os.info(\"recording resumed\"))}bi(){0!==this.Yt.Tt.length&&F(this.ni)&&(this.ni=this._instance.on(\"eventCaptured\",(t=>{try{this.Yt.Tt.includes(t.event)&&this.zi(\"event\")}catch(t){os.error(\"Could not activate event trigger\",t)}})))}overrideLinkedFlag(){this.Xt.linkedFlagSeen=!0,this.Ci(),this.xi(\"linked_flag_overridden\")}overrideSampling(){var t;null==(t=this._instance.persistence)||t.register({[kt]:!0}),this.Ci(),this.xi(\"sampling_overridden\")}overrideTrigger(t){this.zi(t)}xi(t,i){this._instance.register_for_session({$session_recording_start_reason:t}),os.info(t.replace(\"_\",\" \"),i),m([\"recording_initialized\",\"session_id_changed\"],t)||this.ui(t,i)}get sdkDebugProperties(){var{sessionStartTimestamp:t}=this.At.checkAndGetSessionAndWindowId(!0);return{$recording_status:this.status,$sdk_debug_replay_internal_buffer_length:this.C.data.length,$sdk_debug_replay_internal_buffer_size:this.C.size,$sdk_debug_current_session_duration:this.Nt,$sdk_debug_session_start:t}}}var fs=z(\"[SegmentIntegration]\");function ps(t,i){var e=t.config.segment;if(!e)return i();!function(t,i){var e=t.config.segment;if(!e)return i();var r=e=>{var r=()=>e.anonymousId()||Ni();t.config.get_device_id=r,e.id()&&(t.register({distinct_id:e.id(),$device_id:r()}),t.persistence.set_property(Dt,\"identified\")),i()},s=e.user();\"then\"in s&&E(s.then)?s.then((t=>r(t))):r(s)}(t,(()=>{e.register((t=>{Promise&&Promise.resolve||fs.warn(\"This browser does not have Promise support, and can not use the segment integration\");var i=(i,e)=>{if(!e)return i;i.event.userId||i.event.anonymousId===t.get_distinct_id()||(fs.info(\"No userId set, resetting PostHog\"),t.reset()),i.event.userId&&i.event.userId!==t.get_distinct_id()&&(fs.info(\"UserId set, identifying with PostHog\"),t.identify(i.event.userId));var r=t.calculateEventProperties(e,i.event.properties);return i.event.properties=Object.assign({},r,i.event.properties),i};return{name:\"PostHog JS\",type:\"enrichment\",version:\"1.0.0\",isLoaded:()=>!0,load:()=>Promise.resolve(),track:t=>i(t,t.event.event),page:t=>i(t,\"$pageview\"),identify:t=>i(t,\"$identify\"),screen:t=>i(t,\"$screen\")}})(t)).then((()=>{i()}))}))}var _s=\"posthog-js\";function gs(t,i){var{organization:e,projectId:r,prefix:s,severityAllowList:n=[\"error\"]}=void 0===i?{}:i;return i=>{var o,a,l,u,h;if(!(\"*\"===n||n.includes(i.level))||!t.__loaded)return i;i.tags||(i.tags={});var d=t.requestRouter.endpointFor(\"ui\",\"/project/\"+t.config.token+\"/person/\"+t.get_distinct_id());i.tags[\"PostHog Person URL\"]=d,t.sessionRecordingStarted()&&(i.tags[\"PostHog Recording URL\"]=t.get_session_replay_url({withTimestamp:!0}));var v=(null==(o=i.exception)?void 0:o.values)||[],c=v.map((t=>B({},t,{stacktrace:t.stacktrace?B({},t.stacktrace,{type:\"raw\",frames:(t.stacktrace.frames||[]).map((t=>B({},t,{platform:\"web:javascript\"})))}):void 0}))),f={$exception_message:(null==(a=v[0])?void 0:a.value)||i.message,$exception_type:null==(l=v[0])?void 0:l.type,$exception_personURL:d,$exception_level:i.level,$exception_list:c,$sentry_event_id:i.event_id,$sentry_exception:i.exception,$sentry_exception_message:(null==(u=v[0])?void 0:u.value)||i.message,$sentry_exception_type:null==(h=v[0])?void 0:h.type,$sentry_tags:i.tags};return e&&r&&(f.$sentry_url=(s||\"https://sentry.io/organizations/\")+e+\"/issues/?project=\"+r+\"&query=\"+i.event_id),t.exceptions.sendExceptionEvent(f),i}}class ms{constructor(t,i,e,r,s){this.name=_s,this.setupOnce=function(n){n(gs(t,{organization:i,projectId:e,prefix:r,severityAllowList:s}))}}}var bs=null!=t&&t.location?Ei(t.location.hash,\"__posthog\")||Ei(location.hash,\"state\"):null,ys=\"_postHogToolbarParams\",ws=z(\"[Toolbar]\"),Ss=function(t){return t[t.UNINITIALIZED=0]=\"UNINITIALIZED\",t[t.LOADING=1]=\"LOADING\",t[t.LOADED=2]=\"LOADED\",t}(Ss||{});class $s{constructor(t){this.instance=t}Hi(t){v.ph_toolbar_state=t}Wi(){var t;return null!==(t=v.ph_toolbar_state)&&void 0!==t?t:Ss.UNINITIALIZED}maybeLoadToolbar(i,e,r){if(void 0===i&&(i=void 0),void 0===e&&(e=void 0),void 0===r&&(r=void 0),!t||!o)return!1;i=null!=i?i:t.location,r=null!=r?r:t.history;try{if(!e){try{t.localStorage.setItem(\"test\",\"test\"),t.localStorage.removeItem(\"test\")}catch(t){return!1}e=null==t?void 0:t.localStorage}var s,n=bs||Ei(i.hash,\"__posthog\")||Ei(i.hash,\"state\"),a=n?X((()=>JSON.parse(atob(decodeURIComponent(n)))))||X((()=>JSON.parse(decodeURIComponent(n)))):null;return a&&\"ph_authorize\"===a.action?((s=a).source=\"url\",s&&Object.keys(s).length>0&&(a.desiredHash?i.hash=a.desiredHash:r?r.replaceState(r.state,\"\",i.pathname+i.search):i.hash=\"\")):((s=JSON.parse(e.getItem(ys)||\"{}\")).source=\"localstorage\",delete s.userIntent),!(!s.token||this.instance.config.token!==s.token)&&(this.loadToolbar(s),!0)}catch(t){return!1}}Gi(t){var i=v.ph_load_toolbar||v.ph_load_editor;!F(i)&&E(i)?i(t,this.instance):ws.warn(\"No toolbar load function found\")}loadToolbar(i){var e=!(null==o||!o.getElementById(Bt));if(!t||e)return!1;var r=\"custom\"===this.instance.requestRouter.region&&this.instance.config.advanced_disable_toolbar_metrics,s=B({token:this.instance.config.token},i,{apiURL:this.instance.requestRouter.endpointFor(\"ui\")},r?{instrument:!1}:{});if(t.localStorage.setItem(ys,JSON.stringify(B({},s,{source:void 0}))),this.Wi()===Ss.LOADED)this.Gi(s);else if(this.Wi()===Ss.UNINITIALIZED){var n;this.Hi(Ss.LOADING),null==(n=v.__PosthogExtensions__)||null==n.loadExternalDependency||n.loadExternalDependency(this.instance,\"toolbar\",(t=>{if(t)return ws.error(\"[Toolbar] Failed to load\",t),void this.Hi(Ss.UNINITIALIZED);this.Hi(Ss.LOADED),this.Gi(s)})),st(t,\"turbolinks:load\",(()=>{this.Hi(Ss.UNINITIALIZED),this.loadToolbar(s)}))}return!0}Ji(t){return this.loadToolbar(t)}maybeLoadEditor(t,i,e){return void 0===t&&(t=void 0),void 0===i&&(i=void 0),void 0===e&&(e=void 0),this.maybeLoadToolbar(t,i,e)}}var xs=z(\"[TracingHeaders]\");class ks{constructor(t){this.Vi=void 0,this.Ki=void 0,this.nt=()=>{var t,i;R(this.Vi)&&(null==(t=v.__PosthogExtensions__)||null==(t=t.tracingHeadersPatchFns)||t._patchXHR(this._instance.config.__add_tracing_headers||[],this._instance.get_distinct_id(),this._instance.sessionManager));R(this.Ki)&&(null==(i=v.__PosthogExtensions__)||null==(i=i.tracingHeadersPatchFns)||i._patchFetch(this._instance.config.__add_tracing_headers||[],this._instance.get_distinct_id(),this._instance.sessionManager))},this._instance=t}J(t){var i,e;null!=(i=v.__PosthogExtensions__)&&i.tracingHeadersPatchFns&&t(),null==(e=v.__PosthogExtensions__)||null==e.loadExternalDependency||e.loadExternalDependency(this._instance,\"tracing-headers\",(i=>{if(i)return xs.error(\"failed to load script\",i);t()}))}startIfEnabledOrStop(){var t,i;this._instance.config.__add_tracing_headers?this.J(this.nt):(null==(t=this.Vi)||t.call(this),null==(i=this.Ki)||i.call(this),this.Vi=void 0,this.Ki=void 0)}}var Es=z(\"[Web Vitals]\"),Is=9e5;class Ps{constructor(t){var i;this.Yi=!1,this.i=!1,this.C={url:void 0,metrics:[],firstMetricTimestamp:void 0},this.Xi=()=>{clearTimeout(this.Qi),0!==this.C.metrics.length&&(this._instance.capture(\"$web_vitals\",this.C.metrics.reduce(((t,i)=>B({},t,{[\"$web_vitals_\"+i.name+\"_event\"]:B({},i),[\"$web_vitals_\"+i.name+\"_value\"]:i.value})),{})),this.C={url:void 0,metrics:[],firstMetricTimestamp:void 0})},this.Zi=t=>{var i,e=null==(i=this._instance.sessionManager)?void 0:i.checkAndGetSessionAndWindowId(!0);if(R(e))Es.error(\"Could not read session ID. Dropping metrics!\");else{this.C=this.C||{url:void 0,metrics:[],firstMetricTimestamp:void 0};var r=this.te();if(!R(r))if(F(null==t?void 0:t.name)||F(null==t?void 0:t.value))Es.error(\"Invalid metric received\",t);else if(this.ie&&t.value>=this.ie)Es.error(\"Ignoring metric with value >= \"+this.ie,t);else this.C.url!==r&&(this.Xi(),this.Qi=setTimeout(this.Xi,this.flushToCaptureTimeoutMs)),R(this.C.url)&&(this.C.url=r),this.C.firstMetricTimestamp=R(this.C.firstMetricTimestamp)?Date.now():this.C.firstMetricTimestamp,t.attribution&&t.attribution.interactionTargetElement&&(t.attribution.interactionTargetElement=void 0),this.C.metrics.push(B({},t,{$current_url:r,$session_id:e.sessionId,$window_id:e.windowId,timestamp:Date.now()})),this.C.metrics.length===this.allowedMetrics.length&&this.Xi()}},this.nt=()=>{var t,i,e,r,s=v.__PosthogExtensions__;R(s)||R(s.postHogWebVitalsCallbacks)||({onLCP:t,onCLS:i,onFCP:e,onINP:r}=s.postHogWebVitalsCallbacks),t&&i&&e&&r?(this.allowedMetrics.indexOf(\"LCP\")>-1&&t(this.Zi.bind(this)),this.allowedMetrics.indexOf(\"CLS\")>-1&&i(this.Zi.bind(this)),this.allowedMetrics.indexOf(\"FCP\")>-1&&e(this.Zi.bind(this)),this.allowedMetrics.indexOf(\"INP\")>-1&&r(this.Zi.bind(this)),this.i=!0):Es.error(\"web vitals callbacks not loaded - not starting\")},this._instance=t,this.Yi=!(null==(i=this._instance.persistence)||!i.props[ct]),this.startIfEnabled()}get allowedMetrics(){var t,i,e=I(this._instance.config.capture_performance)?null==(t=this._instance.config.capture_performance)?void 0:t.web_vitals_allowed_metrics:void 0;return R(e)?(null==(i=this._instance.persistence)?void 0:i.props[pt])||[\"CLS\",\"FCP\",\"INP\",\"LCP\"]:e}get flushToCaptureTimeoutMs(){return(I(this._instance.config.capture_performance)?this._instance.config.capture_performance.web_vitals_delayed_flush_ms:void 0)||5e3}get ie(){var t=I(this._instance.config.capture_performance)&&O(this._instance.config.capture_performance.__web_vitals_max_value)?this._instance.config.capture_performance.__web_vitals_max_value:Is;return 0<t&&t<=6e4?Is:t}get isEnabled(){var t=null==a?void 0:a.protocol;if(\"http:\"!==t&&\"https:\"!==t)return Es.info(\"Web Vitals are disabled on non-http/https protocols\"),!1;var i=I(this._instance.config.capture_performance)?this._instance.config.capture_performance.web_vitals:A(this._instance.config.capture_performance)?this._instance.config.capture_performance:void 0;return A(i)?i:this.Yi}startIfEnabled(){this.isEnabled&&!this.i&&(Es.info(\"enabled, starting...\"),this.J(this.nt))}onRemoteConfig(t){var i=I(t.capturePerformance)&&!!t.capturePerformance.web_vitals,e=I(t.capturePerformance)?t.capturePerformance.web_vitals_allowed_metrics:void 0;this._instance.persistence&&(this._instance.persistence.register({[ct]:i}),this._instance.persistence.register({[pt]:e})),this.Yi=i,this.startIfEnabled()}J(t){var i,e;null!=(i=v.__PosthogExtensions__)&&i.postHogWebVitalsCallbacks&&t(),null==(e=v.__PosthogExtensions__)||null==e.loadExternalDependency||e.loadExternalDependency(this._instance,\"web-vitals\",(i=>{i?Es.error(\"failed to load script\",i):t()}))}te(){var i=t?t.location.href:void 0;return i||Es.error(\"Could not determine current URL\"),i}}var Rs=z(\"[Heatmaps]\");function Ts(t){return I(t)&&\"clientX\"in t&&\"clientY\"in t&&O(t.clientX)&&O(t.clientY)}class Ms{constructor(t){var i;this.rageclicks=new yi,this.Yi=!1,this.i=!1,this.ee=null,this.instance=t,this.Yi=!(null==(i=this.instance.persistence)||!i.props[ut])}get flushIntervalMilliseconds(){var t=5e3;return I(this.instance.config.capture_heatmaps)&&this.instance.config.capture_heatmaps.flush_interval_milliseconds&&(t=this.instance.config.capture_heatmaps.flush_interval_milliseconds),t}get isEnabled(){return R(this.instance.config.capture_heatmaps)?R(this.instance.config.enable_heatmaps)?this.Yi:this.instance.config.enable_heatmaps:!1!==this.instance.config.capture_heatmaps}startIfEnabled(){if(this.isEnabled){if(this.i)return;Rs.info(\"starting...\"),this.re(),this.ee=setInterval(this.se.bind(this),this.flushIntervalMilliseconds)}else{var t,i;clearInterval(null!==(t=this.ee)&&void 0!==t?t:void 0),null==(i=this.ne)||i.stop(),this.getAndClearBuffer()}}onRemoteConfig(t){var i=!!t.heatmaps;this.instance.persistence&&this.instance.persistence.register({[ut]:i}),this.Yi=i,this.startIfEnabled()}getAndClearBuffer(){var t=this.C;return this.C=void 0,t}oe(t){this.ae(t.originalEvent,\"deadclick\")}re(){t&&o&&(st(t,\"beforeunload\",this.se.bind(this)),st(o,\"click\",(i=>this.ae(i||(null==t?void 0:t.event))),{capture:!0}),st(o,\"mousemove\",(i=>this.le(i||(null==t?void 0:t.event))),{capture:!0}),this.ne=new se(this.instance,ee,this.oe.bind(this)),this.ne.startIfEnabled(),this.i=!0)}ue(i,e){var r=this.instance.scrollManager.scrollY(),s=this.instance.scrollManager.scrollX(),n=this.instance.scrollManager.scrollElement(),o=function(i,e,r){for(var s=i;s&&Jt(s)&&!Vt(s,\"body\");){if(s===r)return!1;if(m(e,null==t?void 0:t.getComputedStyle(s).position))return!0;s=si(s)}return!1}(ei(i),[\"fixed\",\"sticky\"],n);return{x:i.clientX+(o?0:s),y:i.clientY+(o?0:r),target_fixed:o,type:e}}ae(t,i){var e;if(void 0===i&&(i=\"click\"),!Gt(t.target)&&Ts(t)){var r=this.ue(t,i);null!=(e=this.rageclicks)&&e.isRageClick(t.clientX,t.clientY,(new Date).getTime())&&this.he(B({},r,{type:\"rageclick\"})),this.he(r)}}le(t){!Gt(t.target)&&Ts(t)&&(clearTimeout(this.de),this.de=setTimeout((()=>{this.he(this.ue(t,\"mousemove\"))}),500))}he(i){if(t){var e=t.location.href;this.C=this.C||{},this.C[e]||(this.C[e]=[]),this.C[e].push(i)}}se(){this.C&&!P(this.C)&&this.instance.capture(\"$$heatmap\",{$heatmap_data:this.getAndClearBuffer()})}}class Cs{constructor(t){this._instance=t}doPageView(i,e){var r,s=this.ve(i,e);return this.ce={pathname:null!==(r=null==t?void 0:t.location.pathname)&&void 0!==r?r:\"\",pageViewId:e,timestamp:i},this._instance.scrollManager.resetContext(),s}doPageLeave(t){var i;return this.ve(t,null==(i=this.ce)?void 0:i.pageViewId)}doEvent(){var t;return{$pageview_id:null==(t=this.ce)?void 0:t.pageViewId}}ve(t,i){var e=this.ce;if(!e)return{$pageview_id:i};var r={$pageview_id:i,$prev_pageview_id:e.pageViewId},s=this._instance.scrollManager.getContext();if(s&&!this._instance.config.disable_scroll_properties){var{maxScrollHeight:n,lastScrollY:o,maxScrollY:a,maxContentHeight:l,lastContentY:u,maxContentY:h}=s;if(!(R(n)||R(o)||R(a)||R(l)||R(u)||R(h))){n=Math.ceil(n),o=Math.ceil(o),a=Math.ceil(a),l=Math.ceil(l),u=Math.ceil(u),h=Math.ceil(h);var d=n<=1?1:ne(o/n,0,1),v=n<=1?1:ne(a/n,0,1),c=l<=1?1:ne(u/l,0,1),f=l<=1?1:ne(h/l,0,1);r=V(r,{$prev_pageview_last_scroll:o,$prev_pageview_last_scroll_percentage:d,$prev_pageview_max_scroll:a,$prev_pageview_max_scroll_percentage:v,$prev_pageview_last_content:u,$prev_pageview_last_content_percentage:c,$prev_pageview_max_content:h,$prev_pageview_max_content_percentage:f})}}return e.pathname&&(r.$prev_pageview_pathname=e.pathname),e.timestamp&&(r.$prev_pageview_duration=(t.getTime()-e.timestamp.getTime())/1e3),r}}var Fs=function(t){var i,e,r,s,n=\"\";for(i=e=0,r=(t=(t+\"\").replace(/\\r\\n/g,\"\\n\").replace(/\\r/g,\"\\n\")).length,s=0;s<r;s++){var o=t.charCodeAt(s),a=null;o<128?e++:a=o>127&&o<2048?String.fromCharCode(o>>6|192,63&o|128):String.fromCharCode(o>>12|224,o>>6&63|128,63&o|128),C(a)||(e>i&&(n+=t.substring(i,e)),n+=a,i=e=s+1)}return e>i&&(n+=t.substring(i,t.length)),n},Os=!!u||!!l,As=\"text/plain\",Ds=(t,i)=>{var[e,r]=t.split(\"?\"),s=B({},i);null==r||r.split(\"&\").forEach((t=>{var[i]=t.split(\"=\");delete s[i]}));var n=$i(s);return e+\"?\"+(n=n?(r?r+\"&\":\"\")+n:r)},Ls=(t,i)=>JSON.stringify(t,((t,i)=>\"bigint\"==typeof i?i.toString():i),i),js=t=>{var{data:i,compression:e}=t;if(i){if(e===_.GZipJS){var r=Lr(jr(Ls(i)),{mtime:0}),s=new Blob([r],{type:As});return{contentType:As,body:s,estimatedSize:s.size}}if(e===_.Base64){var n=function(t){var i,e,r,s,n,o=\"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=\",a=0,l=0,u=\"\",h=[];if(!t)return t;t=Fs(t);do{i=(n=t.charCodeAt(a++)<<16|t.charCodeAt(a++)<<8|t.charCodeAt(a++))>>18&63,e=n>>12&63,r=n>>6&63,s=63&n,h[l++]=o.charAt(i)+o.charAt(e)+o.charAt(r)+o.charAt(s)}while(a<t.length);switch(u=h.join(\"\"),t.length%3){case 1:u=u.slice(0,-2)+\"==\";break;case 2:u=u.slice(0,-1)+\"=\"}return u}(Ls(i)),o=(t=>\"data=\"+encodeURIComponent(\"string\"==typeof t?t:Ls(t)))(n);return{contentType:\"application/x-www-form-urlencoded\",body:o,estimatedSize:new Blob([o]).size}}var a=Ls(i);return{contentType:\"application/json\",body:a,estimatedSize:new Blob([a]).size}}},Ns=[];l&&Ns.push({transport:\"fetch\",method:t=>{var i,e,{contentType:r,body:s,estimatedSize:n}=null!==(i=js(t))&&void 0!==i?i:{},o=new Headers;J(t.headers,(function(t,i){o.append(i,t)})),r&&o.append(\"Content-Type\",r);var a=t.url,u=null;if(h){var d=new h;u={signal:d.signal,timeout:setTimeout((()=>d.abort()),t.timeout)}}l(a,B({method:(null==t?void 0:t.method)||\"GET\",headers:o,keepalive:\"POST\"===t.method&&(n||0)<52428.8,body:s,signal:null==(e=u)?void 0:e.signal},t.fetchOptions)).then((i=>i.text().then((e=>{var r={statusCode:i.status,text:e};if(200===i.status)try{r.json=JSON.parse(e)}catch(t){N.error(t)}null==t.callback||t.callback(r)})))).catch((i=>{N.error(i),null==t.callback||t.callback({statusCode:0,text:i})})).finally((()=>u?clearTimeout(u.timeout):null))}}),u&&Ns.push({transport:\"XHR\",method:t=>{var i,e=new u;e.open(t.method||\"GET\",t.url,!0);var{contentType:r,body:s}=null!==(i=js(t))&&void 0!==i?i:{};J(t.headers,(function(t,i){e.setRequestHeader(i,t)})),r&&e.setRequestHeader(\"Content-Type\",r),t.timeout&&(e.timeout=t.timeout),e.withCredentials=!0,e.onreadystatechange=()=>{if(4===e.readyState){var i={statusCode:e.status,text:e.responseText};if(200===e.status)try{i.json=JSON.parse(e.responseText)}catch(t){}null==t.callback||t.callback(i)}},e.send(s)}}),null!=n&&n.sendBeacon&&Ns.push({transport:\"sendBeacon\",method:t=>{var i=Ds(t.url,{beacon:\"1\"});try{var e,{contentType:r,body:s}=null!==(e=js(t))&&void 0!==e?e:{},o=\"string\"==typeof s?new Blob([s],{type:r}):s;n.sendBeacon(i,o)}catch(t){}}});var zs=function(t,i){if(!function(t){try{new RegExp(t)}catch(t){return!1}return!0}(i))return!1;try{return new RegExp(i).test(t)}catch(t){return!1}};function Us(t,i,e){return Ls({distinct_id:t,userPropertiesToSet:i,userPropertiesToSetOnce:e})}var qs={exact:(t,i)=>i.some((i=>t.some((t=>i===t)))),is_not:(t,i)=>i.every((i=>t.every((t=>i!==t)))),regex:(t,i)=>i.some((i=>t.some((t=>zs(i,t))))),not_regex:(t,i)=>i.every((i=>t.every((t=>!zs(i,t))))),icontains:(t,i)=>i.map(Bs).some((i=>t.map(Bs).some((t=>i.includes(t))))),not_icontains:(t,i)=>i.map(Bs).every((i=>t.map(Bs).every((t=>!i.includes(t)))))},Bs=t=>t.toLowerCase(),Hs=z(\"[Error tracking]\");class Ws{constructor(t){var i,e;this.fe=[],this._instance=t,this.fe=null!==(i=null==(e=this._instance.persistence)?void 0:e.get_property(dt))&&void 0!==i?i:[]}onRemoteConfig(t){var i,e,r,s=null!==(i=null==(e=t.errorTracking)?void 0:e.suppressionRules)&&void 0!==i?i:[],n=null==(r=t.errorTracking)?void 0:r.captureExtensionExceptions;this.fe=s,this._instance.persistence&&this._instance.persistence.register({[dt]:this.fe,[vt]:n})}get pe(){var t,i=!!this._instance.get_property(vt),e=this._instance.config.error_tracking.captureExtensionExceptions;return null!==(t=null!=e?e:i)&&void 0!==t&&t}sendExceptionEvent(t){if(this._e(t))Hs.info(\"Skipping exception capture because a suppression rule matched\");else{if(this.pe||!this.ge(t))return this._instance.capture(\"$exception\",t,{_noTruncate:!0,_batchKey:\"exceptionEvent\"});Hs.info(\"Skipping exception capture because it was thrown by an extension\")}}_e(t){var i=t.$exception_list;if(!i||!k(i)||0===i.length)return!1;var e=i.reduce(((t,i)=>{var{type:e,value:r}=i;return T(e)&&e.length>0&&t.$exception_types.push(e),T(r)&&r.length>0&&t.$exception_values.push(r),t}),{$exception_types:[],$exception_values:[]});return this.fe.some((t=>{var i=t.values.map((t=>{var i,r=qs[t.operator],s=k(t.value)?t.value:[t.value],n=null!==(i=e[t.key])&&void 0!==i?i:[];return s.length>0&&r(s,n)}));return\"OR\"===t.type?i.some(Boolean):i.every(Boolean)}))}ge(t){var i=t.$exception_list;return!(!i||!k(i))&&i.flatMap((t=>{var i,e;return null!==(i=null==(e=t.stacktrace)?void 0:e.frames)&&void 0!==i?i:[]})).some((t=>t.filename&&t.filename.startsWith(\"chrome-extension://\")))}}var Gs=\"Mobile\",Js=\"iOS\",Vs=\"Android\",Ks=\"Tablet\",Ys=Vs+\" \"+Ks,Xs=\"iPad\",Qs=\"Apple\",Zs=Qs+\" Watch\",tn=\"Safari\",en=\"BlackBerry\",rn=\"Samsung\",sn=rn+\"Browser\",nn=rn+\" Internet\",on=\"Chrome\",an=on+\" OS\",ln=on+\" \"+Js,un=\"Internet Explorer\",hn=un+\" \"+Gs,dn=\"Opera\",vn=dn+\" Mini\",cn=\"Edge\",fn=\"Microsoft \"+cn,pn=\"Firefox\",_n=pn+\" \"+Js,gn=\"Nintendo\",mn=\"PlayStation\",bn=\"Xbox\",yn=Vs+\" \"+Gs,wn=Gs+\" \"+tn,Sn=\"Windows\",$n=Sn+\" Phone\",xn=\"Nokia\",kn=\"Ouya\",En=\"Generic\",In=En+\" \"+Gs.toLowerCase(),Pn=En+\" \"+Ks.toLowerCase(),Rn=\"Konqueror\",Tn=\"(\\\\d+(\\\\.\\\\d+)?)\",Mn=new RegExp(\"Version/\"+Tn),Cn=new RegExp(bn,\"i\"),Fn=new RegExp(mn+\" \\\\w+\",\"i\"),On=new RegExp(gn+\" \\\\w+\",\"i\"),An=new RegExp(en+\"|PlayBook|BB10\",\"i\"),Dn={\"NT3.51\":\"NT 3.11\",\"NT4.0\":\"NT 4.0\",\"5.0\":\"2000\",5.1:\"XP\",5.2:\"XP\",\"6.0\":\"Vista\",6.1:\"7\",6.2:\"8\",6.3:\"8.1\",6.4:\"10\",\"10.0\":\"10\"};var Ln=(t,i)=>i&&m(i,Qs)||function(t){return m(t,tn)&&!m(t,on)&&!m(t,Vs)}(t),jn=function(t,i){return i=i||\"\",m(t,\" OPR/\")&&m(t,\"Mini\")?vn:m(t,\" OPR/\")?dn:An.test(t)?en:m(t,\"IE\"+Gs)||m(t,\"WPDesktop\")?hn:m(t,sn)?nn:m(t,cn)||m(t,\"Edg/\")?fn:m(t,\"FBIOS\")?\"Facebook \"+Gs:m(t,\"UCWEB\")||m(t,\"UCBrowser\")?\"UC Browser\":m(t,\"CriOS\")?ln:m(t,\"CrMo\")||m(t,on)?on:m(t,Vs)&&m(t,tn)?yn:m(t,\"FxiOS\")?_n:m(t.toLowerCase(),Rn.toLowerCase())?Rn:Ln(t,i)?m(t,Gs)?wn:tn:m(t,pn)?pn:m(t,\"MSIE\")||m(t,\"Trident/\")?un:m(t,\"Gecko\")?pn:\"\"},Nn={[hn]:[new RegExp(\"rv:\"+Tn)],[fn]:[new RegExp(cn+\"?\\\\/\"+Tn)],[on]:[new RegExp(\"(\"+on+\"|CrMo)\\\\/\"+Tn)],[ln]:[new RegExp(\"CriOS\\\\/\"+Tn)],\"UC Browser\":[new RegExp(\"(UCBrowser|UCWEB)\\\\/\"+Tn)],[tn]:[Mn],[wn]:[Mn],[dn]:[new RegExp(\"(Opera|OPR)\\\\/\"+Tn)],[pn]:[new RegExp(pn+\"\\\\/\"+Tn)],[_n]:[new RegExp(\"FxiOS\\\\/\"+Tn)],[Rn]:[new RegExp(\"Konqueror[:/]?\"+Tn,\"i\")],[en]:[new RegExp(en+\" \"+Tn),Mn],[yn]:[new RegExp(\"android\\\\s\"+Tn,\"i\")],[nn]:[new RegExp(sn+\"\\\\/\"+Tn)],[un]:[new RegExp(\"(rv:|MSIE )\"+Tn)],Mozilla:[new RegExp(\"rv:\"+Tn)]},zn=function(t,i){var e=jn(t,i),r=Nn[e];if(R(r))return null;for(var s=0;s<r.length;s++){var n=r[s],o=t.match(n);if(o)return parseFloat(o[o.length-2])}return null},Un=[[new RegExp(bn+\"; \"+bn+\" (.*?)[);]\",\"i\"),t=>[bn,t&&t[1]||\"\"]],[new RegExp(gn,\"i\"),[gn,\"\"]],[new RegExp(mn,\"i\"),[mn,\"\"]],[An,[en,\"\"]],[new RegExp(Sn,\"i\"),(t,i)=>{if(/Phone/.test(i)||/WPDesktop/.test(i))return[$n,\"\"];if(new RegExp(Gs).test(i)&&!/IEMobile\\b/.test(i))return[Sn+\" \"+Gs,\"\"];var e=/Windows NT ([0-9.]+)/i.exec(i);if(e&&e[1]){var r=e[1],s=Dn[r]||\"\";return/arm/i.test(i)&&(s=\"RT\"),[Sn,s]}return[Sn,\"\"]}],[/((iPhone|iPad|iPod).*?OS (\\d+)_(\\d+)_?(\\d+)?|iPhone)/,t=>{if(t&&t[3]){var i=[t[3],t[4],t[5]||\"0\"];return[Js,i.join(\".\")]}return[Js,\"\"]}],[/(watch.*\\/(\\d+\\.\\d+\\.\\d+)|watch os,(\\d+\\.\\d+),)/i,t=>{var i=\"\";return t&&t.length>=3&&(i=R(t[2])?t[3]:t[2]),[\"watchOS\",i]}],[new RegExp(\"(\"+Vs+\" (\\\\d+)\\\\.(\\\\d+)\\\\.?(\\\\d+)?|\"+Vs+\")\",\"i\"),t=>{if(t&&t[2]){var i=[t[2],t[3],t[4]||\"0\"];return[Vs,i.join(\".\")]}return[Vs,\"\"]}],[/Mac OS X (\\d+)[_.](\\d+)[_.]?(\\d+)?/i,t=>{var i=[\"Mac OS X\",\"\"];if(t&&t[1]){var e=[t[1],t[2],t[3]||\"0\"];i[1]=e.join(\".\")}return i}],[/Mac/i,[\"Mac OS X\",\"\"]],[/CrOS/,[an,\"\"]],[/Linux|debian/i,[\"Linux\",\"\"]]],qn=function(t){return On.test(t)?gn:Fn.test(t)?mn:Cn.test(t)?bn:new RegExp(kn,\"i\").test(t)?kn:new RegExp(\"(\"+$n+\"|WPDesktop)\",\"i\").test(t)?$n:/iPad/.test(t)?Xs:/iPod/.test(t)?\"iPod Touch\":/iPhone/.test(t)?\"iPhone\":/(watch)(?: ?os[,/]|\\d,\\d\\/)[\\d.]+/i.test(t)?Zs:An.test(t)?en:/(kobo)\\s(ereader|touch)/i.test(t)?\"Kobo\":new RegExp(xn,\"i\").test(t)?xn:/(kf[a-z]{2}wi|aeo[c-r]{2})( bui|\\))/i.test(t)||/(kf[a-z]+)( bui|\\)).+silk\\//i.test(t)?\"Kindle Fire\":/(Android|ZTE)/i.test(t)?!new RegExp(Gs).test(t)||/(9138B|TB782B|Nexus [97]|pixel c|HUAWEISHT|BTV|noble nook|smart ultra 6)/i.test(t)?/pixel[\\daxl ]{1,6}/i.test(t)&&!/pixel c/i.test(t)||/(huaweimed-al00|tah-|APA|SM-G92|i980|zte|U304AA)/i.test(t)||/lmy47v/i.test(t)&&!/QTAQZ3/i.test(t)?Vs:Ys:Vs:new RegExp(\"(pda|\"+Gs+\")\",\"i\").test(t)?In:new RegExp(Ks,\"i\").test(t)&&!new RegExp(Ks+\" pc\",\"i\").test(t)?Pn:\"\"},Bn=\"https?://(.*)\",Hn=[\"gclid\",\"gclsrc\",\"dclid\",\"gbraid\",\"wbraid\",\"fbclid\",\"msclkid\",\"twclid\",\"li_fat_id\",\"igshid\",\"ttclid\",\"rdt_cid\",\"epik\",\"qclid\",\"sccid\",\"irclid\",\"_kx\"],Wn=K([\"utm_source\",\"utm_medium\",\"utm_campaign\",\"utm_content\",\"utm_term\",\"gad_source\",\"mc_cid\"],Hn),Gn=\"<masked>\",Jn=[\"li_fat_id\"];function Vn(t,i,e){if(!o)return{};var r,s=i?K([],Hn,e||[]):[],n=Kn(ki(o.URL,s,Gn),t),a=(r={},J(Jn,(function(t){var i=Hi.D(t);r[t]=i||null})),r);return V(a,n)}function Kn(t,i){var e=Wn.concat(i||[]),r={};return J(e,(function(i){var e=xi(t,i);r[i]=e||null})),r}function Yn(t){var i=function(t){return t?0===t.search(Bn+\"google.([^/?]*)\")?\"google\":0===t.search(Bn+\"bing.com\")?\"bing\":0===t.search(Bn+\"yahoo.com\")?\"yahoo\":0===t.search(Bn+\"duckduckgo.com\")?\"duckduckgo\":null:null}(t),e=\"yahoo\"!=i?\"q\":\"p\",r={};if(!C(i)){r.$search_engine=i;var s=o?xi(o.referrer,e):\"\";s.length&&(r.ph_keyword=s)}return r}function Xn(){return navigator.language||navigator.userLanguage}function Qn(){return(null==o?void 0:o.referrer)||\"$direct\"}function Zn(t,i){var e=t?K([],Hn,i||[]):[],r=null==a?void 0:a.href.substring(0,1e3);return{r:Qn().substring(0,1e3),u:r?ki(r,e,Gn):void 0}}function to(t){var i,{r:e,u:r}=t,s={$referrer:e,$referring_domain:null==e?void 0:\"$direct\"==e?\"$direct\":null==(i=Si(e))?void 0:i.host};if(r){s.$current_url=r;var n=Si(r);s.$host=null==n?void 0:n.host,s.$pathname=null==n?void 0:n.pathname;var o=Kn(r);V(s,o)}if(e){var a=Yn(e);V(s,a)}return s}function io(){try{return Intl.DateTimeFormat().resolvedOptions().timeZone}catch(t){return}}function eo(){try{return(new Date).getTimezoneOffset()}catch(t){return}}function ro(i,e){if(!d)return{};var r,s,n,o=i?K([],Hn,e||[]):[],[l,u]=function(t){for(var i=0;i<Un.length;i++){var[e,r]=Un[i],s=e.exec(t),n=s&&(E(r)?r(s,t):r);if(n)return n}return[\"\",\"\"]}(d);return V(Z({$os:l,$os_version:u,$browser:jn(d,navigator.vendor),$device:qn(d),$device_type:(s=d,n=qn(s),n===Xs||n===Ys||\"Kobo\"===n||\"Kindle Fire\"===n||n===Pn?Ks:n===gn||n===bn||n===mn||n===kn?\"Console\":n===Zs?\"Wearable\":n?Gs:\"Desktop\"),$timezone:io(),$timezone_offset:eo()}),{$current_url:ki(null==a?void 0:a.href,o,Gn),$host:null==a?void 0:a.host,$pathname:null==a?void 0:a.pathname,$raw_user_agent:d.length>1e3?d.substring(0,997)+\"...\":d,$browser_version:zn(d,navigator.vendor),$browser_language:Xn(),$browser_language_prefix:(r=Xn(),\"string\"==typeof r?r.split(\"-\")[0]:void 0),$screen_height:null==t?void 0:t.screen.height,$screen_width:null==t?void 0:t.screen.width,$viewport_height:null==t?void 0:t.innerHeight,$viewport_width:null==t?void 0:t.innerWidth,$lib:\"web\",$lib_version:c.LIB_VERSION,$insert_id:Math.random().toString(36).substring(2,10)+Math.random().toString(36).substring(2,10),$time:Date.now()/1e3})}var so=z(\"[FeatureFlags]\"),no=\"$active_feature_flags\",oo=\"$override_feature_flags\",ao=\"$feature_flag_payloads\",lo=\"$override_feature_flag_payloads\",uo=\"$feature_flag_request_id\",ho=t=>{var i={};for(var[e,r]of Y(t||{}))r&&(i[e]=r);return i},vo=t=>{var i=t.flags;return i?(t.featureFlags=Object.fromEntries(Object.keys(i).map((t=>{var e;return[t,null!==(e=i[t].variant)&&void 0!==e?e:i[t].enabled]}))),t.featureFlagPayloads=Object.fromEntries(Object.keys(i).filter((t=>i[t].enabled)).filter((t=>{var e;return null==(e=i[t].metadata)?void 0:e.payload})).map((t=>{var e;return[t,null==(e=i[t].metadata)?void 0:e.payload]})))):so.warn(\"Using an older version of the feature flags endpoint. Please upgrade your PostHog server to the latest version\"),t},co=function(t){return t.FeatureFlags=\"feature_flags\",t.Recordings=\"recordings\",t}({});class fo{constructor(t){this.me=!1,this.be=!1,this.ye=!1,this.we=!1,this.Se=!1,this.$e=!1,this.xe=!1,this._instance=t,this.featureFlagEventHandlers=[]}flags(){if(this._instance.config.__preview_remote_config)this.$e=!0;else{var t=!this.ke&&(this._instance.config.advanced_disable_feature_flags||this._instance.config.advanced_disable_feature_flags_on_first_load);this.Ee({disableFlags:t})}}get hasLoadedFlags(){return this.be}getFlags(){return Object.keys(this.getFlagVariants())}getFlagsWithDetails(){var t=this._instance.get_property(Tt),i=this._instance.get_property(oo),e=this._instance.get_property(lo);if(!e&&!i)return t||{};var r=V({},t||{}),s=[...new Set([...Object.keys(e||{}),...Object.keys(i||{})])];for(var n of s){var o,a,l=r[n],u=null==i?void 0:i[n],h=R(u)?null!==(o=null==l?void 0:l.enabled)&&void 0!==o&&o:!!u,d=R(u)?l.variant:\"string\"==typeof u?u:void 0,v=null==e?void 0:e[n],c=B({},l,{enabled:h,variant:h?null!=d?d:null==l?void 0:l.variant:void 0});if(h!==(null==l?void 0:l.enabled)&&(c.original_enabled=null==l?void 0:l.enabled),d!==(null==l?void 0:l.variant)&&(c.original_variant=null==l?void 0:l.variant),v)c.metadata=B({},null==l?void 0:l.metadata,{payload:v,original_payload:null==l||null==(a=l.metadata)?void 0:a.payload});r[n]=c}return this.me||(so.warn(\" Overriding feature flag details!\",{flagDetails:t,overriddenPayloads:e,finalDetails:r}),this.me=!0),r}getFlagVariants(){var t=this._instance.get_property(Pt),i=this._instance.get_property(oo);if(!i)return t||{};for(var e=V({},t),r=Object.keys(i),s=0;s<r.length;s++)e[r[s]]=i[r[s]];return this.me||(so.warn(\" Overriding feature flags!\",{enabledFlags:t,overriddenFlags:i,finalFlags:e}),this.me=!0),e}getFlagPayloads(){var t=this._instance.get_property(ao),i=this._instance.get_property(lo);if(!i)return t||{};for(var e=V({},t||{}),r=Object.keys(i),s=0;s<r.length;s++)e[r[s]]=i[r[s]];return this.me||(so.warn(\" Overriding feature flag payloads!\",{flagPayloads:t,overriddenPayloads:i,finalPayloads:e}),this.me=!0),e}reloadFeatureFlags(){this.we||this._instance.config.advanced_disable_feature_flags||this.ke||(this.ke=setTimeout((()=>{this.Ee()}),5))}Ie(){clearTimeout(this.ke),this.ke=void 0}ensureFlagsLoaded(){this.be||this.ye||this.ke||this.reloadFeatureFlags()}setAnonymousDistinctId(t){this.$anon_distinct_id=t}setReloadingPaused(t){this.we=t}Ee(t){var i;if(this.Ie(),!this._instance.I())if(this.ye)this.Se=!0;else{var e={token:this._instance.config.token,distinct_id:this._instance.get_distinct_id(),groups:this._instance.getGroups(),$anon_distinct_id:this.$anon_distinct_id,person_properties:B({},(null==(i=this._instance.persistence)?void 0:i.get_initial_props())||{},this._instance.get_property(Mt)||{}),group_properties:this._instance.get_property(Ct)};(null!=t&&t.disableFlags||this._instance.config.advanced_disable_feature_flags)&&(e.disable_flags=!0);var r=this._instance.config.__preview_remote_config,s=r?\"/flags/?v=2\":\"/flags/?v=2&config=true\",n=this._instance.config.advanced_only_evaluate_survey_feature_flags?\"&only_evaluate_survey_feature_flags=true\":\"\",o=this._instance.requestRouter.endpointFor(\"api\",s+n);r&&(e.timezone=io()),this.ye=!0,this._instance.Pe({method:\"POST\",url:o,data:e,compression:this._instance.config.disable_compression?void 0:_.Base64,timeout:this._instance.config.feature_flag_request_timeout_ms,callback:t=>{var i,r,s=!0;(200===t.statusCode&&(this.Se||(this.$anon_distinct_id=void 0),s=!1),this.ye=!1,this.$e)||(this.$e=!0,this._instance.Re(null!==(r=t.json)&&void 0!==r?r:{}));if(!e.disable_flags||this.Se)if(this.xe=!s,t.json&&null!=(i=t.json.quotaLimited)&&i.includes(co.FeatureFlags))so.warn(\"You have hit your feature flags quota limit, and will not be able to load feature flags until the quota is reset.  Please visit https://posthog.com/docs/billing/limits-alerts to learn more.\");else{var n;if(!e.disable_flags)this.receivedFeatureFlags(null!==(n=t.json)&&void 0!==n?n:{},s);this.Se&&(this.Se=!1,this.Ee())}}})}}getFeatureFlag(t,i){if(void 0===i&&(i={}),this.be||this.getFlags()&&this.getFlags().length>0){var e=this.getFlagVariants()[t],r=\"\"+e,s=this._instance.get_property(uo)||void 0,n=this._instance.get_property(At)||{};if((i.send_event||!(\"send_event\"in i))&&(!(t in n)||!n[t].includes(r))){var o,a,l,u,h,d,v,c,f;k(n[t])?n[t].push(r):n[t]=[r],null==(o=this._instance.persistence)||o.register({[At]:n});var p=this.getFeatureFlagDetails(t),_={$feature_flag:t,$feature_flag_response:e,$feature_flag_payload:this.getFeatureFlagPayload(t)||null,$feature_flag_request_id:s,$feature_flag_bootstrapped_response:(null==(a=this._instance.config.bootstrap)||null==(a=a.featureFlags)?void 0:a[t])||null,$feature_flag_bootstrapped_payload:(null==(l=this._instance.config.bootstrap)||null==(l=l.featureFlagPayloads)?void 0:l[t])||null,$used_bootstrap_value:!this.xe};R(null==p||null==(u=p.metadata)?void 0:u.version)||(_.$feature_flag_version=p.metadata.version);var g,m=null!==(h=null==p||null==(d=p.reason)?void 0:d.description)&&void 0!==h?h:null==p||null==(v=p.reason)?void 0:v.code;if(m&&(_.$feature_flag_reason=m),null!=p&&null!=(c=p.metadata)&&c.id&&(_.$feature_flag_id=p.metadata.id),R(null==p?void 0:p.original_variant)&&R(null==p?void 0:p.original_enabled)||(_.$feature_flag_original_response=R(p.original_variant)?p.original_enabled:p.original_variant),null!=p&&null!=(f=p.metadata)&&f.original_payload)_.$feature_flag_original_payload=null==p||null==(g=p.metadata)?void 0:g.original_payload;this._instance.capture(\"$feature_flag_called\",_)}return e}so.warn('getFeatureFlag for key \"'+t+\"\\\" failed. Feature flags didn't load in time.\")}getFeatureFlagDetails(t){return this.getFlagsWithDetails()[t]}getFeatureFlagPayload(t){return this.getFlagPayloads()[t]}getRemoteConfigPayload(t,i){var e=this._instance.config.token;this._instance.Pe({method:\"POST\",url:this._instance.requestRouter.endpointFor(\"api\",\"/flags/?v=2&config=true\"),data:{distinct_id:this._instance.get_distinct_id(),token:e},compression:this._instance.config.disable_compression?void 0:_.Base64,timeout:this._instance.config.feature_flag_request_timeout_ms,callback:e=>{var r,s=null==(r=e.json)?void 0:r.featureFlagPayloads;i((null==s?void 0:s[t])||void 0)}})}isFeatureEnabled(t,i){if(void 0===i&&(i={}),this.be||this.getFlags()&&this.getFlags().length>0)return!!this.getFeatureFlag(t,i);so.warn('isFeatureEnabled for key \"'+t+\"\\\" failed. Feature flags didn't load in time.\")}addFeatureFlagsHandler(t){this.featureFlagEventHandlers.push(t)}removeFeatureFlagsHandler(t){this.featureFlagEventHandlers=this.featureFlagEventHandlers.filter((i=>i!==t))}receivedFeatureFlags(t,i){if(this._instance.persistence){this.be=!0;var e=this.getFlagVariants(),r=this.getFlagPayloads(),s=this.getFlagsWithDetails();!function(t,i,e,r,s){void 0===e&&(e={}),void 0===r&&(r={}),void 0===s&&(s={});var n=vo(t),o=n.flags,a=n.featureFlags,l=n.featureFlagPayloads;if(a){var u=t.requestId;if(k(a)){so.warn(\"v1 of the feature flags endpoint is deprecated. Please use the latest version.\");var h={};if(a)for(var d=0;d<a.length;d++)h[a[d]]=!0;i&&i.register({[no]:a,[Pt]:h})}else{var v=a,c=l,f=o;t.errorsWhileComputingFlags&&(v=B({},e,v),c=B({},r,c),f=B({},s,f)),i&&i.register(B({[no]:Object.keys(ho(v)),[Pt]:v||{},[ao]:c||{},[Tt]:f||{}},u?{[uo]:u}:{}))}}}(t,this._instance.persistence,e,r,s),this.Te(i)}}override(t,i){void 0===i&&(i=!1),so.warn(\"override is deprecated. Please use overrideFeatureFlags instead.\"),this.overrideFeatureFlags({flags:t,suppressWarning:i})}overrideFeatureFlags(t){if(!this._instance.__loaded||!this._instance.persistence)return so.uninitializedWarning(\"posthog.featureFlags.overrideFeatureFlags\");if(!1===t)return this._instance.persistence.unregister(oo),this._instance.persistence.unregister(lo),void this.Te();if(t&&\"object\"==typeof t&&(\"flags\"in t||\"payloads\"in t)){var i,e=t;if(this.me=Boolean(null!==(i=e.suppressWarning)&&void 0!==i&&i),\"flags\"in e)if(!1===e.flags)this._instance.persistence.unregister(oo);else if(e.flags)if(k(e.flags)){for(var r={},s=0;s<e.flags.length;s++)r[e.flags[s]]=!0;this._instance.persistence.register({[oo]:r})}else this._instance.persistence.register({[oo]:e.flags});return\"payloads\"in e&&(!1===e.payloads?this._instance.persistence.unregister(lo):e.payloads&&this._instance.persistence.register({[lo]:e.payloads})),void this.Te()}this.Te()}onFeatureFlags(t){if(this.addFeatureFlagsHandler(t),this.be){var{flags:i,flagVariants:e}=this.Me();t(i,e)}return()=>this.removeFeatureFlagsHandler(t)}updateEarlyAccessFeatureEnrollment(t,i,e){var r,s=(this._instance.get_property(Rt)||[]).find((i=>i.flagKey===t)),n={[\"$feature_enrollment/\"+t]:i},o={$feature_flag:t,$feature_enrollment:i,$set:n};s&&(o.$early_access_feature_name=s.name),e&&(o.$feature_enrollment_stage=e),this._instance.capture(\"$feature_enrollment_update\",o),this.setPersonPropertiesForFlags(n,!1);var a=B({},this.getFlagVariants(),{[t]:i});null==(r=this._instance.persistence)||r.register({[no]:Object.keys(ho(a)),[Pt]:a}),this.Te()}getEarlyAccessFeatures(t,i,e){void 0===i&&(i=!1);var r=this._instance.get_property(Rt),s=e?\"&\"+e.map((t=>\"stage=\"+t)).join(\"&\"):\"\";if(r&&!i)return t(r);this._instance.Pe({url:this._instance.requestRouter.endpointFor(\"api\",\"/api/early_access_features/?token=\"+this._instance.config.token+s),method:\"GET\",callback:i=>{var e,r;if(i.json){var s=i.json.earlyAccessFeatures;return null==(e=this._instance.persistence)||e.unregister(Rt),null==(r=this._instance.persistence)||r.register({[Rt]:s}),t(s)}}})}Me(){var t=this.getFlags(),i=this.getFlagVariants();return{flags:t.filter((t=>i[t])),flagVariants:Object.keys(i).filter((t=>i[t])).reduce(((t,e)=>(t[e]=i[e],t)),{})}}Te(t){var{flags:i,flagVariants:e}=this.Me();this.featureFlagEventHandlers.forEach((r=>r(i,e,{errorsLoading:t})))}setPersonPropertiesForFlags(t,i){void 0===i&&(i=!0);var e=this._instance.get_property(Mt)||{};this._instance.register({[Mt]:B({},e,t)}),i&&this._instance.reloadFeatureFlags()}resetPersonPropertiesForFlags(){this._instance.unregister(Mt)}setGroupPropertiesForFlags(t,i){void 0===i&&(i=!0);var e=this._instance.get_property(Ct)||{};0!==Object.keys(e).length&&Object.keys(e).forEach((i=>{e[i]=B({},e[i],t[i]),delete t[i]})),this._instance.register({[Ct]:B({},e,t)}),i&&this._instance.reloadFeatureFlags()}resetGroupPropertiesForFlags(t){if(t){var i=this._instance.get_property(Ct)||{};this._instance.register({[Ct]:B({},i,{[t]:{}})})}else this._instance.unregister(Ct)}reset(){this.be=!1,this.ye=!1,this.we=!1,this.Se=!1,this.$e=!1,this.xe=!1,this.$anon_distinct_id=void 0,this.Ie(),this.me=!1}}var po=[\"cookie\",\"localstorage\",\"localstorage+cookie\",\"sessionstorage\",\"memory\"];class _o{constructor(t,i){this.S=t,this.props={},this.Ce=!1,this.Fe=(t=>{var i=\"\";return t.token&&(i=t.token.replace(/\\+/g,\"PL\").replace(/\\//g,\"SL\").replace(/=/g,\"EQ\")),t.persistence_name?\"ph_\"+t.persistence_name:\"ph_\"+i+\"_posthog\"})(t),this.B=this.Oe(t),this.load(),t.debug&&N.info(\"Persistence loaded\",t.persistence,B({},this.props)),this.update_config(t,t,i),this.save()}isDisabled(){return!!this.Ae}Oe(t){-1===po.indexOf(t.persistence.toLowerCase())&&(N.critical(\"Unknown persistence type \"+t.persistence+\"; falling back to localStorage+cookie\"),t.persistence=\"localStorage+cookie\");var i=t.persistence.toLowerCase();return\"localstorage\"===i&&Gi.O()?Gi:\"localstorage+cookie\"===i&&Vi.O()?Vi:\"sessionstorage\"===i&&Qi.O()?Qi:\"memory\"===i?Yi:\"cookie\"===i?Hi:Vi.O()?Vi:Hi}properties(){var t={};return J(this.props,(function(i,e){if(e===Pt&&I(i))for(var r=Object.keys(i),n=0;n<r.length;n++)t[\"$feature/\"+r[n]]=i[r[n]];else a=e,l=!1,(C(o=Wt)?l:s&&o.indexOf===s?-1!=o.indexOf(a):(J(o,(function(t){if(l||(l=t===a))return W})),l))||(t[e]=i);var o,a,l})),t}load(){if(!this.Ae){var t=this.B.L(this.Fe);t&&(this.props=V({},t))}}save(){this.Ae||this.B.j(this.Fe,this.props,this.De,this.Le,this.je,this.S.debug)}remove(){this.B.N(this.Fe,!1),this.B.N(this.Fe,!0)}clear(){this.remove(),this.props={}}register_once(t,i,e){if(I(t)){R(i)&&(i=\"None\"),this.De=R(e)?this.Ne:e;var r=!1;if(J(t,((t,e)=>{this.props.hasOwnProperty(e)&&this.props[e]!==i||(this.props[e]=t,r=!0)})),r)return this.save(),!0}return!1}register(t,i){if(I(t)){this.De=R(i)?this.Ne:i;var e=!1;if(J(t,((i,r)=>{t.hasOwnProperty(r)&&this.props[r]!==i&&(this.props[r]=i,e=!0)})),e)return this.save(),!0}return!1}unregister(t){t in this.props&&(delete this.props[t],this.save())}update_campaign_params(){if(!this.Ce){var t=Vn(this.S.custom_campaign_params,this.S.mask_personal_data_properties,this.S.custom_personal_data_properties);P(Z(t))||this.register(t),this.Ce=!0}}update_search_keyword(){var t;this.register((t=null==o?void 0:o.referrer)?Yn(t):{})}update_referrer_info(){var t;this.register_once({$referrer:Qn(),$referring_domain:null!=o&&o.referrer&&(null==(t=Si(o.referrer))?void 0:t.host)||\"$direct\"},void 0)}set_initial_person_info(){this.props[Nt]||this.props[zt]||this.register_once({[Ut]:Zn(this.S.mask_personal_data_properties,this.S.custom_personal_data_properties)},void 0)}get_initial_props(){var t={};J([zt,Nt],(i=>{var e=this.props[i];e&&J(e,(function(i,e){t[\"$initial_\"+y(e)]=i}))}));var i,e,r=this.props[Ut];if(r){var s=(i=to(r),e={},J(i,(function(t,i){e[\"$initial_\"+y(i)]=t})),e);V(t,s)}return t}safe_merge(t){return J(this.props,(function(i,e){e in t||(t[e]=i)})),t}update_config(t,i,e){if(this.Ne=this.De=t.cookie_expiration,this.set_disabled(t.disable_persistence||!!e),this.set_cross_subdomain(t.cross_subdomain_cookie),this.set_secure(t.secure_cookie),t.persistence!==i.persistence){var r=this.Oe(t),s=this.props;this.clear(),this.B=r,this.props=s,this.save()}}set_disabled(t){this.Ae=t,this.Ae?this.remove():this.save()}set_cross_subdomain(t){t!==this.Le&&(this.Le=t,this.remove(),this.save())}set_secure(t){t!==this.je&&(this.je=t,this.remove(),this.save())}set_event_timer(t,i){var e=this.props[at]||{};e[t]=i,this.props[at]=e,this.save()}remove_event_timer(t){var i=(this.props[at]||{})[t];return R(i)||(delete this.props[at][t],this.save()),i}get_property(t){return this.props[t]}set_property(t,i){this.props[t]=i,this.save()}}class go{constructor(){this.ze={},this.ze={}}on(t,i){return this.ze[t]||(this.ze[t]=[]),this.ze[t].push(i),()=>{this.ze[t]=this.ze[t].filter((t=>t!==i))}}emit(t,i){for(var e of this.ze[t]||[])e(i);for(var r of this.ze[\"*\"]||[])r(t,i)}}class mo{constructor(t){this.Ue=new go,this.qe=(t,i)=>this.Be(t,i)&&this.He(t,i)&&this.We(t,i),this.Be=(t,i)=>null==i||!i.event||(null==t?void 0:t.event)===(null==i?void 0:i.event),this._instance=t,this.Ge=new Set,this.Je=new Set}init(){var t;if(!R(null==(t=this._instance)?void 0:t.Ve)){var i;null==(i=this._instance)||i.Ve(((t,i)=>{this.on(t,i)}))}}register(t){var i,e;if(!R(null==(i=this._instance)?void 0:i.Ve)&&(t.forEach((t=>{var i,e;null==(i=this.Je)||i.add(t),null==(e=t.steps)||e.forEach((t=>{var i;null==(i=this.Ge)||i.add((null==t?void 0:t.event)||\"\")}))})),null!=(e=this._instance)&&e.autocapture)){var r,s=new Set;t.forEach((t=>{var i;null==(i=t.steps)||i.forEach((t=>{null!=t&&t.selector&&s.add(null==t?void 0:t.selector)}))})),null==(r=this._instance)||r.autocapture.setElementSelectors(s)}}on(t,i){var e;null!=i&&0!=t.length&&(this.Ge.has(t)||this.Ge.has(null==i?void 0:i.event))&&this.Je&&(null==(e=this.Je)?void 0:e.size)>0&&this.Je.forEach((t=>{this.Ke(i,t)&&this.Ue.emit(\"actionCaptured\",t.name)}))}Ye(t){this.onAction(\"actionCaptured\",(i=>t(i)))}Ke(t,i){if(null==(null==i?void 0:i.steps))return!1;for(var e of i.steps)if(this.qe(t,e))return!0;return!1}onAction(t,i){return this.Ue.on(t,i)}He(t,i){if(null!=i&&i.url){var e,r=null==t||null==(e=t.properties)?void 0:e.$current_url;if(!r||\"string\"!=typeof r)return!1;if(!mo.Xe(r,null==i?void 0:i.url,(null==i?void 0:i.url_matching)||\"contains\"))return!1}return!0}static Xe(i,e,r){switch(r){case\"regex\":return!!t&&zs(i,e);case\"exact\":return e===i;case\"contains\":var s=mo.Qe(e).replace(/_/g,\".\").replace(/%/g,\".*\");return zs(i,s);default:return!1}}static Qe(t){return t.replace(/[|\\\\{}()[\\]^$+*?.]/g,\"\\\\$&\").replace(/-/g,\"\\\\x2d\")}We(t,i){if((null!=i&&i.href||null!=i&&i.tag_name||null!=i&&i.text)&&!this.Ze(t).some((t=>!(null!=i&&i.href&&!mo.Xe(t.href||\"\",null==i?void 0:i.href,(null==i?void 0:i.href_matching)||\"exact\"))&&((null==i||!i.tag_name||t.tag_name===(null==i?void 0:i.tag_name))&&!(null!=i&&i.text&&!mo.Xe(t.text||\"\",null==i?void 0:i.text,(null==i?void 0:i.text_matching)||\"exact\")&&!mo.Xe(t.$el_text||\"\",null==i?void 0:i.text,(null==i?void 0:i.text_matching)||\"exact\"))))))return!1;if(null!=i&&i.selector){var e,r=null==t||null==(e=t.properties)?void 0:e.$element_selectors;if(!r)return!1;if(!r.includes(null==i?void 0:i.selector))return!1}return!0}Ze(t){return null==(null==t?void 0:t.properties.$elements)?[]:null==t?void 0:t.properties.$elements}}var bo=function(t){return t.Button=\"button\",t.Tab=\"tab\",t.Selector=\"selector\",t}({}),yo=function(t){return t.TopLeft=\"top_left\",t.TopRight=\"top_right\",t.TopCenter=\"top_center\",t.MiddleLeft=\"middle_left\",t.MiddleRight=\"middle_right\",t.MiddleCenter=\"middle_center\",t.Left=\"left\",t.Center=\"center\",t.Right=\"right\",t.NextToTrigger=\"next_to_trigger\",t}({}),wo=function(t){return t.Popover=\"popover\",t.API=\"api\",t.Widget=\"widget\",t.ExternalSurvey=\"external_survey\",t}({}),So=function(t){return t.Open=\"open\",t.MultipleChoice=\"multiple_choice\",t.SingleChoice=\"single_choice\",t.Rating=\"rating\",t.Link=\"link\",t}({}),$o=function(t){return t.NextQuestion=\"next_question\",t.End=\"end\",t.ResponseBased=\"response_based\",t.SpecificQuestion=\"specific_question\",t}({}),xo=function(t){return t.Once=\"once\",t.Recurring=\"recurring\",t.Always=\"always\",t}({}),ko=function(t){return t.SHOWN=\"survey shown\",t.DISMISSED=\"survey dismissed\",t.SENT=\"survey sent\",t}({}),Eo=function(t){return t.SURVEY_ID=\"$survey_id\",t.SURVEY_NAME=\"$survey_name\",t.SURVEY_RESPONSE=\"$survey_response\",t.SURVEY_ITERATION=\"$survey_iteration\",t.SURVEY_ITERATION_START_DATE=\"$survey_iteration_start_date\",t.SURVEY_PARTIALLY_COMPLETED=\"$survey_partially_completed\",t.SURVEY_SUBMISSION_ID=\"$survey_submission_id\",t.SURVEY_QUESTIONS=\"$survey_questions\",t.SURVEY_COMPLETED=\"$survey_completed\",t}({}),Io=z(\"[Surveys]\");var Po=\"seenSurvey_\",Ro=(t,i)=>{var e=\"$survey_\"+i+\"/\"+t.id;return t.current_iteration&&t.current_iteration>0&&(e=\"$survey_\"+i+\"/\"+t.id+\"/\"+t.current_iteration),e},To=[wo.Popover,wo.Widget,wo.API];class Mo{constructor(t){this._instance=t,this.tr=new Map,this.ir=new Map}register(t){var i;R(null==(i=this._instance)?void 0:i.Ve)||(this.er(t),this.rr(t))}rr(t){var i=t.filter((t=>{var i,e;return(null==(i=t.conditions)?void 0:i.actions)&&(null==(e=t.conditions)||null==(e=e.actions)||null==(e=e.values)?void 0:e.length)>0}));if(0!==i.length){if(null==this.sr){this.sr=new mo(this._instance),this.sr.init();this.sr.Ye((t=>{this.onAction(t)}))}i.forEach((t=>{var i,e,r,s,n;t.conditions&&null!=(i=t.conditions)&&i.actions&&null!=(e=t.conditions)&&null!=(e=e.actions)&&e.values&&(null==(r=t.conditions)||null==(r=r.actions)||null==(r=r.values)?void 0:r.length)>0&&(null==(s=this.sr)||s.register(t.conditions.actions.values),null==(n=t.conditions)||null==(n=n.actions)||null==(n=n.values)||n.forEach((i=>{if(i&&i.name){var e=this.ir.get(i.name);e&&e.push(t.id),this.ir.set(i.name,e||[t.id])}})))}))}}er(t){var i;if(0!==t.filter((t=>{var i,e;return(null==(i=t.conditions)?void 0:i.events)&&(null==(e=t.conditions)||null==(e=e.events)||null==(e=e.values)?void 0:e.length)>0})).length){null==(i=this._instance)||i.Ve(((t,i)=>{this.onEvent(t,i)})),t.forEach((t=>{var i;null==(i=t.conditions)||null==(i=i.events)||null==(i=i.values)||i.forEach((i=>{if(i&&i.name){var e=this.tr.get(i.name);e&&e.push(t.id),this.tr.set(i.name,e||[t.id])}}))}))}}onEvent(t,i){var e,r=(null==(e=this._instance)||null==(e=e.persistence)?void 0:e.props[Ot])||[];if(\"survey shown\"===t&&i&&r.length>0){var s;Io.info(\"survey event matched, removing survey from activated surveys\",{event:t,eventPayload:i,existingActivatedSurveys:r});var n=null==i||null==(s=i.properties)?void 0:s.$survey_id;if(n){var o=r.indexOf(n);o>=0&&(r.splice(o,1),this.nr(r))}}else this.tr.has(t)&&(Io.info(\"survey event matched, updating activated surveys\",{event:t,surveys:this.tr.get(t)}),this.nr(r.concat(this.tr.get(t)||[])))}onAction(t){var i,e=(null==(i=this._instance)||null==(i=i.persistence)?void 0:i.props[Ot])||[];this.ir.has(t)&&this.nr(e.concat(this.ir.get(t)||[]))}nr(t){var i;null==(i=this._instance)||null==(i=i.persistence)||i.register({[Ot]:[...new Set(t)]})}getSurveys(){var t,i=null==(t=this._instance)||null==(t=t.persistence)?void 0:t.props[Ot];return i||[]}getEventToSurveys(){return this.tr}ar(){return this.sr}}class Co{constructor(t){this.lr=void 0,this._surveyManager=null,this.ur=!1,this.hr=!1,this.dr=[],this._instance=t,this._surveyEventReceiver=null}onRemoteConfig(t){var i=t.surveys;if(F(i))return Io.warn(\"Flags not loaded yet. Not loading surveys.\");var e=k(i);this.lr=e?i.length>0:i,Io.info(\"flags response received, isSurveysEnabled: \"+this.lr),this.loadIfEnabled()}reset(){localStorage.removeItem(\"lastSeenSurveyDate\");for(var t=[],i=0;i<localStorage.length;i++){var e=localStorage.key(i);(null!=e&&e.startsWith(Po)||null!=e&&e.startsWith(\"inProgressSurvey_\"))&&t.push(e)}t.forEach((t=>localStorage.removeItem(t)))}loadIfEnabled(){if(!this._surveyManager)if(this.hr)Io.info(\"Already initializing surveys, skipping...\");else if(this._instance.config.disable_surveys)Io.info(\"Disabled. Not loading surveys.\");else{var t=null==v?void 0:v.__PosthogExtensions__;if(t){if(!R(this.lr)||this._instance.config.advanced_enable_surveys){var i=this.lr||this._instance.config.advanced_enable_surveys;this.hr=!0;try{var e=t.generateSurveys;if(e)return void this.vr(e,i);var r=t.loadExternalDependency;if(!r)return void this.cr(\"PostHog loadExternalDependency extension not found.\");r(this._instance,\"surveys\",(e=>{e||!t.generateSurveys?this.cr(\"Could not load surveys script\",e):this.vr(t.generateSurveys,i)}))}catch(t){throw this.cr(\"Error initializing surveys\",t),t}finally{this.hr=!1}}}else Io.error(\"PostHog Extensions not found.\")}}vr(t,i){this._surveyManager=t(this._instance,i),this._surveyEventReceiver=new Mo(this._instance),Io.info(\"Surveys loaded successfully\"),this.pr({isLoaded:!0})}cr(t,i){Io.error(t,i),this.pr({isLoaded:!1,error:t})}onSurveysLoaded(t){return this.dr.push(t),this._surveyManager&&this.pr({isLoaded:!0}),()=>{this.dr=this.dr.filter((i=>i!==t))}}getSurveys(t,i){if(void 0===i&&(i=!1),this._instance.config.disable_surveys)return Io.info(\"Disabled. Not loading surveys.\"),t([]);var e=this._instance.get_property(Ft);if(e&&!i)return t(e,{isLoaded:!0});if(this.ur)return t([],{isLoaded:!1,error:\"Surveys are already being loaded\"});try{this.ur=!0,this._instance.Pe({url:this._instance.requestRouter.endpointFor(\"api\",\"/api/surveys/?token=\"+this._instance.config.token),method:\"GET\",timeout:this._instance.config.surveys_request_timeout_ms,callback:i=>{var e;this.ur=!1;var r=i.statusCode;if(200!==r||!i.json){var s=\"Surveys API could not be loaded, status: \"+r;return Io.error(s),t([],{isLoaded:!1,error:s})}var n,o=i.json.surveys||[],a=o.filter((t=>function(t){return!(!t.start_date||t.end_date)}(t)&&(function(t){var i;return!(null==(i=t.conditions)||null==(i=i.events)||null==(i=i.values)||!i.length)}(t)||function(t){var i;return!(null==(i=t.conditions)||null==(i=i.actions)||null==(i=i.values)||!i.length)}(t))));a.length>0&&(null==(n=this._surveyEventReceiver)||n.register(a));return null==(e=this._instance.persistence)||e.register({[Ft]:o}),t(o,{isLoaded:!0})}})}catch(t){throw this.ur=!1,t}}pr(t){for(var i of this.dr)try{if(!t.isLoaded)return i([],t);this.getSurveys(i)}catch(t){Io.error(\"Error in survey callback\",t)}}getActiveMatchingSurveys(t,i){if(void 0===i&&(i=!1),!F(this._surveyManager))return this._surveyManager.getActiveMatchingSurveys(t,i);Io.warn(\"init was not called\")}_r(t){var i=null;return this.getSurveys((e=>{var r;i=null!==(r=e.find((i=>i.id===t)))&&void 0!==r?r:null})),i}gr(t){if(F(this._surveyManager))return{eligible:!1,reason:\"SDK is not enabled or survey functionality is not yet loaded\"};var i=\"string\"==typeof t?this._r(t):t;return i?this._surveyManager.checkSurveyEligibility(i):{eligible:!1,reason:\"Survey not found\"}}canRenderSurvey(t){if(F(this._surveyManager))return Io.warn(\"init was not called\"),{visible:!1,disabledReason:\"SDK is not enabled or survey functionality is not yet loaded\"};var i=this.gr(t);return{visible:i.eligible,disabledReason:i.reason}}canRenderSurveyAsync(t,i){return F(this._surveyManager)?(Io.warn(\"init was not called\"),Promise.resolve({visible:!1,disabledReason:\"SDK is not enabled or survey functionality is not yet loaded\"})):new Promise((e=>{this.getSurveys((i=>{var r,s=null!==(r=i.find((i=>i.id===t)))&&void 0!==r?r:null;if(s){var n=this.gr(s);e({visible:n.eligible,disabledReason:n.reason})}else e({visible:!1,disabledReason:\"Survey not found\"})}),i)}))}renderSurvey(t,i){if(F(this._surveyManager))Io.warn(\"init was not called\");else{var e=this._r(t);if(e)if(To.includes(e.type)){var r=null==o?void 0:o.querySelector(i);r?this._surveyManager.renderSurvey(e,r):Io.warn(\"Survey element not found\")}else Io.warn(\"Surveys of type \"+e.type+\" cannot be rendered in the app\");else Io.warn(\"Survey not found\")}}}var Fo=z(\"[RateLimiter]\");class Oo{constructor(t){var i,e;this.serverLimits={},this.lastEventRateLimited=!1,this.checkForLimiting=t=>{var i=t.text;if(i&&i.length)try{(JSON.parse(i).quota_limited||[]).forEach((t=>{Fo.info((t||\"events\")+\" is quota limited.\"),this.serverLimits[t]=(new Date).getTime()+6e4}))}catch(t){return void Fo.warn('could not rate limit - continuing. Error: \"'+(null==t?void 0:t.message)+'\"',{text:i})}},this.instance=t,this.captureEventsPerSecond=(null==(i=t.config.rate_limiting)?void 0:i.events_per_second)||10,this.captureEventsBurstLimit=Math.max((null==(e=t.config.rate_limiting)?void 0:e.events_burst_limit)||10*this.captureEventsPerSecond,this.captureEventsPerSecond),this.lastEventRateLimited=this.clientRateLimitContext(!0).isRateLimited}clientRateLimitContext(t){var i,e,r;void 0===t&&(t=!1);var s=(new Date).getTime(),n=null!==(i=null==(e=this.instance.persistence)?void 0:e.get_property(jt))&&void 0!==i?i:{tokens:this.captureEventsBurstLimit,last:s};n.tokens+=(s-n.last)/1e3*this.captureEventsPerSecond,n.last=s,n.tokens>this.captureEventsBurstLimit&&(n.tokens=this.captureEventsBurstLimit);var o=n.tokens<1;return o||t||(n.tokens=Math.max(0,n.tokens-1)),!o||this.lastEventRateLimited||t||this.instance.capture(\"$$client_ingestion_warning\",{$$client_ingestion_warning_message:\"posthog-js client rate limited. Config is set to \"+this.captureEventsPerSecond+\" events per second and \"+this.captureEventsBurstLimit+\" events burst limit.\"},{skip_client_rate_limiting:!0}),this.lastEventRateLimited=o,null==(r=this.instance.persistence)||r.set_property(jt,n),{isRateLimited:o,remainingTokens:n.tokens}}isServerRateLimited(t){var i=this.serverLimits[t||\"events\"]||!1;return!1!==i&&(new Date).getTime()<i}}var Ao=z(\"[RemoteConfig]\");class Do{constructor(t){this._instance=t}get remoteConfig(){var t;return null==(t=v._POSTHOG_REMOTE_CONFIG)||null==(t=t[this._instance.config.token])?void 0:t.config}mr(t){var i,e;null!=(i=v.__PosthogExtensions__)&&i.loadExternalDependency?null==(e=v.__PosthogExtensions__)||null==e.loadExternalDependency||e.loadExternalDependency(this._instance,\"remote-config\",(()=>t(this.remoteConfig))):(Ao.error(\"PostHog Extensions not found. Cannot load remote config.\"),t())}br(t){this._instance.Pe({method:\"GET\",url:this._instance.requestRouter.endpointFor(\"assets\",\"/array/\"+this._instance.config.token+\"/config\"),callback:i=>{t(i.json)}})}load(){try{if(this.remoteConfig)return Ao.info(\"Using preloaded remote config\",this.remoteConfig),void this.Re(this.remoteConfig);if(this._instance.I())return void Ao.warn(\"Remote config is disabled. Falling back to local config.\");this.mr((t=>{if(!t)return Ao.info(\"No config found after loading remote JS config. Falling back to JSON.\"),void this.br((t=>{this.Re(t)}));this.Re(t)}))}catch(t){Ao.error(\"Error loading remote config\",t)}}Re(t){t?this._instance.config.__preview_remote_config?(this._instance.Re(t),!1!==t.hasFeatureFlags&&this._instance.featureFlags.ensureFlagsLoaded()):Ao.info(\"__preview_remote_config is disabled. Logging config instead\",t):Ao.error(\"Failed to fetch remote config from PostHog.\")}}var Lo=3e3;class jo{constructor(t,i){this.yr=!0,this.wr=[],this.Sr=ne((null==i?void 0:i.flush_interval_ms)||Lo,250,5e3,\"flush interval\",Lo),this.$r=t}enqueue(t){this.wr.push(t),this.kr||this.Er()}unload(){this.Ir();var t=this.wr.length>0?this.Pr():{},i=Object.values(t);[...i.filter((t=>0===t.url.indexOf(\"/e\"))),...i.filter((t=>0!==t.url.indexOf(\"/e\")))].map((t=>{this.$r(B({},t,{transport:\"sendBeacon\"}))}))}enable(){this.yr=!1,this.Er()}Er(){var t=this;this.yr||(this.kr=setTimeout((()=>{if(this.Ir(),this.wr.length>0){var i=this.Pr(),e=function(){var e=i[r],s=(new Date).getTime();e.data&&k(e.data)&&J(e.data,(t=>{t.offset=Math.abs(t.timestamp-s),delete t.timestamp})),t.$r(e)};for(var r in i)e()}}),this.Sr))}Ir(){clearTimeout(this.kr),this.kr=void 0}Pr(){var t={};return J(this.wr,(i=>{var e,r=i,s=(r?r.batchKey:null)||r.url;R(t[s])&&(t[s]=B({},r,{data:[]})),null==(e=t[s].data)||e.push(r.data)})),this.wr=[],t}}var No=[\"retriesPerformedSoFar\"];class zo{constructor(i){this.Rr=!1,this.Tr=3e3,this.wr=[],this._instance=i,this.wr=[],this.Mr=!0,!R(t)&&\"onLine\"in t.navigator&&(this.Mr=t.navigator.onLine,st(t,\"online\",(()=>{this.Mr=!0,this.se()})),st(t,\"offline\",(()=>{this.Mr=!1})))}get length(){return this.wr.length}retriableRequest(t){var{retriesPerformedSoFar:i}=t,e=H(t,No);O(i)&&i>0&&(e.url=Ds(e.url,{retry_count:i})),this._instance.Pe(B({},e,{callback:t=>{200!==t.statusCode&&(t.statusCode<400||t.statusCode>=500)&&(null!=i?i:0)<10?this.Cr(B({retriesPerformedSoFar:i},e)):null==e.callback||e.callback(t)}}))}Cr(t){var i=t.retriesPerformedSoFar||0;t.retriesPerformedSoFar=i+1;var e=function(t){var i=3e3*Math.pow(2,t),e=i/2,r=Math.min(18e5,i),s=(Math.random()-.5)*(r-e);return Math.ceil(r+s)}(i),r=Date.now()+e;this.wr.push({retryAt:r,requestOptions:t});var s=\"Enqueued failed request for retry in \"+e;navigator.onLine||(s+=\" (Browser is offline)\"),N.warn(s),this.Rr||(this.Rr=!0,this.Fr())}Fr(){this.Or&&clearTimeout(this.Or),this.Or=setTimeout((()=>{this.Mr&&this.wr.length>0&&this.se(),this.Fr()}),this.Tr)}se(){var t=Date.now(),i=[],e=this.wr.filter((e=>e.retryAt<t||(i.push(e),!1)));if(this.wr=i,e.length>0)for(var{requestOptions:r}of e)this.retriableRequest(r)}unload(){for(var{requestOptions:t}of(this.Or&&(clearTimeout(this.Or),this.Or=void 0),this.wr))try{this._instance.Pe(B({},t,{transport:\"sendBeacon\"}))}catch(t){N.error(t)}this.wr=[]}}class Uo{constructor(t){this.Ar=()=>{var t,i,e,r;this.Dr||(this.Dr={});var s=this.scrollElement(),n=this.scrollY(),o=s?Math.max(0,s.scrollHeight-s.clientHeight):0,a=n+((null==s?void 0:s.clientHeight)||0),l=(null==s?void 0:s.scrollHeight)||0;this.Dr.lastScrollY=Math.ceil(n),this.Dr.maxScrollY=Math.max(n,null!==(t=this.Dr.maxScrollY)&&void 0!==t?t:0),this.Dr.maxScrollHeight=Math.max(o,null!==(i=this.Dr.maxScrollHeight)&&void 0!==i?i:0),this.Dr.lastContentY=a,this.Dr.maxContentY=Math.max(a,null!==(e=this.Dr.maxContentY)&&void 0!==e?e:0),this.Dr.maxContentHeight=Math.max(l,null!==(r=this.Dr.maxContentHeight)&&void 0!==r?r:0)},this._instance=t}getContext(){return this.Dr}resetContext(){var t=this.Dr;return setTimeout(this.Ar,0),t}startMeasuringScrollPosition(){st(t,\"scroll\",this.Ar,{capture:!0}),st(t,\"scrollend\",this.Ar,{capture:!0}),st(t,\"resize\",this.Ar)}scrollElement(){if(!this._instance.config.scroll_root_selector)return null==t?void 0:t.document.documentElement;var i=k(this._instance.config.scroll_root_selector)?this._instance.config.scroll_root_selector:[this._instance.config.scroll_root_selector];for(var e of i){var r=null==t?void 0:t.document.querySelector(e);if(r)return r}}scrollY(){if(this._instance.config.scroll_root_selector){var i=this.scrollElement();return i&&i.scrollTop||0}return t&&(t.scrollY||t.pageYOffset||t.document.documentElement.scrollTop)||0}scrollX(){if(this._instance.config.scroll_root_selector){var i=this.scrollElement();return i&&i.scrollLeft||0}return t&&(t.scrollX||t.pageXOffset||t.document.documentElement.scrollLeft)||0}}var qo=t=>Zn(null==t?void 0:t.config.mask_personal_data_properties,null==t?void 0:t.config.custom_personal_data_properties);class Bo{constructor(t,i,e,r){this.Lr=t=>{var i=this.jr();if(!i||i.sessionId!==t){var e={sessionId:t,props:this.Nr(this._instance)};this.zr.register({[Lt]:e})}},this._instance=t,this.Ur=i,this.zr=e,this.Nr=r||qo,this.Ur.onSessionId(this.Lr)}jr(){return this.zr.props[Lt]}getSetOnceProps(){var t,i=null==(t=this.jr())?void 0:t.props;return i?\"r\"in i?to(i):{$referring_domain:i.referringDomain,$pathname:i.initialPathName,utm_source:i.utm_source,utm_campaign:i.utm_campaign,utm_medium:i.utm_medium,utm_content:i.utm_content,utm_term:i.utm_term}:{}}getSessionProps(){var t={};return J(Z(this.getSetOnceProps()),((i,e)=>{\"$current_url\"===e&&(e=\"url\"),t[\"$session_entry_\"+y(e)]=i})),t}}var Ho=z(\"[SessionId]\");class Wo{constructor(t,i,e){var r;if(this.qr=[],this.Br=(t,i)=>Math.abs(t-i)>this.sessionTimeoutMs,!t.persistence)throw new Error(\"SessionIdManager requires a PostHogPersistence instance\");if(t.config.__preview_experimental_cookieless_mode)throw new Error(\"SessionIdManager cannot be used with __preview_experimental_cookieless_mode\");this.S=t.config,this.zr=t.persistence,this.fi=void 0,this.Ct=void 0,this._sessionStartTimestamp=null,this._sessionActivityTimestamp=null,this.Hr=i||Ni,this.Wr=e||Ni;var s=this.S.persistence_name||this.S.token,n=this.S.session_idle_timeout_seconds||1800;if(this._sessionTimeoutMs=1e3*ne(n,60,36e3,\"session_idle_timeout_seconds\",1800),t.register({$configured_session_timeout_ms:this._sessionTimeoutMs}),this.Gr(),this.Jr=\"ph_\"+s+\"_window_id\",this.Vr=\"ph_\"+s+\"_primary_window_exists\",this.Kr()){var o=Qi.L(this.Jr),a=Qi.L(this.Vr);o&&!a?this.fi=o:Qi.N(this.Jr),Qi.j(this.Vr,!0)}if(null!=(r=this.S.bootstrap)&&r.sessionID)try{var l=(t=>{var i=t.replace(/-/g,\"\");if(32!==i.length)throw new Error(\"Not a valid UUID\");if(\"7\"!==i[12])throw new Error(\"Not a UUIDv7\");return parseInt(i.substring(0,12),16)})(this.S.bootstrap.sessionID);this.Yr(this.S.bootstrap.sessionID,(new Date).getTime(),l)}catch(t){Ho.error(\"Invalid sessionID in bootstrap\",t)}this.Xr()}get sessionTimeoutMs(){return this._sessionTimeoutMs}onSessionId(t){return R(this.qr)&&(this.qr=[]),this.qr.push(t),this.Ct&&t(this.Ct,this.fi),()=>{this.qr=this.qr.filter((i=>i!==t))}}Kr(){return\"memory\"!==this.S.persistence&&!this.zr.Ae&&Qi.O()}Qr(t){t!==this.fi&&(this.fi=t,this.Kr()&&Qi.j(this.Jr,t))}Zr(){return this.fi?this.fi:this.Kr()?Qi.L(this.Jr):null}Yr(t,i,e){t===this.Ct&&i===this._sessionActivityTimestamp&&e===this._sessionStartTimestamp||(this._sessionStartTimestamp=e,this._sessionActivityTimestamp=i,this.Ct=t,this.zr.register({[xt]:[i,t,e]}))}ts(){if(this.Ct&&this._sessionActivityTimestamp&&this._sessionStartTimestamp)return[this._sessionActivityTimestamp,this.Ct,this._sessionStartTimestamp];var t=this.zr.props[xt];return k(t)&&2===t.length&&t.push(t[0]),t||[0,null,0]}resetSessionId(){this.Yr(null,null,null)}Xr(){st(t,\"beforeunload\",(()=>{this.Kr()&&Qi.N(this.Vr)}),{capture:!1})}checkAndGetSessionAndWindowId(t,i){if(void 0===t&&(t=!1),void 0===i&&(i=null),this.S.__preview_experimental_cookieless_mode)throw new Error(\"checkAndGetSessionAndWindowId should not be called in __preview_experimental_cookieless_mode\");var e=i||(new Date).getTime(),[r,s,n]=this.ts(),o=this.Zr(),a=O(n)&&n>0&&Math.abs(e-n)>864e5,l=!1,u=!s,h=!t&&this.Br(e,r);u||h||a?(s=this.Hr(),o=this.Wr(),Ho.info(\"new session ID generated\",{sessionId:s,windowId:o,changeReason:{noSessionId:u,activityTimeout:h,sessionPastMaximumLength:a}}),n=e,l=!0):o||(o=this.Wr(),l=!0);var d=0===r||!t||a?e:r,v=0===n?(new Date).getTime():n;return this.Qr(o),this.Yr(s,d,v),t||this.Gr(),l&&this.qr.forEach((t=>t(s,o,l?{noSessionId:u,activityTimeout:h,sessionPastMaximumLength:a}:void 0))),{sessionId:s,windowId:o,sessionStartTimestamp:v,changeReason:l?{noSessionId:u,activityTimeout:h,sessionPastMaximumLength:a}:void 0,lastActivityTimestamp:r}}Gr(){clearTimeout(this.es),this.es=setTimeout((()=>{var[t]=this.ts();this.Br((new Date).getTime(),t)&&this.resetSessionId()}),1.1*this.sessionTimeoutMs)}}var Go=[\"$set_once\",\"$set\"],Jo=z(\"[SiteApps]\");class Vo{constructor(t){this._instance=t,this.rs=[],this.apps={}}get isEnabled(){return!!this._instance.config.opt_in_site_apps}ss(t,i){if(i){var e=this.globalsForEvent(i);this.rs.push(e),this.rs.length>1e3&&(this.rs=this.rs.slice(10))}}get siteAppLoaders(){var t;return null==(t=v._POSTHOG_REMOTE_CONFIG)||null==(t=t[this._instance.config.token])?void 0:t.siteApps}init(){if(this.isEnabled){var t=this._instance.Ve(this.ss.bind(this));this.ns=()=>{t(),this.rs=[],this.ns=void 0}}}globalsForEvent(t){var i,e,r,s,n,o,a;if(!t)throw new Error(\"Event payload is required\");var l={},u=this._instance.get_property(\"$groups\")||[],h=this._instance.get_property(\"$stored_group_properties\")||{};for(var[d,v]of Object.entries(h))l[d]={id:u[d],type:d,properties:v};var{$set_once:c,$set:f}=t;return{event:B({},H(t,Go),{properties:B({},t.properties,f?{$set:B({},null!==(i=null==(e=t.properties)?void 0:e.$set)&&void 0!==i?i:{},f)}:{},c?{$set_once:B({},null!==(r=null==(s=t.properties)?void 0:s.$set_once)&&void 0!==r?r:{},c)}:{}),elements_chain:null!==(n=null==(o=t.properties)?void 0:o.$elements_chain)&&void 0!==n?n:\"\",distinct_id:null==(a=t.properties)?void 0:a.distinct_id}),person:{properties:this._instance.get_property(\"$stored_person_properties\")},groups:l}}setupSiteApp(t){var i=this.apps[t.id],e=()=>{var e;(!i.errored&&this.rs.length&&(Jo.info(\"Processing \"+this.rs.length+\" events for site app with id \"+t.id),this.rs.forEach((t=>null==i.processEvent?void 0:i.processEvent(t))),i.processedBuffer=!0),Object.values(this.apps).every((t=>t.processedBuffer||t.errored)))&&(null==(e=this.ns)||e.call(this))},r=!1,s=s=>{i.errored=!s,i.loaded=!0,Jo.info(\"Site app with id \"+t.id+\" \"+(s?\"loaded\":\"errored\")),r&&e()};try{var{processEvent:n}=t.init({posthog:this._instance,callback:t=>{s(t)}});n&&(i.processEvent=n),r=!0}catch(i){Jo.error(\"Error while initializing PostHog app with config id \"+t.id,i),s(!1)}if(r&&i.loaded)try{e()}catch(e){Jo.error(\"Error while processing buffered events PostHog app with config id \"+t.id,e),i.errored=!0}}os(){var t=this.siteAppLoaders||[];for(var i of t)this.apps[i.id]={id:i.id,loaded:!1,errored:!1,processedBuffer:!1};for(var e of t)this.setupSiteApp(e)}ls(t){if(0!==Object.keys(this.apps).length){var i=this.globalsForEvent(t);for(var e of Object.values(this.apps))try{null==e.processEvent||e.processEvent(i)}catch(i){Jo.error(\"Error while processing event \"+t.event+\" for site app \"+e.id,i)}}}onRemoteConfig(t){var i,e,r,s=this;if(null!=(i=this.siteAppLoaders)&&i.length)return this.isEnabled?(this.os(),void this._instance.on(\"eventCaptured\",(t=>this.ls(t)))):void Jo.error('PostHog site apps are disabled. Enable the \"opt_in_site_apps\" config to proceed.');if(null==(e=this.ns)||e.call(this),null!=(r=t.siteApps)&&r.length)if(this.isEnabled){var n=function(t){var i;v[\"__$$ph_site_app_\"+t]=s._instance,null==(i=v.__PosthogExtensions__)||null==i.loadSiteApp||i.loadSiteApp(s._instance,a,(i=>{if(i)return Jo.error(\"Error while initializing PostHog app with config id \"+t,i)}))};for(var{id:o,url:a}of t.siteApps)n(o)}else Jo.error('PostHog site apps are disabled. Enable the \"opt_in_site_apps\" config to proceed.')}}var Ko=[\"amazonbot\",\"amazonproductbot\",\"app.hypefactors.com\",\"applebot\",\"archive.org_bot\",\"awariobot\",\"backlinksextendedbot\",\"baiduspider\",\"bingbot\",\"bingpreview\",\"chrome-lighthouse\",\"dataforseobot\",\"deepscan\",\"duckduckbot\",\"facebookexternal\",\"facebookcatalog\",\"http://yandex.com/bots\",\"hubspot\",\"ia_archiver\",\"leikibot\",\"linkedinbot\",\"meta-externalagent\",\"mj12bot\",\"msnbot\",\"nessus\",\"petalbot\",\"pinterest\",\"prerender\",\"rogerbot\",\"screaming frog\",\"sebot-wa\",\"sitebulb\",\"slackbot\",\"slurp\",\"trendictionbot\",\"turnitin\",\"twitterbot\",\"vercel-screenshot\",\"vercelbot\",\"yahoo! slurp\",\"yandexbot\",\"zoombot\",\"bot.htm\",\"bot.php\",\"(bot;\",\"bot/\",\"crawler\",\"ahrefsbot\",\"ahrefssiteaudit\",\"semrushbot\",\"siteauditbot\",\"splitsignalbot\",\"gptbot\",\"oai-searchbot\",\"chatgpt-user\",\"perplexitybot\",\"better uptime bot\",\"sentryuptimebot\",\"uptimerobot\",\"headlesschrome\",\"cypress\",\"google-hoteladsverifier\",\"adsbot-google\",\"apis-google\",\"duplexweb-google\",\"feedfetcher-google\",\"google favicon\",\"google web preview\",\"google-read-aloud\",\"googlebot\",\"googleother\",\"google-cloudvertexbot\",\"googleweblight\",\"mediapartners-google\",\"storebot-google\",\"google-inspectiontool\",\"bytespider\"],Yo=function(t,i){if(!t)return!1;var e=t.toLowerCase();return Ko.concat(i||[]).some((t=>{var i=t.toLowerCase();return-1!==e.indexOf(i)}))},Xo=function(t,i){if(!t)return!1;var e=t.userAgent;if(e&&Yo(e,i))return!0;try{var r=null==t?void 0:t.userAgentData;if(null!=r&&r.brands&&r.brands.some((t=>Yo(null==t?void 0:t.brand,i))))return!0}catch(t){}return!!t.webdriver},Qo=function(t){return t.US=\"us\",t.EU=\"eu\",t.CUSTOM=\"custom\",t}({}),Zo=\"i.posthog.com\";class ta{constructor(t){this.us={},this.instance=t}get apiHost(){var t=this.instance.config.api_host.trim().replace(/\\/$/,\"\");return\"https://app.posthog.com\"===t?\"https://us.i.posthog.com\":t}get uiHost(){var t,i=null==(t=this.instance.config.ui_host)?void 0:t.replace(/\\/$/,\"\");return i||(i=this.apiHost.replace(\".\"+Zo,\".posthog.com\")),\"https://app.posthog.com\"===i?\"https://us.posthog.com\":i}get region(){return this.us[this.apiHost]||(/https:\\/\\/(app|us|us-assets)(\\.i)?\\.posthog\\.com/i.test(this.apiHost)?this.us[this.apiHost]=Qo.US:/https:\\/\\/(eu|eu-assets)(\\.i)?\\.posthog\\.com/i.test(this.apiHost)?this.us[this.apiHost]=Qo.EU:this.us[this.apiHost]=Qo.CUSTOM),this.us[this.apiHost]}endpointFor(t,i){if(void 0===i&&(i=\"\"),i&&(i=\"/\"===i[0]?i:\"/\"+i),\"ui\"===t)return this.uiHost+i;if(this.region===Qo.CUSTOM)return this.apiHost+i;var e=Zo+i;switch(t){case\"assets\":return\"https://\"+this.region+\"-assets.\"+e;case\"api\":return\"https://\"+this.region+\".\"+e}}}var ia={icontains:(i,e)=>!!t&&e.href.toLowerCase().indexOf(i.toLowerCase())>-1,not_icontains:(i,e)=>!!t&&-1===e.href.toLowerCase().indexOf(i.toLowerCase()),regex:(i,e)=>!!t&&zs(e.href,i),not_regex:(i,e)=>!!t&&!zs(e.href,i),exact:(t,i)=>i.href===t,is_not:(t,i)=>i.href!==t};class ea{constructor(t){var i=this;this.getWebExperimentsAndEvaluateDisplayLogic=function(t){void 0===t&&(t=!1),i.getWebExperiments((t=>{ea.hs(\"retrieved web experiments from the server\"),i.ds=new Map,t.forEach((t=>{if(t.feature_flag_key){var e;if(i.ds)ea.hs(\"setting flag key \",t.feature_flag_key,\" to web experiment \",t),null==(e=i.ds)||e.set(t.feature_flag_key,t);var r=i._instance.getFeatureFlag(t.feature_flag_key);T(r)&&t.variants[r]&&i.vs(t.name,r,t.variants[r].transforms)}else if(t.variants)for(var s in t.variants){var n=t.variants[s];ea.cs(n)&&i.vs(t.name,s,n.transforms)}}))}),t)},this._instance=t,this._instance.onFeatureFlags((t=>{this.onFeatureFlags(t)}))}onFeatureFlags(t){if(this._is_bot())ea.hs(\"Refusing to render web experiment since the viewer is a likely bot\");else if(!this._instance.config.disable_web_experiments){if(F(this.ds))return this.ds=new Map,this.loadIfEnabled(),void this.previewWebExperiment();ea.hs(\"applying feature flags\",t),t.forEach((t=>{var i;if(this.ds&&null!=(i=this.ds)&&i.has(t)){var e,r=this._instance.getFeatureFlag(t),s=null==(e=this.ds)?void 0:e.get(t);r&&null!=s&&s.variants[r]&&this.vs(s.name,r,s.variants[r].transforms)}}))}}previewWebExperiment(){var t=ea.getWindowLocation();if(null!=t&&t.search){var i=xi(null==t?void 0:t.search,\"__experiment_id\"),e=xi(null==t?void 0:t.search,\"__experiment_variant\");i&&e&&(ea.hs(\"previewing web experiments \"+i+\" && \"+e),this.getWebExperiments((t=>{this.fs(parseInt(i),e,t)}),!1,!0))}}loadIfEnabled(){this._instance.config.disable_web_experiments||this.getWebExperimentsAndEvaluateDisplayLogic()}getWebExperiments(t,i,e){if(this._instance.config.disable_web_experiments&&!e)return t([]);var r=this._instance.get_property(\"$web_experiments\");if(r&&!i)return t(r);this._instance.Pe({url:this._instance.requestRouter.endpointFor(\"api\",\"/api/web_experiments/?token=\"+this._instance.config.token),method:\"GET\",callback:i=>{if(200!==i.statusCode||!i.json)return t([]);var e=i.json.experiments||[];return t(e)}})}fs(t,i,e){var r=e.filter((i=>i.id===t));r&&r.length>0&&(ea.hs(\"Previewing web experiment [\"+r[0].name+\"] with variant [\"+i+\"]\"),this.vs(r[0].name,i,r[0].variants[i].transforms))}static cs(t){return!F(t.conditions)&&(ea.ps(t)&&ea._s(t))}static ps(t){var i;if(F(t.conditions)||F(null==(i=t.conditions)?void 0:i.url))return!0;var e,r,s,n=ea.getWindowLocation();return!!n&&(null==(e=t.conditions)||!e.url||ia[null!==(r=null==(s=t.conditions)?void 0:s.urlMatchType)&&void 0!==r?r:\"icontains\"](t.conditions.url,n))}static getWindowLocation(){return null==t?void 0:t.location}static _s(t){var i;if(F(t.conditions)||F(null==(i=t.conditions)?void 0:i.utm))return!0;var e=Vn();if(e.utm_source){var r,s,n,o,a,l,u,h,d=null==(r=t.conditions)||null==(r=r.utm)||!r.utm_campaign||(null==(s=t.conditions)||null==(s=s.utm)?void 0:s.utm_campaign)==e.utm_campaign,v=null==(n=t.conditions)||null==(n=n.utm)||!n.utm_source||(null==(o=t.conditions)||null==(o=o.utm)?void 0:o.utm_source)==e.utm_source,c=null==(a=t.conditions)||null==(a=a.utm)||!a.utm_medium||(null==(l=t.conditions)||null==(l=l.utm)?void 0:l.utm_medium)==e.utm_medium,f=null==(u=t.conditions)||null==(u=u.utm)||!u.utm_term||(null==(h=t.conditions)||null==(h=h.utm)?void 0:h.utm_term)==e.utm_term;return d&&c&&f&&v}return!1}static hs(t){for(var i=arguments.length,e=new Array(i>1?i-1:0),r=1;r<i;r++)e[r-1]=arguments[r];N.info(\"[WebExperiments] \"+t,e)}vs(t,i,e){this._is_bot()?ea.hs(\"Refusing to render web experiment since the viewer is a likely bot\"):\"control\"!==i?e.forEach((e=>{if(e.selector){var r;ea.hs(\"applying transform of variant \"+i+\" for experiment \"+t+\" \",e);var s=null==(r=document)?void 0:r.querySelectorAll(e.selector);null==s||s.forEach((t=>{var i=t;e.html&&(i.innerHTML=e.html),e.css&&i.setAttribute(\"style\",e.css)}))}})):ea.hs(\"Control variants leave the page unmodified.\")}_is_bot(){return n&&this._instance?Xo(n,this._instance.config.custom_blocked_useragents):void 0}}var ra=z(\"[PostHog ExternalIntegrations]\"),sa={intercom:\"intercom-integration\",crispChat:\"crisp-chat-integration\"};class na{constructor(t){this._instance=t}J(t,i){var e;null==(e=v.__PosthogExtensions__)||null==e.loadExternalDependency||e.loadExternalDependency(this._instance,t,(t=>{if(t)return ra.error(\"failed to load script\",t);i()}))}startIfEnabledOrStop(){var t=this,i=function(i){var e,s,n;(!r||null!=(e=v.__PosthogExtensions__)&&null!=(e=e.integrations)&&e[i]||t.J(sa[i],(()=>{var e;null==(e=v.__PosthogExtensions__)||null==(e=e.integrations)||null==(e=e[i])||e.start(t._instance)})),!r&&null!=(s=v.__PosthogExtensions__)&&null!=(s=s.integrations)&&s[i])&&(null==(n=v.__PosthogExtensions__)||null==(n=n.integrations)||null==(n=n[i])||n.stop())};for(var[e,r]of Object.entries(null!==(s=this._instance.config.integrations)&&void 0!==s?s:{})){var s;i(e)}}}var oa={},aa=()=>{},la=\"posthog\",ua=!Os&&-1===(null==d?void 0:d.indexOf(\"MSIE\"))&&-1===(null==d?void 0:d.indexOf(\"Mozilla\")),ha=i=>{var e;return{api_host:\"https://us.i.posthog.com\",ui_host:null,token:\"\",autocapture:!0,rageclick:!0,cross_subdomain_cookie:et(null==o?void 0:o.location),persistence:\"localStorage+cookie\",persistence_name:\"\",loaded:aa,save_campaign_params:!0,custom_campaign_params:[],custom_blocked_useragents:[],save_referrer:!0,capture_pageview:\"2025-05-24\"!==i||\"history_change\",capture_pageleave:\"if_capture_pageview\",defaults:null!=i?i:\"unset\",debug:a&&T(null==a?void 0:a.search)&&-1!==a.search.indexOf(\"__posthog_debug=true\")||!1,cookie_expiration:365,upgrade:!1,disable_session_recording:!1,disable_persistence:!1,disable_web_experiments:!0,disable_surveys:!1,disable_surveys_automatic_display:!1,disable_external_dependency_loading:!1,enable_recording_console_log:void 0,secure_cookie:\"https:\"===(null==t||null==(e=t.location)?void 0:e.protocol),ip:!1,opt_out_capturing_by_default:!1,opt_out_persistence_by_default:!1,opt_out_useragent_filter:!1,opt_out_capturing_persistence_type:\"localStorage\",opt_out_capturing_cookie_prefix:null,opt_in_site_apps:!1,property_denylist:[],respect_dnt:!1,sanitize_properties:null,request_headers:{},request_batching:!0,properties_string_max_length:65535,session_recording:{},mask_all_element_attributes:!1,mask_all_text:!1,mask_personal_data_properties:!1,custom_personal_data_properties:[],advanced_disable_flags:!1,advanced_disable_decide:!1,advanced_disable_feature_flags:!1,advanced_disable_feature_flags_on_first_load:!1,advanced_only_evaluate_survey_feature_flags:!1,advanced_enable_surveys:!1,advanced_disable_toolbar_metrics:!1,feature_flag_request_timeout_ms:3e3,surveys_request_timeout_ms:1e4,on_request_error:t=>{var i=\"Bad HTTP status: \"+t.statusCode+\" \"+t.text;N.error(i)},get_device_id:t=>t,capture_performance:void 0,name:\"posthog\",bootstrap:{},disable_compression:!1,session_idle_timeout_seconds:1800,person_profiles:\"identified_only\",before_send:void 0,request_queue_config:{flush_interval_ms:Lo},error_tracking:{},_onCapture:aa}},da=t=>{var i={};R(t.process_person)||(i.person_profiles=t.process_person),R(t.xhr_headers)||(i.request_headers=t.xhr_headers),R(t.cookie_name)||(i.persistence_name=t.cookie_name),R(t.disable_cookie)||(i.disable_persistence=t.disable_cookie),R(t.store_google)||(i.save_campaign_params=t.store_google),R(t.verbose)||(i.debug=t.verbose);var e=V({},i,t);return k(t.property_blacklist)&&(R(t.property_denylist)?e.property_denylist=t.property_blacklist:k(t.property_denylist)?e.property_denylist=[...t.property_blacklist,...t.property_denylist]:N.error(\"Invalid value for property_denylist config: \"+t.property_denylist)),e};class va{constructor(){this.__forceAllowLocalhost=!1}get gs(){return this.__forceAllowLocalhost}set gs(t){N.error(\"WebPerformanceObserver is deprecated and has no impact on network capture. Use `_forceAllowLocalhostNetworkCapture` on `posthog.sessionRecording`\"),this.__forceAllowLocalhost=t}}class ca{get decideEndpointWasHit(){var t,i;return null!==(t=null==(i=this.featureFlags)?void 0:i.hasLoadedFlags)&&void 0!==t&&t}get flagsEndpointWasHit(){var t,i;return null!==(t=null==(i=this.featureFlags)?void 0:i.hasLoadedFlags)&&void 0!==t&&t}constructor(){this.webPerformance=new va,this.bs=!1,this.version=c.LIB_VERSION,this.ys=new go,this._calculate_event_properties=this.calculateEventProperties.bind(this),this.config=ha(),this.SentryIntegration=ms,this.sentryIntegration=t=>function(t,i){var e=gs(t,i);return{name:_s,processEvent:t=>e(t)}}(this,t),this.__request_queue=[],this.__loaded=!1,this.analyticsDefaultEndpoint=\"/e/\",this.ws=!1,this.Ss=null,this.$s=null,this.xs=null,this.featureFlags=new fo(this),this.toolbar=new $s(this),this.scrollManager=new Uo(this),this.pageViewManager=new Cs(this),this.surveys=new Co(this),this.experiments=new ea(this),this.exceptions=new Ws(this),this.rateLimiter=new Oo(this),this.requestRouter=new ta(this),this.consent=new te(this),this.externalIntegrations=new na(this),this.people={set:(t,i,e)=>{var r=T(t)?{[t]:i}:t;this.setPersonProperties(r),null==e||e({})},set_once:(t,i,e)=>{var r=T(t)?{[t]:i}:t;this.setPersonProperties(void 0,r),null==e||e({})}},this.on(\"eventCaptured\",(t=>N.info('send \"'+(null==t?void 0:t.event)+'\"',t)))}init(t,i,e){if(e&&e!==la){var r,s=null!==(r=oa[e])&&void 0!==r?r:new ca;return s._init(t,i,e),oa[e]=s,oa[la][e]=s,s}return this._init(t,i,e)}_init(i,e,r){var s,n;if(void 0===e&&(e={}),R(i)||M(i))return N.critical(\"PostHog was initialized without a token. This likely indicates a misconfiguration. Please check the first argument passed to posthog.init()\"),this;if(this.__loaded)return N.warn(\"You have already initialized PostHog! Re-initializing is a no-op\"),this;this.__loaded=!0,this.config={},this.ks=e,this.Es=[],e.person_profiles&&(this.$s=e.person_profiles),this.set_config(V({},ha(e.defaults),da(e),{name:r,token:i})),this.config.on_xhr_error&&N.error(\"on_xhr_error is deprecated. Use on_request_error instead\"),this.compression=e.disable_compression?void 0:_.GZipJS;var o=this.Is();this.persistence=new _o(this.config,o),this.sessionPersistence=\"sessionStorage\"===this.config.persistence||\"memory\"===this.config.persistence?this.persistence:new _o(B({},this.config,{persistence:\"sessionStorage\"}),o);var a=B({},this.persistence.props),l=B({},this.sessionPersistence.props);if(this.register({$initialization_time:(new Date).toISOString()}),this.Ps=new jo((t=>this.Rs(t)),this.config.request_queue_config),this.Ts=new zo(this),this.__request_queue=[],this.config.__preview_experimental_cookieless_mode||(this.sessionManager=new Wo(this),this.sessionPropsManager=new Bo(this,this.sessionManager,this.persistence)),new ks(this).startIfEnabledOrStop(),this.siteApps=new Vo(this),null==(s=this.siteApps)||s.init(),this.config.__preview_experimental_cookieless_mode||(this.sessionRecording=new cs(this),this.sessionRecording.startIfEnabledOrStop()),this.config.disable_scroll_properties||this.scrollManager.startMeasuringScrollPosition(),this.autocapture=new Ci(this),this.autocapture.startIfEnabled(),this.surveys.loadIfEnabled(),this.heatmaps=new Ms(this),this.heatmaps.startIfEnabled(),this.webVitalsAutocapture=new Ps(this),this.exceptionObserver=new le(this),this.exceptionObserver.startIfEnabled(),this.deadClicksAutocapture=new se(this,re),this.deadClicksAutocapture.startIfEnabled(),this.historyAutocapture=new ze(this),this.historyAutocapture.startIfEnabled(),c.DEBUG=c.DEBUG||this.config.debug,c.DEBUG&&N.info(\"Starting in debug mode\",{this:this,config:e,thisC:B({},this.config),p:a,s:l}),void 0!==(null==(n=e.bootstrap)?void 0:n.distinctID)){var u,h,d=this.config.get_device_id(Ni()),v=null!=(u=e.bootstrap)&&u.isIdentifiedID?d:e.bootstrap.distinctID;this.persistence.set_property(Dt,null!=(h=e.bootstrap)&&h.isIdentifiedID?\"identified\":\"anonymous\"),this.register({distinct_id:e.bootstrap.distinctID,$device_id:v})}if(this.Ms()){var f,p,g=Object.keys((null==(f=e.bootstrap)?void 0:f.featureFlags)||{}).filter((t=>{var i;return!(null==(i=e.bootstrap)||null==(i=i.featureFlags)||!i[t])})).reduce(((t,i)=>{var r;return t[i]=(null==(r=e.bootstrap)||null==(r=r.featureFlags)?void 0:r[i])||!1,t}),{}),m=Object.keys((null==(p=e.bootstrap)?void 0:p.featureFlagPayloads)||{}).filter((t=>g[t])).reduce(((t,i)=>{var r,s;null!=(r=e.bootstrap)&&null!=(r=r.featureFlagPayloads)&&r[i]&&(t[i]=null==(s=e.bootstrap)||null==(s=s.featureFlagPayloads)?void 0:s[i]);return t}),{});this.featureFlags.receivedFeatureFlags({featureFlags:g,featureFlagPayloads:m})}if(this.config.__preview_experimental_cookieless_mode)this.register_once({distinct_id:Ht,$device_id:null},\"\");else if(!this.get_distinct_id()){var b=this.config.get_device_id(Ni());this.register_once({distinct_id:b,$device_id:b},\"\"),this.persistence.set_property(Dt,\"anonymous\")}return st(t,\"onpagehide\"in self?\"pagehide\":\"unload\",this._handle_unload.bind(this),{passive:!1}),this.toolbar.maybeLoadToolbar(),e.segment?ps(this,(()=>this.Cs())):this.Cs(),E(this.config._onCapture)&&this.config._onCapture!==aa&&(N.warn(\"onCapture is deprecated. Please use `before_send` instead\"),this.on(\"eventCaptured\",(t=>this.config._onCapture(t.event,t)))),this.config.ip&&N.warn('The `ip` config option has NO EFFECT AT ALL and has been deprecated. Use a custom transformation or \"Discard IP data\" project setting instead. See https://posthog.com/tutorials/web-redact-properties#hiding-customer-ip-address for more information.'),this}Re(t){var i,e,r,s,n,a,l,u;if(!o||!o.body)return N.info(\"document not ready yet, trying again in 500 milliseconds...\"),void setTimeout((()=>{this.Re(t)}),500);this.compression=void 0,t.supportedCompression&&!this.config.disable_compression&&(this.compression=m(t.supportedCompression,_.GZipJS)?_.GZipJS:m(t.supportedCompression,_.Base64)?_.Base64:void 0),null!=(i=t.analytics)&&i.endpoint&&(this.analyticsDefaultEndpoint=t.analytics.endpoint),this.set_config({person_profiles:this.$s?this.$s:\"identified_only\"}),null==(e=this.siteApps)||e.onRemoteConfig(t),null==(r=this.sessionRecording)||r.onRemoteConfig(t),null==(s=this.autocapture)||s.onRemoteConfig(t),null==(n=this.heatmaps)||n.onRemoteConfig(t),this.surveys.onRemoteConfig(t),null==(a=this.webVitalsAutocapture)||a.onRemoteConfig(t),null==(l=this.exceptionObserver)||l.onRemoteConfig(t),this.exceptions.onRemoteConfig(t),null==(u=this.deadClicksAutocapture)||u.onRemoteConfig(t)}Cs(){try{this.config.loaded(this)}catch(t){N.critical(\"`loaded` function failed\",t)}this.Fs(),this.config.capture_pageview&&setTimeout((()=>{this.consent.isOptedIn()&&this.Os()}),1),new Do(this).load(),this.featureFlags.flags()}Fs(){var t;this.has_opted_out_capturing()||this.config.request_batching&&(null==(t=this.Ps)||t.enable())}_dom_loaded(){this.has_opted_out_capturing()||G(this.__request_queue,(t=>this.Rs(t))),this.__request_queue=[],this.Fs()}_handle_unload(){var t,i;this.config.request_batching?(this.As()&&this.capture(\"$pageleave\"),null==(t=this.Ps)||t.unload(),null==(i=this.Ts)||i.unload()):this.As()&&this.capture(\"$pageleave\",null,{transport:\"sendBeacon\"})}Pe(t){this.__loaded&&(ua?this.__request_queue.push(t):this.rateLimiter.isServerRateLimited(t.batchKey)||(t.transport=t.transport||this.config.api_transport,t.url=Ds(t.url,{ip:this.config.ip?1:0}),t.headers=B({},this.config.request_headers),t.compression=\"best-available\"===t.compression?this.compression:t.compression,t.fetchOptions=t.fetchOptions||this.config.fetch_options,(t=>{var i,e,r,s=B({},t);s.timeout=s.timeout||6e4,s.url=Ds(s.url,{_:(new Date).getTime().toString(),ver:c.LIB_VERSION,compression:s.compression});var n=null!==(i=s.transport)&&void 0!==i?i:\"fetch\",o=null!==(e=null==(r=rt(Ns,(t=>t.transport===n)))?void 0:r.method)&&void 0!==e?e:Ns[0].method;if(!o)throw new Error(\"No available transport method\");o(s)})(B({},t,{callback:i=>{var e,r;(this.rateLimiter.checkForLimiting(i),i.statusCode>=400)&&(null==(e=(r=this.config).on_request_error)||e.call(r,i));null==t.callback||t.callback(i)}}))))}Rs(t){this.Ts?this.Ts.retriableRequest(t):this.Pe(t)}_execute_array(t){var i,e=[],r=[],s=[];G(t,(t=>{t&&(i=t[0],k(i)?s.push(t):E(t)?t.call(this):k(t)&&\"alias\"===i?e.push(t):k(t)&&-1!==i.indexOf(\"capture\")&&E(this[i])?s.push(t):r.push(t))}));var n=function(t,i){G(t,(function(t){if(k(t[0])){var e=i;J(t,(function(t){e=e[t[0]].apply(e,t.slice(1))}))}else this[t[0]].apply(this,t.slice(1))}),i)};n(e,this),n(r,this),n(s,this)}Ms(){var t,i;return(null==(t=this.config.bootstrap)?void 0:t.featureFlags)&&Object.keys(null==(i=this.config.bootstrap)?void 0:i.featureFlags).length>0||!1}push(t){this._execute_array([t])}capture(t,i,e){var r;if(this.__loaded&&this.persistence&&this.sessionPersistence&&this.Ps){if(!this.consent.isOptedOut())if(!R(t)&&T(t)){if(this.config.opt_out_useragent_filter||!this._is_bot()){var s=null!=e&&e.skip_client_rate_limiting?void 0:this.rateLimiter.clientRateLimitContext();if(null==s||!s.isRateLimited){null!=i&&i.$current_url&&!T(null==i?void 0:i.$current_url)&&(N.error(\"Invalid `$current_url` property provided to `posthog.capture`. Input must be a string. Ignoring provided value.\"),null==i||delete i.$current_url),this.sessionPersistence.update_search_keyword(),this.config.save_campaign_params&&this.sessionPersistence.update_campaign_params(),this.config.save_referrer&&this.sessionPersistence.update_referrer_info(),(this.config.save_campaign_params||this.config.save_referrer)&&this.persistence.set_initial_person_info();var n=new Date,o=(null==e?void 0:e.timestamp)||n,a=Ni(),l={uuid:a,event:t,properties:this.calculateEventProperties(t,i||{},o,a)};s&&(l.properties.$lib_rate_limit_remaining_tokens=s.remainingTokens),(null==e?void 0:e.$set)&&(l.$set=null==e?void 0:e.$set);var u,h,d=this.Ds(null==e?void 0:e.$set_once);if(d&&(l.$set_once=d),(l=tt(l,null!=e&&e._noTruncate?null:this.config.properties_string_max_length)).timestamp=o,R(null==e?void 0:e.timestamp)||(l.properties.$event_time_override_provided=!0,l.properties.$event_time_override_system_time=n),t===ko.DISMISSED||t===ko.SENT){var v=null==i?void 0:i[Eo.SURVEY_ID],c=null==i?void 0:i[Eo.SURVEY_ITERATION];localStorage.setItem((h=\"\"+Po+(u={id:v,current_iteration:c}).id,u.current_iteration&&u.current_iteration>0&&(h=\"\"+Po+u.id+\"_\"+u.current_iteration),h),\"true\"),l.$set=B({},l.$set,{[Ro({id:v,current_iteration:c},t===ko.SENT?\"responded\":\"dismissed\")]:!0})}var f=B({},l.properties.$set,l.$set);if(P(f)||this.setPersonPropertiesForFlags(f),!F(this.config.before_send)){var p=this.Ls(l);if(!p)return;l=p}this.ys.emit(\"eventCaptured\",l);var _={method:\"POST\",url:null!==(r=null==e?void 0:e._url)&&void 0!==r?r:this.requestRouter.endpointFor(\"api\",this.analyticsDefaultEndpoint),data:l,compression:\"best-available\",batchKey:null==e?void 0:e._batchKey};return!this.config.request_batching||e&&(null==e||!e._batchKey)||null!=e&&e.send_instantly?this.Rs(_):this.Ps.enqueue(_),l}N.critical(\"This capture call is ignored due to client rate limiting.\")}}else N.error(\"No event name provided to posthog.capture\")}else N.uninitializedWarning(\"posthog.capture\")}Ve(t){return this.on(\"eventCaptured\",(i=>t(i.event,i)))}calculateEventProperties(t,i,e,r,s){if(e=e||new Date,!this.persistence||!this.sessionPersistence)return i;var n=s?void 0:this.persistence.remove_event_timer(t),a=B({},i);if(a.token=this.config.token,a.$config_defaults=this.config.defaults,this.config.__preview_experimental_cookieless_mode&&(a.$cookieless_mode=!0),\"$snapshot\"===t){var l=B({},this.persistence.properties(),this.sessionPersistence.properties());return a.distinct_id=l.distinct_id,(!T(a.distinct_id)&&!O(a.distinct_id)||M(a.distinct_id))&&N.error(\"Invalid distinct_id for replay event. This indicates a bug in your implementation\"),a}var u,h=ro(this.config.mask_personal_data_properties,this.config.custom_personal_data_properties);if(this.sessionManager){var{sessionId:v,windowId:c}=this.sessionManager.checkAndGetSessionAndWindowId(s,e.getTime());a.$session_id=v,a.$window_id=c}this.sessionPropsManager&&V(a,this.sessionPropsManager.getSessionProps());try{var f;this.sessionRecording&&V(a,this.sessionRecording.sdkDebugProperties),a.$sdk_debug_retry_queue_size=null==(f=this.Ts)?void 0:f.length}catch(t){a.$sdk_debug_error_capturing_properties=String(t)}if(this.requestRouter.region===Qo.CUSTOM&&(a.$lib_custom_api_host=this.config.api_host),u=\"$pageview\"!==t||s?\"$pageleave\"!==t||s?this.pageViewManager.doEvent():this.pageViewManager.doPageLeave(e):this.pageViewManager.doPageView(e,r),a=V(a,u),\"$pageview\"===t&&o&&(a.title=o.title),!R(n)){var p=e.getTime()-n;a.$duration=parseFloat((p/1e3).toFixed(3))}d&&this.config.opt_out_useragent_filter&&(a.$browser_type=this._is_bot()?\"bot\":\"browser\"),(a=V({},h,this.persistence.properties(),this.sessionPersistence.properties(),a)).$is_identified=this._isIdentified(),k(this.config.property_denylist)?J(this.config.property_denylist,(function(t){delete a[t]})):N.error(\"Invalid value for property_denylist config: \"+this.config.property_denylist+\" or property_blacklist config: \"+this.config.property_blacklist);var _=this.config.sanitize_properties;_&&(N.error(\"sanitize_properties is deprecated. Use before_send instead\"),a=_(a,t));var g=this.js();return a.$process_person_profile=g,g&&!s&&this.Ns(\"_calculate_event_properties\"),a}Ds(t){var i;if(!this.persistence||!this.js())return t;if(this.bs)return t;var e=this.persistence.get_initial_props(),r=null==(i=this.sessionPropsManager)?void 0:i.getSetOnceProps(),s=V({},e,r||{},t||{}),n=this.config.sanitize_properties;return n&&(N.error(\"sanitize_properties is deprecated. Use before_send instead\"),s=n(s,\"$set_once\")),this.bs=!0,P(s)?void 0:s}register(t,i){var e;null==(e=this.persistence)||e.register(t,i)}register_once(t,i,e){var r;null==(r=this.persistence)||r.register_once(t,i,e)}register_for_session(t){var i;null==(i=this.sessionPersistence)||i.register(t)}unregister(t){var i;null==(i=this.persistence)||i.unregister(t)}unregister_for_session(t){var i;null==(i=this.sessionPersistence)||i.unregister(t)}zs(t,i){this.register({[t]:i})}getFeatureFlag(t,i){return this.featureFlags.getFeatureFlag(t,i)}getFeatureFlagPayload(t){var i=this.featureFlags.getFeatureFlagPayload(t);try{return JSON.parse(i)}catch(t){return i}}isFeatureEnabled(t,i){return this.featureFlags.isFeatureEnabled(t,i)}reloadFeatureFlags(){this.featureFlags.reloadFeatureFlags()}updateEarlyAccessFeatureEnrollment(t,i,e){this.featureFlags.updateEarlyAccessFeatureEnrollment(t,i,e)}getEarlyAccessFeatures(t,i,e){return void 0===i&&(i=!1),this.featureFlags.getEarlyAccessFeatures(t,i,e)}on(t,i){return this.ys.on(t,i)}onFeatureFlags(t){return this.featureFlags.onFeatureFlags(t)}onSurveysLoaded(t){return this.surveys.onSurveysLoaded(t)}onSessionId(t){var i,e;return null!==(i=null==(e=this.sessionManager)?void 0:e.onSessionId(t))&&void 0!==i?i:()=>{}}getSurveys(t,i){void 0===i&&(i=!1),this.surveys.getSurveys(t,i)}getActiveMatchingSurveys(t,i){void 0===i&&(i=!1),this.surveys.getActiveMatchingSurveys(t,i)}renderSurvey(t,i){this.surveys.renderSurvey(t,i)}canRenderSurvey(t){return this.surveys.canRenderSurvey(t)}canRenderSurveyAsync(t,i){return void 0===i&&(i=!1),this.surveys.canRenderSurveyAsync(t,i)}identify(t,i,e){if(!this.__loaded||!this.persistence)return N.uninitializedWarning(\"posthog.identify\");if(O(t)&&(t=t.toString(),N.warn(\"The first argument to posthog.identify was a number, but it should be a string. It has been converted to a string.\")),t)if([\"distinct_id\",\"distinctid\"].includes(t.toLowerCase()))N.critical('The string \"'+t+'\" was set in posthog.identify which indicates an error. This ID should be unique to the user and not a hardcoded string.');else if(t!==Ht){if(this.Ns(\"posthog.identify\")){var r=this.get_distinct_id();if(this.register({$user_id:t}),!this.get_property(\"$device_id\")){var s=r;this.register_once({$had_persisted_distinct_id:!0,$device_id:s},\"\")}t!==r&&t!==this.get_property(ot)&&(this.unregister(ot),this.register({distinct_id:t}));var n=\"anonymous\"===(this.persistence.get_property(Dt)||\"anonymous\");t!==r&&n?(this.persistence.set_property(Dt,\"identified\"),this.setPersonPropertiesForFlags(B({},e||{},i||{}),!1),this.capture(\"$identify\",{distinct_id:t,$anon_distinct_id:r},{$set:i||{},$set_once:e||{}}),this.xs=Us(t,i,e),this.featureFlags.setAnonymousDistinctId(r)):(i||e)&&this.setPersonProperties(i,e),t!==r&&(this.reloadFeatureFlags(),this.unregister(At))}}else N.critical('The string \"'+Ht+'\" was set in posthog.identify which indicates an error. This ID is only used as a sentinel value.');else N.error(\"Unique user id has not been set in posthog.identify\")}setPersonProperties(t,i){if((t||i)&&this.Ns(\"posthog.setPersonProperties\")){var e=Us(this.get_distinct_id(),t,i);this.xs!==e?(this.setPersonPropertiesForFlags(B({},i||{},t||{})),this.capture(\"$set\",{$set:t||{},$set_once:i||{}}),this.xs=e):N.info(\"A duplicate setPersonProperties call was made with the same properties. It has been ignored.\")}}group(t,i,e){if(t&&i){if(this.Ns(\"posthog.group\")){var r=this.getGroups();r[t]!==i&&this.resetGroupPropertiesForFlags(t),this.register({$groups:B({},r,{[t]:i})}),e&&(this.capture(\"$groupidentify\",{$group_type:t,$group_key:i,$group_set:e}),this.setGroupPropertiesForFlags({[t]:e})),r[t]===i||e||this.reloadFeatureFlags()}}else N.error(\"posthog.group requires a group type and group key\")}resetGroups(){this.register({$groups:{}}),this.resetGroupPropertiesForFlags(),this.reloadFeatureFlags()}setPersonPropertiesForFlags(t,i){void 0===i&&(i=!0),this.featureFlags.setPersonPropertiesForFlags(t,i)}resetPersonPropertiesForFlags(){this.featureFlags.resetPersonPropertiesForFlags()}setGroupPropertiesForFlags(t,i){void 0===i&&(i=!0),this.Ns(\"posthog.setGroupPropertiesForFlags\")&&this.featureFlags.setGroupPropertiesForFlags(t,i)}resetGroupPropertiesForFlags(t){this.featureFlags.resetGroupPropertiesForFlags(t)}reset(t){var i,e,r,s;if(N.info(\"reset\"),!this.__loaded)return N.uninitializedWarning(\"posthog.reset\");var n=this.get_property(\"$device_id\");if(this.consent.reset(),null==(i=this.persistence)||i.clear(),null==(e=this.sessionPersistence)||e.clear(),this.surveys.reset(),this.featureFlags.reset(),null==(r=this.persistence)||r.set_property(Dt,\"anonymous\"),null==(s=this.sessionManager)||s.resetSessionId(),this.xs=null,this.config.__preview_experimental_cookieless_mode)this.register_once({distinct_id:Ht,$device_id:null},\"\");else{var o=this.config.get_device_id(Ni());this.register_once({distinct_id:o,$device_id:t?o:n},\"\")}this.register({$last_posthog_reset:(new Date).toISOString()},1)}get_distinct_id(){return this.get_property(\"distinct_id\")}getGroups(){return this.get_property(\"$groups\")||{}}get_session_id(){var t,i;return null!==(t=null==(i=this.sessionManager)?void 0:i.checkAndGetSessionAndWindowId(!0).sessionId)&&void 0!==t?t:\"\"}get_session_replay_url(t){if(!this.sessionManager)return\"\";var{sessionId:i,sessionStartTimestamp:e}=this.sessionManager.checkAndGetSessionAndWindowId(!0),r=this.requestRouter.endpointFor(\"ui\",\"/project/\"+this.config.token+\"/replay/\"+i);if(null!=t&&t.withTimestamp&&e){var s,n=null!==(s=t.timestampLookBack)&&void 0!==s?s:10;if(!e)return r;r+=\"?t=\"+Math.max(Math.floor(((new Date).getTime()-e)/1e3)-n,0)}return r}alias(t,i){return t===this.get_property(nt)?(N.critical(\"Attempting to create alias for existing People user - aborting.\"),-2):this.Ns(\"posthog.alias\")?(R(i)&&(i=this.get_distinct_id()),t!==i?(this.zs(ot,t),this.capture(\"$create_alias\",{alias:t,distinct_id:i})):(N.warn(\"alias matches current distinct_id - skipping api call.\"),this.identify(t),-1)):void 0}set_config(t){var i=B({},this.config);if(I(t)){var e,r,s,n,o;V(this.config,da(t));var a=this.Is();null==(e=this.persistence)||e.update_config(this.config,i,a),this.sessionPersistence=\"sessionStorage\"===this.config.persistence||\"memory\"===this.config.persistence?this.persistence:new _o(B({},this.config,{persistence:\"sessionStorage\"}),a),Gi.O()&&\"true\"===Gi.D(\"ph_debug\")&&(this.config.debug=!0),this.config.debug&&(c.DEBUG=!0,N.info(\"set_config\",{config:t,oldConfig:i,newConfig:B({},this.config)})),null==(r=this.sessionRecording)||r.startIfEnabledOrStop(),null==(s=this.autocapture)||s.startIfEnabled(),null==(n=this.heatmaps)||n.startIfEnabled(),this.surveys.loadIfEnabled(),this.Us(),null==(o=this.externalIntegrations)||o.startIfEnabledOrStop()}}startSessionRecording(t){var i=!0===t,e={sampling:i||!(null==t||!t.sampling),linked_flag:i||!(null==t||!t.linked_flag),url_trigger:i||!(null==t||!t.url_trigger),event_trigger:i||!(null==t||!t.event_trigger)};if(Object.values(e).some(Boolean)){var r,s,n,o,a;if(null==(r=this.sessionManager)||r.checkAndGetSessionAndWindowId(),e.sampling)null==(s=this.sessionRecording)||s.overrideSampling();if(e.linked_flag)null==(n=this.sessionRecording)||n.overrideLinkedFlag();if(e.url_trigger)null==(o=this.sessionRecording)||o.overrideTrigger(\"url\");if(e.event_trigger)null==(a=this.sessionRecording)||a.overrideTrigger(\"event\")}this.set_config({disable_session_recording:!1})}stopSessionRecording(){this.set_config({disable_session_recording:!0})}sessionRecordingStarted(){var t;return!(null==(t=this.sessionRecording)||!t.started)}captureException(t,i){var e=new Error(\"PostHog syntheticException\");return this.exceptions.sendExceptionEvent(B({},je((t=>t instanceof Error)(t)?{error:t,event:t.message}:{event:t},{syntheticException:e}),i))}loadToolbar(t){return this.toolbar.loadToolbar(t)}get_property(t){var i;return null==(i=this.persistence)?void 0:i.props[t]}getSessionProperty(t){var i;return null==(i=this.sessionPersistence)?void 0:i.props[t]}toString(){var t,i=null!==(t=this.config.name)&&void 0!==t?t:la;return i!==la&&(i=la+\".\"+i),i}_isIdentified(){var t,i;return\"identified\"===(null==(t=this.persistence)?void 0:t.get_property(Dt))||\"identified\"===(null==(i=this.sessionPersistence)?void 0:i.get_property(Dt))}js(){var t,i;return!(\"never\"===this.config.person_profiles||\"identified_only\"===this.config.person_profiles&&!this._isIdentified()&&P(this.getGroups())&&(null==(t=this.persistence)||null==(t=t.props)||!t[ot])&&(null==(i=this.persistence)||null==(i=i.props)||!i[qt]))}As(){return!0===this.config.capture_pageleave||\"if_capture_pageview\"===this.config.capture_pageleave&&(!0===this.config.capture_pageview||\"history_change\"===this.config.capture_pageview)}createPersonProfile(){this.js()||this.Ns(\"posthog.createPersonProfile\")&&this.setPersonProperties({},{})}Ns(t){return\"never\"===this.config.person_profiles?(N.error(t+' was called, but process_person is set to \"never\". This call will be ignored.'),!1):(this.zs(qt,!0),!0)}Is(){var t=this.consent.isOptedOut(),i=this.config.opt_out_persistence_by_default;return this.config.disable_persistence||t&&!!i}Us(){var t,i,e,r,s=this.Is();(null==(t=this.persistence)?void 0:t.Ae)!==s&&(null==(e=this.persistence)||e.set_disabled(s));(null==(i=this.sessionPersistence)?void 0:i.Ae)!==s&&(null==(r=this.sessionPersistence)||r.set_disabled(s));return s}opt_in_capturing(t){var i;(this.consent.optInOut(!0),this.Us(),R(null==t?void 0:t.captureEventName)||null!=t&&t.captureEventName)&&this.capture(null!==(i=null==t?void 0:t.captureEventName)&&void 0!==i?i:\"$opt_in\",null==t?void 0:t.captureProperties,{send_instantly:!0});this.config.capture_pageview&&this.Os()}opt_out_capturing(){this.consent.optInOut(!1),this.Us()}has_opted_in_capturing(){return this.consent.isOptedIn()}has_opted_out_capturing(){return this.consent.isOptedOut()}clear_opt_in_out_capturing(){this.consent.reset(),this.Us()}_is_bot(){return n?Xo(n,this.config.custom_blocked_useragents):void 0}Os(){o&&(\"visible\"===o.visibilityState?this.ws||(this.ws=!0,this.capture(\"$pageview\",{title:o.title},{send_instantly:!0}),this.Ss&&(o.removeEventListener(\"visibilitychange\",this.Ss),this.Ss=null)):this.Ss||(this.Ss=this.Os.bind(this),st(o,\"visibilitychange\",this.Ss)))}debug(i){!1===i?(null==t||t.console.log(\"You've disabled debug mode.\"),localStorage&&localStorage.removeItem(\"ph_debug\"),this.set_config({debug:!1})):(null==t||t.console.log(\"You're now in debug mode. All calls to PostHog will be logged in your console.\\nYou can disable this with `posthog.debug(false)`.\"),localStorage&&localStorage.setItem(\"ph_debug\",\"true\"),this.set_config({debug:!0}))}I(){var t,i,e,r,s,n,o,a=this.ks||{};return\"advanced_disable_flags\"in a?!!a.advanced_disable_flags:!1!==this.config.advanced_disable_flags?!!this.config.advanced_disable_flags:!0===this.config.advanced_disable_decide?(N.warn(\"Config field 'advanced_disable_decide' is deprecated. Please use 'advanced_disable_flags' instead. The old field will be removed in a future major version.\"),!0):(e=\"advanced_disable_decide\",r=!1,s=N,n=(i=\"advanced_disable_flags\")in(t=a)&&!R(t[i]),o=e in t&&!R(t[e]),n?t[i]:o?(s&&s.warn(\"Config field '\"+e+\"' is deprecated. Please use '\"+i+\"' instead. The old field will be removed in a future major version.\"),t[e]):r)}Ls(t){if(F(this.config.before_send))return t;var i=k(this.config.before_send)?this.config.before_send:[this.config.before_send],e=t;for(var r of i){if(e=r(e),F(e)){var s=\"Event '\"+t.event+\"' was rejected in beforeSend function\";return L(t.event)?N.warn(s+\". This can cause unexpected behavior.\"):N.info(s),null}e.properties&&!P(e.properties)||N.warn(\"Event '\"+t.event+\"' has no properties after beforeSend function, this is likely an error.\")}return e}getPageViewId(){var t;return null==(t=this.pageViewManager.ce)?void 0:t.pageViewId}captureTraceFeedback(t,i){this.capture(\"$ai_feedback\",{$ai_trace_id:String(t),$ai_feedback_text:i})}captureTraceMetric(t,i,e){this.capture(\"$ai_metric\",{$ai_trace_id:String(t),$ai_metric_name:i,$ai_metric_value:String(e)})}}!function(t,i){for(var e=0;e<i.length;e++)t.prototype[i[e]]=Q(t.prototype[i[e]])}(ca,[\"identify\"]);var fa,pa=(fa=oa[la]=new ca,function(){function i(){i.done||(i.done=!0,ua=!1,J(oa,(function(t){t._dom_loaded()})))}null!=o&&o.addEventListener?\"complete\"===o.readyState?i():st(o,\"DOMContentLoaded\",i,{capture:!1}):t&&N.error(\"Browser doesn't support `document.addEventListener` so PostHog couldn't be initialized\")}(),fa);\n//# sourceMappingURL=module.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/posthog-js/dist/module.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/posthog-js/react/dist/esm/index.js":
/*!*********************************************************!*\
  !*** ./node_modules/posthog-js/react/dist/esm/index.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PostHogContext: () => (/* binding */ PostHogContext),\n/* harmony export */   PostHogErrorBoundary: () => (/* binding */ PostHogErrorBoundary),\n/* harmony export */   PostHogFeature: () => (/* binding */ PostHogFeature),\n/* harmony export */   PostHogProvider: () => (/* binding */ PostHogProvider),\n/* harmony export */   useActiveFeatureFlags: () => (/* binding */ useActiveFeatureFlags),\n/* harmony export */   useFeatureFlagEnabled: () => (/* binding */ useFeatureFlagEnabled),\n/* harmony export */   useFeatureFlagPayload: () => (/* binding */ useFeatureFlagPayload),\n/* harmony export */   useFeatureFlagVariantKey: () => (/* binding */ useFeatureFlagVariantKey),\n/* harmony export */   usePostHog: () => (/* binding */ usePostHog)\n/* harmony export */ });\n/* harmony import */ var posthog_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! posthog-js */ \"(ssr)/./node_modules/posthog-js/dist/module.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\n\nvar PostHogContext = (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)({ client: posthog_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"] });\n\nfunction isDeepEqual(obj1, obj2, visited) {\n    if (visited === void 0) { visited = new WeakMap(); }\n    if (obj1 === obj2) {\n        return true;\n    }\n    if (typeof obj1 !== 'object' || obj1 === null || typeof obj2 !== 'object' || obj2 === null) {\n        return false;\n    }\n    if (visited.has(obj1) && visited.get(obj1) === obj2) {\n        return true;\n    }\n    visited.set(obj1, obj2);\n    var keys1 = Object.keys(obj1);\n    var keys2 = Object.keys(obj2);\n    if (keys1.length !== keys2.length) {\n        return false;\n    }\n    for (var _i = 0, keys1_1 = keys1; _i < keys1_1.length; _i++) {\n        var key = keys1_1[_i];\n        if (!keys2.includes(key)) {\n            return false;\n        }\n        if (!isDeepEqual(obj1[key], obj2[key], visited)) {\n            return false;\n        }\n    }\n    return true;\n}\n\nfunction PostHogProvider(_a) {\n    var children = _a.children, client = _a.client, apiKey = _a.apiKey, options = _a.options;\n    var previousInitializationRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    var posthog = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(function () {\n        if (client) {\n            if (apiKey) {\n                console.warn('[PostHog.js] You have provided both `client` and `apiKey` to `PostHogProvider`. `apiKey` will be ignored in favour of `client`.');\n            }\n            if (options) {\n                console.warn('[PostHog.js] You have provided both `client` and `options` to `PostHogProvider`. `options` will be ignored in favour of `client`.');\n            }\n            return client;\n        }\n        if (apiKey) {\n            return posthog_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"];\n        }\n        console.warn('[PostHog.js] No `apiKey` or `client` were provided to `PostHogProvider`. Using default global `window.posthog` instance. You must initialize it manually. This is not recommended behavior.');\n        return posthog_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"];\n    }, [client, apiKey, JSON.stringify(options)]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function () {\n        if (client) {\n            return;\n        }\n        var previousInitialization = previousInitializationRef.current;\n        if (!previousInitialization) {\n            if (posthog_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].__loaded) {\n                console.warn('[PostHog.js] `posthog` was already loaded elsewhere. This may cause issues.');\n            }\n            posthog_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].init(apiKey, options);\n            previousInitializationRef.current = {\n                apiKey: apiKey,\n                options: options !== null && options !== void 0 ? options : {},\n            };\n        }\n        else {\n            if (apiKey !== previousInitialization.apiKey) {\n                console.warn(\"[PostHog.js] You have provided a different `apiKey` to `PostHogProvider` than the one that was already initialized. This is not supported by our provider and we'll keep using the previous key. If you need to toggle between API Keys you need to control the `client` yourself and pass it in as a prop rather than an `apiKey` prop.\");\n            }\n            if (options && !isDeepEqual(options, previousInitialization.options)) {\n                posthog_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].set_config(options);\n            }\n            previousInitializationRef.current = {\n                apiKey: apiKey,\n                options: options !== null && options !== void 0 ? options : {},\n            };\n        }\n    }, [client, apiKey, JSON.stringify(options)]);\n    return react__WEBPACK_IMPORTED_MODULE_1___default().createElement(PostHogContext.Provider, { value: { client: posthog } }, children);\n}\n\nvar usePostHog = function () {\n    var client = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(PostHogContext).client;\n    return client;\n};\n\nfunction useFeatureFlagEnabled(flag) {\n    var client = usePostHog();\n    var _a = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(function () { return client.isFeatureEnabled(flag); }), featureEnabled = _a[0], setFeatureEnabled = _a[1];\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function () {\n        return client.onFeatureFlags(function () {\n            setFeatureEnabled(client.isFeatureEnabled(flag));\n        });\n    }, [client, flag]);\n    return featureEnabled;\n}\n\nfunction useFeatureFlagPayload(flag) {\n    var client = usePostHog();\n    var _a = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(function () { return client.getFeatureFlagPayload(flag); }), featureFlagPayload = _a[0], setFeatureFlagPayload = _a[1];\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function () {\n        return client.onFeatureFlags(function () {\n            setFeatureFlagPayload(client.getFeatureFlagPayload(flag));\n        });\n    }, [client, flag]);\n    return featureFlagPayload;\n}\n\nfunction useActiveFeatureFlags() {\n    var client = usePostHog();\n    var _a = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(function () { return client.featureFlags.getFlags(); }), featureFlags = _a[0], setFeatureFlags = _a[1];\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function () {\n        return client.onFeatureFlags(function (flags) {\n            setFeatureFlags(flags);\n        });\n    }, [client]);\n    return featureFlags;\n}\n\nfunction useFeatureFlagVariantKey(flag) {\n    var client = usePostHog();\n    var _a = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(function () {\n        return client.getFeatureFlag(flag);\n    }), featureFlagVariantKey = _a[0], setFeatureFlagVariantKey = _a[1];\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function () {\n        return client.onFeatureFlags(function () {\n            setFeatureFlagVariantKey(client.getFeatureFlag(flag));\n        });\n    }, [client, flag]);\n    return featureFlagVariantKey;\n}\n\n/******************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n/* global Reflect, Promise, SuppressedError, Symbol, Iterator */\r\n\r\nvar extendStatics = function(d, b) {\r\n    extendStatics = Object.setPrototypeOf ||\r\n        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n        function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\r\n    return extendStatics(d, b);\r\n};\r\n\r\nfunction __extends(d, b) {\r\n    if (typeof b !== \"function\" && b !== null)\r\n        throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\nvar __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    };\r\n    return __assign.apply(this, arguments);\r\n};\r\n\r\nfunction __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\r\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\r\n                t[p[i]] = s[p[i]];\r\n        }\r\n    return t;\r\n}\r\n\r\ntypeof SuppressedError === \"function\" ? SuppressedError : function (error, suppressed, message) {\r\n    var e = new Error(message);\r\n    return e.name = \"SuppressedError\", e.error = error, e.suppressed = suppressed, e;\r\n};\n\nvar isFunction = function (f) {\n    return typeof f === 'function';\n};\nvar isUndefined = function (x) {\n    return x === void 0;\n};\nvar isNull = function (x) {\n    return x === null;\n};\n\nfunction PostHogFeature(_a) {\n    var flag = _a.flag, match = _a.match, children = _a.children, fallback = _a.fallback, visibilityObserverOptions = _a.visibilityObserverOptions, trackInteraction = _a.trackInteraction, trackView = _a.trackView, props = __rest(_a, [\"flag\", \"match\", \"children\", \"fallback\", \"visibilityObserverOptions\", \"trackInteraction\", \"trackView\"]);\n    var payload = useFeatureFlagPayload(flag);\n    var variant = useFeatureFlagVariantKey(flag);\n    var shouldTrackInteraction = trackInteraction !== null && trackInteraction !== void 0 ? trackInteraction : true;\n    var shouldTrackView = trackView !== null && trackView !== void 0 ? trackView : true;\n    if (isUndefined(match) || variant === match) {\n        var childNode = isFunction(children) ? children(payload) : children;\n        return (react__WEBPACK_IMPORTED_MODULE_1___default().createElement(VisibilityAndClickTrackers, __assign({ flag: flag, options: visibilityObserverOptions, trackInteraction: shouldTrackInteraction, trackView: shouldTrackView }, props), childNode));\n    }\n    return react__WEBPACK_IMPORTED_MODULE_1___default().createElement((react__WEBPACK_IMPORTED_MODULE_1___default().Fragment), null, fallback);\n}\nfunction captureFeatureInteraction(_a) {\n    var _b;\n    var flag = _a.flag, posthog = _a.posthog, flagVariant = _a.flagVariant;\n    var properties = {\n        feature_flag: flag,\n        $set: (_b = {}, _b[\"$feature_interaction/\".concat(flag)] = flagVariant !== null && flagVariant !== void 0 ? flagVariant : true, _b),\n    };\n    if (typeof flagVariant === 'string') {\n        properties.feature_flag_variant = flagVariant;\n    }\n    posthog.capture('$feature_interaction', properties);\n}\nfunction captureFeatureView(_a) {\n    var _b;\n    var flag = _a.flag, posthog = _a.posthog, flagVariant = _a.flagVariant;\n    var properties = {\n        feature_flag: flag,\n        $set: (_b = {}, _b[\"$feature_view/\".concat(flag)] = flagVariant !== null && flagVariant !== void 0 ? flagVariant : true, _b),\n    };\n    if (typeof flagVariant === 'string') {\n        properties.feature_flag_variant = flagVariant;\n    }\n    posthog.capture('$feature_view', properties);\n}\nfunction VisibilityAndClickTracker(_a) {\n    var flag = _a.flag, children = _a.children, onIntersect = _a.onIntersect, onClick = _a.onClick, trackView = _a.trackView, options = _a.options, props = __rest(_a, [\"flag\", \"children\", \"onIntersect\", \"onClick\", \"trackView\", \"options\"]);\n    var ref = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    var posthog = usePostHog();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function () {\n        if (isNull(ref.current) || !trackView)\n            return;\n        var observer = new IntersectionObserver(function (_a) {\n            var entry = _a[0];\n            return onIntersect(entry);\n        }, __assign({ threshold: 0.1 }, options));\n        observer.observe(ref.current);\n        return function () { return observer.disconnect(); };\n    }, [flag, options, posthog, ref, trackView, onIntersect]);\n    return (react__WEBPACK_IMPORTED_MODULE_1___default().createElement(\"div\", __assign({ ref: ref }, props, { onClick: onClick }), children));\n}\nfunction VisibilityAndClickTrackers(_a) {\n    var flag = _a.flag, children = _a.children, trackInteraction = _a.trackInteraction, trackView = _a.trackView, options = _a.options, props = __rest(_a, [\"flag\", \"children\", \"trackInteraction\", \"trackView\", \"options\"]);\n    var clickTrackedRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    var visibilityTrackedRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    var posthog = usePostHog();\n    var variant = useFeatureFlagVariantKey(flag);\n    var cachedOnClick = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function () {\n        if (!clickTrackedRef.current && trackInteraction) {\n            captureFeatureInteraction({ flag: flag, posthog: posthog, flagVariant: variant });\n            clickTrackedRef.current = true;\n        }\n    }, [flag, posthog, trackInteraction, variant]);\n    var onIntersect = function (entry) {\n        if (!visibilityTrackedRef.current && entry.isIntersecting) {\n            captureFeatureView({ flag: flag, posthog: posthog, flagVariant: variant });\n            visibilityTrackedRef.current = true;\n        }\n    };\n    var trackedChildren = react__WEBPACK_IMPORTED_MODULE_1__.Children.map(children, function (child) {\n        return (react__WEBPACK_IMPORTED_MODULE_1___default().createElement(VisibilityAndClickTracker, __assign({ flag: flag, onClick: cachedOnClick, onIntersect: onIntersect, trackView: trackView, options: options }, props), child));\n    });\n    return react__WEBPACK_IMPORTED_MODULE_1___default().createElement((react__WEBPACK_IMPORTED_MODULE_1___default().Fragment), null, trackedChildren);\n}\n\nvar INITIAL_STATE = {\n    componentStack: null,\n    error: null,\n};\nvar __POSTHOG_ERROR_MESSAGES = {\n    INVALID_FALLBACK: '[PostHog.js][PostHogErrorBoundary] Invalid fallback prop, provide a valid React element or a function that returns a valid React element.',\n};\nvar PostHogErrorBoundary = (function (_super) {\n    __extends(PostHogErrorBoundary, _super);\n    function PostHogErrorBoundary(props) {\n        var _this = _super.call(this, props) || this;\n        _this.state = INITIAL_STATE;\n        return _this;\n    }\n    PostHogErrorBoundary.prototype.componentDidCatch = function (error, errorInfo) {\n        var componentStack = errorInfo.componentStack;\n        var additionalProperties = this.props.additionalProperties;\n        this.setState({\n            error: error,\n            componentStack: componentStack,\n        });\n        var currentProperties;\n        if (isFunction(additionalProperties)) {\n            currentProperties = additionalProperties(error);\n        }\n        else if (typeof additionalProperties === 'object') {\n            currentProperties = additionalProperties;\n        }\n        var client = this.context.client;\n        client.captureException(error, currentProperties);\n    };\n    PostHogErrorBoundary.prototype.render = function () {\n        var _a = this.props, children = _a.children, fallback = _a.fallback;\n        var state = this.state;\n        if (state.componentStack == null) {\n            return isFunction(children) ? children() : children;\n        }\n        var element = isFunction(fallback)\n            ? react__WEBPACK_IMPORTED_MODULE_1___default().createElement(fallback, {\n                error: state.error,\n                componentStack: state.componentStack,\n            })\n            : fallback;\n        if (react__WEBPACK_IMPORTED_MODULE_1___default().isValidElement(element)) {\n            return element;\n        }\n        console.warn(__POSTHOG_ERROR_MESSAGES.INVALID_FALLBACK);\n        return react__WEBPACK_IMPORTED_MODULE_1___default().createElement((react__WEBPACK_IMPORTED_MODULE_1___default().Fragment), null);\n    };\n    PostHogErrorBoundary.contextType = PostHogContext;\n    return PostHogErrorBoundary;\n}((react__WEBPACK_IMPORTED_MODULE_1___default().Component)));\n\n\n//# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/posthog-js/react/dist/esm/index.js\n");

/***/ })

};
;