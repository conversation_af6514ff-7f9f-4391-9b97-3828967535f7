"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/projects/[id]/page",{

/***/ "(app-pages-browser)/./src/components/project/ProjectDetailHeader.tsx":
/*!********************************************************!*\
  !*** ./src/components/project/ProjectDetailHeader.tsx ***!
  \********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ProjectDetailHeader: () => (/* binding */ ProjectDetailHeader)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_ui_popover__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/popover */ \"(app-pages-browser)/./src/components/ui/popover.tsx\");\n/* harmony import */ var _components_ui_sidebar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/sidebar */ \"(app-pages-browser)/./src/components/ui/sidebar.tsx\");\n/* harmony import */ var _components_ui_sidebar_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/sidebar-button */ \"(app-pages-browser)/./src/components/ui/sidebar-button.tsx\");\n/* harmony import */ var _hooks_useAnalytics__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/useAnalytics */ \"(app-pages-browser)/./src/hooks/useAnalytics.ts\");\n/* harmony import */ var _lib_constants__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/constants */ \"(app-pages-browser)/./src/lib/constants.ts\");\n/* harmony import */ var _mockdata_businessItemQuestions__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/mockdata/businessItemQuestions */ \"(app-pages-browser)/./src/mockdata/businessItemQuestions.ts\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_HelpCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,HelpCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_HelpCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,HelpCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-question-mark.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_7__);\n/* __next_internal_client_entry_do_not_use__ ProjectDetailHeader auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction ProjectDetailHeader(param) {\n    let { selectedBusinessItem, onBackToItems } = param;\n    _s();\n    const { trackClick, trackCustomEvent } = (0,_hooks_useAnalytics__WEBPACK_IMPORTED_MODULE_4__.useAnalytics)();\n    const [isHelpOpen, setIsHelpOpen] = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(false);\n    const [isInfoOpen, setIsInfoOpen] = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(false);\n    const { state, isMobile } = (0,_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_2__.useSidebar)();\n    // Get dynamic question and answer based on business item\n    const { question, answer } = (0,_mockdata_businessItemQuestions__WEBPACK_IMPORTED_MODULE_6__.getBusinessItemQA)(selectedBusinessItem);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"flex h-20 shrink-0 items-center gap-2 transition-[width] ease-linear border-b border-border\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-between w-full h-full px-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-4 h-full\",\n                    children: [\n                        isMobile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar_button__WEBPACK_IMPORTED_MODULE_3__.SidebarButton, {\n                            onClick: ()=>{\n                                trackClick(\"back-to-items\", \"project-detail-header\");\n                                trackCustomEvent(\"navigation_clicked\", {\n                                    destination: \"items\",\n                                    from_page: \"item-detail\",\n                                    location: \"header\"\n                                });\n                                onBackToItems();\n                            },\n                            icon: _barrel_optimize_names_ArrowLeft_HelpCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n                            variant: \"ghost\",\n                            size: \"lg\",\n                            layout: \"icon-only\",\n                            showBorder: true,\n                            hoverColor: \"grey\",\n                            hoverScale: true,\n                            iconClassName: _lib_constants__WEBPACK_IMPORTED_MODULE_5__.ICON_SIZES.lg\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                            lineNumber: 41,\n                            columnNumber: 13\n                        }, this),\n                        state === \"collapsed\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-xl font-semibold text-gray-900 dark:text-gray-100\",\n                            children: (selectedBusinessItem === null || selectedBusinessItem === void 0 ? void 0 : selectedBusinessItem.title) || \"Untitled Item\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                            lineNumber: 62,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                    lineNumber: 39,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 flex flex-col justify-center mr-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2 mb-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_1__.Popover, {\n                                open: isInfoOpen,\n                                onOpenChange: setIsInfoOpen,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_1__.PopoverTrigger, {\n                                        asChild: true,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"flex items-center justify-center w-4 h-4 rounded-full bg-blue-100 dark:bg-blue-900 hover:bg-blue-200 dark:hover:bg-blue-800 transition-colors\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-2.5 h-2.5 text-blue-600 dark:text-blue-400\",\n                                                fill: \"currentColor\",\n                                                viewBox: \"0 0 20 20\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    fillRule: \"evenodd\",\n                                                    d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z\",\n                                                    clipRule: \"evenodd\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                                                    lineNumber: 80,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                                                lineNumber: 75,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                                            lineNumber: 74,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                                        lineNumber: 73,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_1__.PopoverContent, {\n                                        align: \"start\",\n                                        side: \"bottom\",\n                                        className: \"w-80\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-semibold text-sm\",\n                                                    children: \"Description\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                                                    lineNumber: 90,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-600 dark:text-gray-400 leading-relaxed\",\n                                                    children: answer\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                                                    lineNumber: 91,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                                            lineNumber: 89,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                                        lineNumber: 88,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                                lineNumber: 72,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm font-bold text-[var(--brand-dark)] dark:text-[var(--primary)]\",\n                                children: question\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                                lineNumber: 97,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                        lineNumber: 71,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                    lineNumber: 69,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-2 h-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_1__.Popover, {\n                        open: isHelpOpen,\n                        onOpenChange: setIsHelpOpen,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_1__.PopoverTrigger, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar_button__WEBPACK_IMPORTED_MODULE_3__.SidebarButton, {\n                                    icon: _barrel_optimize_names_ArrowLeft_HelpCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n                                    variant: \"ghost\",\n                                    size: \"lg\",\n                                    layout: \"icon-only\",\n                                    showBorder: true,\n                                    hoverColor: \"grey\",\n                                    hoverScale: true,\n                                    onClick: ()=>{\n                                        trackClick(\"help-button\", \"project-header\");\n                                        trackCustomEvent(\"help_clicked\", {\n                                            from_item: selectedBusinessItem === null || selectedBusinessItem === void 0 ? void 0 : selectedBusinessItem.title,\n                                            location: \"header\"\n                                        });\n                                    },\n                                    iconClassName: _lib_constants__WEBPACK_IMPORTED_MODULE_5__.ICON_SIZES.lg\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                                    lineNumber: 107,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                                lineNumber: 106,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_1__.PopoverContent, {\n                                align: \"end\",\n                                side: \"bottom\",\n                                className: \"w-80\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold text-sm\",\n                                            children: \"Table Guide\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                                            lineNumber: 127,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2 text-xs\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2 p-2 border-gray-200 dark:border-gray-700 rounded-md border-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-blue-500 rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                                                            lineNumber: 130,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    children: \"Idea:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                                                                    lineNumber: 132,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \" What is the main idea of the item?\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                                                            lineNumber: 131,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                                                    lineNumber: 129,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2 p-2 border-gray-200 dark:border-gray-700 rounded-md border-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-yellow-500 rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                                                            lineNumber: 136,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    children: \"Action:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                                                                    lineNumber: 138,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \" What was done to achieve the idea?\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                                                            lineNumber: 137,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                                                    lineNumber: 135,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2 p-2 border-gray-200 dark:border-gray-700 rounded-md border-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-green-500 rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                                                            lineNumber: 143,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    children: \"Result:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                                                                    lineNumber: 145,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \" What was the outcome of the action?\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                                                            lineNumber: 144,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                                                    lineNumber: 142,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2 p-2 border-gray-200 dark:border-gray-700 rounded-md border-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-purple-500 rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                                                            lineNumber: 150,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    children: \"Status:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                                                                    lineNumber: 152,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \" Current state\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                                                            lineNumber: 151,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                                                    lineNumber: 149,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                                            lineNumber: 128,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"pt-2 border-t border-gray-200 dark:border-gray-700\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-gray-600 dark:text-gray-400\",\n                                                children: \"Click cells to edit • Drag rows to reorder • Use + to add items\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                                                lineNumber: 157,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                                            lineNumber: 156,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                                    lineNumber: 126,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                                lineNumber: 125,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                        lineNumber: 105,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                    lineNumber: 104,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n            lineNumber: 37,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n        lineNumber: 36,\n        columnNumber: 5\n    }, this);\n}\n_s(ProjectDetailHeader, \"tPemVZUBvKAktCNdsiqoqorGMA8=\", false, function() {\n    return [\n        _hooks_useAnalytics__WEBPACK_IMPORTED_MODULE_4__.useAnalytics,\n        _components_ui_sidebar__WEBPACK_IMPORTED_MODULE_2__.useSidebar\n    ];\n});\n_c = ProjectDetailHeader;\nvar _c;\n$RefreshReg$(_c, \"ProjectDetailHeader\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/project/ProjectDetailHeader.tsx\n"));

/***/ })

});