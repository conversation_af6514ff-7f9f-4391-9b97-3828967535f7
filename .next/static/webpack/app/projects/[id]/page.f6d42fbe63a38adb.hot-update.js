"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/projects/[id]/page",{

/***/ "(app-pages-browser)/./src/components/business-item-hybrid-view.tsx":
/*!******************************************************!*\
  !*** ./src/components/business-item-hybrid-view.tsx ***!
  \******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BusinessItemHybridView: () => (/* binding */ BusinessItemHybridView)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_ui_tooltip__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/tooltip */ \"(app-pages-browser)/./src/components/ui/tooltip.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _business_item_table__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./business-item-table */ \"(app-pages-browser)/./src/components/business-item-table.tsx\");\n/* __next_internal_client_entry_do_not_use__ BusinessItemHybridView auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction BusinessItemHybridView(param) {\n    let { selectedItem, itemDetails, onBackToItems } = param;\n    _s();\n    const [isInfoExpanded, setIsInfoExpanded] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    // Get comprehensive data for the selected item\n    // Get dependent items (items that depend on this one)\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_1__.TooltipProvider, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold mb-4\",\n                        children: \"Action Items & Results\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                        lineNumber: 33,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_business_item_table__WEBPACK_IMPORTED_MODULE_3__.BusinessItemTable, {\n                        itemDetails: itemDetails,\n                        selectedBusinessItem: selectedItem,\n                        onBackToItems: onBackToItems\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                        lineNumber: 34,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n                lineNumber: 32,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n            lineNumber: 30,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx\",\n        lineNumber: 29,\n        columnNumber: 5\n    }, this);\n}\n_s(BusinessItemHybridView, \"xhHbMehHQ3yyT7gl75aBEcicsE0=\");\n_c = BusinessItemHybridView;\nvar _c;\n$RefreshReg$(_c, \"BusinessItemHybridView\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/business-item-hybrid-view.tsx\n"));

/***/ })

});