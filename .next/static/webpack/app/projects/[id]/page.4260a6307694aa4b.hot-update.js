"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/projects/[id]/page",{

/***/ "(app-pages-browser)/./src/components/project/ProjectDetailHeader.tsx":
/*!********************************************************!*\
  !*** ./src/components/project/ProjectDetailHeader.tsx ***!
  \********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ProjectDetailHeader: () => (/* binding */ ProjectDetailHeader)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_ui_popover__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/popover */ \"(app-pages-browser)/./src/components/ui/popover.tsx\");\n/* harmony import */ var _components_ui_sidebar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/sidebar */ \"(app-pages-browser)/./src/components/ui/sidebar.tsx\");\n/* harmony import */ var _components_ui_sidebar_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/sidebar-button */ \"(app-pages-browser)/./src/components/ui/sidebar-button.tsx\");\n/* harmony import */ var _hooks_useAnalytics__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/useAnalytics */ \"(app-pages-browser)/./src/hooks/useAnalytics.ts\");\n/* harmony import */ var _lib_constants__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/constants */ \"(app-pages-browser)/./src/lib/constants.ts\");\n/* harmony import */ var _mockdata_businessItemQuestions__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/mockdata/businessItemQuestions */ \"(app-pages-browser)/./src/mockdata/businessItemQuestions.ts\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_HelpCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,HelpCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_HelpCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,HelpCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-question-mark.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_7__);\n/* __next_internal_client_entry_do_not_use__ ProjectDetailHeader auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction ProjectDetailHeader(param) {\n    let { selectedBusinessItem, onBackToItems } = param;\n    _s();\n    const { trackClick, trackCustomEvent } = (0,_hooks_useAnalytics__WEBPACK_IMPORTED_MODULE_4__.useAnalytics)();\n    const [isHelpOpen, setIsHelpOpen] = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(false);\n    const { state, isMobile } = (0,_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_2__.useSidebar)();\n    // Get dynamic question and answer based on business item\n    const { question, answer } = (0,_mockdata_businessItemQuestions__WEBPACK_IMPORTED_MODULE_6__.getBusinessItemQA)(selectedBusinessItem);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"flex h-20 shrink-0 items-center gap-2 transition-[width] ease-linear border-b border-border\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-between w-full h-full px-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-4 h-full\",\n                    children: [\n                        isMobile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar_button__WEBPACK_IMPORTED_MODULE_3__.SidebarButton, {\n                            onClick: ()=>{\n                                trackClick(\"back-to-items\", \"project-detail-header\");\n                                trackCustomEvent(\"navigation_clicked\", {\n                                    destination: \"items\",\n                                    from_page: \"item-detail\",\n                                    location: \"header\"\n                                });\n                                onBackToItems();\n                            },\n                            icon: _barrel_optimize_names_ArrowLeft_HelpCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n                            variant: \"ghost\",\n                            size: \"lg\",\n                            layout: \"icon-only\",\n                            showBorder: true,\n                            hoverColor: \"grey\",\n                            hoverScale: true,\n                            iconClassName: _lib_constants__WEBPACK_IMPORTED_MODULE_5__.ICON_SIZES.lg\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                            lineNumber: 40,\n                            columnNumber: 13\n                        }, this),\n                        state === \"collapsed\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-xl font-semibold text-gray-900 dark:text-gray-100\",\n                            children: (selectedBusinessItem === null || selectedBusinessItem === void 0 ? void 0 : selectedBusinessItem.title) || \"Untitled Item\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                            lineNumber: 61,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                    lineNumber: 38,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 flex flex-col justify-center mr-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm font-bold text-[var(--brand-dark)] dark:text-[var(--primary)] mb-2\",\n                            children: question\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                            lineNumber: 70,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-gray-600 dark:text-gray-400 leading-relaxed\",\n                            children: answer\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                            lineNumber: 74,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                    lineNumber: 68,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-2 h-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_1__.Popover, {\n                        open: isHelpOpen,\n                        onOpenChange: setIsHelpOpen,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_1__.PopoverTrigger, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar_button__WEBPACK_IMPORTED_MODULE_3__.SidebarButton, {\n                                    icon: _barrel_optimize_names_ArrowLeft_HelpCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n                                    variant: \"ghost\",\n                                    size: \"lg\",\n                                    layout: \"icon-only\",\n                                    showBorder: true,\n                                    hoverColor: \"grey\",\n                                    hoverScale: true,\n                                    onClick: ()=>{\n                                        trackClick(\"help-button\", \"project-header\");\n                                        trackCustomEvent(\"help_clicked\", {\n                                            from_item: selectedBusinessItem === null || selectedBusinessItem === void 0 ? void 0 : selectedBusinessItem.title,\n                                            location: \"header\"\n                                        });\n                                    },\n                                    iconClassName: _lib_constants__WEBPACK_IMPORTED_MODULE_5__.ICON_SIZES.lg\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                                    lineNumber: 83,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                                lineNumber: 82,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_1__.PopoverContent, {\n                                align: \"end\",\n                                side: \"bottom\",\n                                className: \"w-80\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold text-sm\",\n                                            children: \"Table Guide\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                                            lineNumber: 103,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2 text-xs\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2 p-2 border-gray-200 dark:border-gray-700 rounded-md border-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-blue-500 rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                                                            lineNumber: 106,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    children: \"Idea:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                                                                    lineNumber: 108,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \" What is the main idea of the item?\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                                                            lineNumber: 107,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                                                    lineNumber: 105,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2 p-2 border-gray-200 dark:border-gray-700 rounded-md border-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-yellow-500 rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                                                            lineNumber: 112,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    children: \"Action:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                                                                    lineNumber: 114,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \" What was done to achieve the idea?\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                                                            lineNumber: 113,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                                                    lineNumber: 111,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2 p-2 border-gray-200 dark:border-gray-700 rounded-md border-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-green-500 rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                                                            lineNumber: 119,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    children: \"Result:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                                                                    lineNumber: 121,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \" What was the outcome of the action?\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                                                            lineNumber: 120,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                                                    lineNumber: 118,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2 p-2 border-gray-200 dark:border-gray-700 rounded-md border-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-purple-500 rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                                                            lineNumber: 126,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    children: \"Status:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                                                                    lineNumber: 128,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \" Current state\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                                                            lineNumber: 127,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                                                    lineNumber: 125,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                                            lineNumber: 104,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"pt-2 border-t border-gray-200 dark:border-gray-700\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-gray-600 dark:text-gray-400\",\n                                                children: \"Click cells to edit • Drag rows to reorder • Use + to add items\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                                                lineNumber: 133,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                                            lineNumber: 132,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                                    lineNumber: 102,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                                lineNumber: 101,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                        lineNumber: 81,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n                    lineNumber: 80,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n            lineNumber: 36,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx\",\n        lineNumber: 35,\n        columnNumber: 5\n    }, this);\n}\n_s(ProjectDetailHeader, \"K+RJ3QdmS4gz6YuJwR8lWY9rWGg=\", false, function() {\n    return [\n        _hooks_useAnalytics__WEBPACK_IMPORTED_MODULE_4__.useAnalytics,\n        _components_ui_sidebar__WEBPACK_IMPORTED_MODULE_2__.useSidebar\n    ];\n});\n_c = ProjectDetailHeader;\nvar _c;\n$RefreshReg$(_c, \"ProjectDetailHeader\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/project/ProjectDetailHeader.tsx\n"));

/***/ })

});