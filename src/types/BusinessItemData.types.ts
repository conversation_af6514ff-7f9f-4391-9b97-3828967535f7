// Business item data types

export interface BusinessItemData {
  id: string;
  title: string;
  description: string;
  question: string;
  guidance: string;
  category: "Market" | "Solution" | "Sales & Marketing" | "Company";
  order: number;
  inputType: "Manual" | "Auto" | "Suggest";
  dependencies: string[];
  status: "idea" | "action" | "confirmed" | "unproven";
  values: string[];
  actions: number;
  ideas: number;
  results: number;
}
